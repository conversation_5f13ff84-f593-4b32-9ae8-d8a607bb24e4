package seoworkflow

import (
	"context"
	"testing"
	"time"

	"github.com/golang/mock/gomock"
	"github.com/stretchr/testify/mock"
	"github.com/stretchr/testify/suite"
	"go.temporal.io/sdk/testsuite"
	"go.temporal.io/sdk/worker"

	"github.com/stretchr/testify/assert"
	"github.com/vendasta/listing-products/internal/dataforseo"
)

type AIOAuditTestSuite struct {
	suite.Suite
	testsuite.WorkflowTestSuite

	workflowEnv *testsuite.TestWorkflowEnvironment
	activityEnv *testsuite.TestActivityEnvironment

	ctrl                 *gomock.Controller
	mockDataForSEOClient *dataforseo.MockSERPClient
}

func TestAIOAuditTestSuite(t *testing.T) {
	suite.Run(t, &AIOAuditTestSuite{})
}

func (s *AIOAuditTestSuite) SetupTest() {
	s.ctrl = gomock.NewController(s.T())

	// Create mocks
	s.mockDataForSEOClient = dataforseo.NewMockSERPClient(s.ctrl)

	// For testing workflow functions
	s.workflowEnv = s.NewTestWorkflowEnvironment()
	s.workflowEnv.RegisterWorkflow(AIOAuditWorkflow)
	s.workflowEnv.RegisterActivity(buildAuditRequestActivity)
	s.workflowEnv.RegisterActivity(startSiteCrawlActivity)
	s.workflowEnv.RegisterActivity(checkCrawlStatusActivity)
	s.workflowEnv.RegisterActivity(getCrawlResultsActivity)
	s.workflowEnv.RegisterActivity(generateAiInsightsActivity)

	// Set up activity context for workflow tests
	ctx := NewActivityContext(context.Background(), nil, nil, nil, nil, s.mockDataForSEOClient, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil, nil)
	s.workflowEnv.SetWorkerOptions(worker.Options{
		BackgroundActivityContext: ctx,
		DeadlockDetectionTimeout:  time.Second * 30,
	})

	// For testing activity functions
	s.activityEnv = s.NewTestActivityEnvironment()
	s.activityEnv.RegisterActivity(buildAuditRequestActivity)
	s.activityEnv.RegisterActivity(startSiteCrawlActivity)
	s.activityEnv.RegisterActivity(checkCrawlStatusActivity)
	s.activityEnv.RegisterActivity(getCrawlResultsActivity)
	s.activityEnv.RegisterActivity(generateAiInsightsActivity)

	// Set up activity context for activity tests
	s.activityEnv.SetWorkerOptions(worker.Options{
		BackgroundActivityContext: ctx,
		DeadlockDetectionTimeout:  time.Second * 30,
	})
}

func (s *AIOAuditTestSuite) TestAIOAuditWorkflow_ReturnsNilWhenNoErrorsOccur() {
	defer s.ctrl.Finish()

	buildAuditRequestParam := &buildAuditRequestParams{
		BusinessID:   "test-business-id",
		Website:      "https://example.com",
		BusinessName: "Test Business",
	}
	buildAuditResponse := &buildAuditRequestResponse{
		Request: &dataforseo.OnPageTaskPostRequest{
			Target:                  "https://example.com",
			MaxCrawlPages:           2,
			CrawlMode:               "recursive",
			LoadResources:           true,
			StoreRawHTML:            true,
			EnableAIContentAnalysis: true,
			EnableJavascript:        false,
			Tag:                     "test-business-id::aio_audit",
			CustomUserAgent:         "GPTBot/1.0 (+https://openai.com/gptbot)",
		},
	}
	s.workflowEnv.OnActivity(buildAuditRequestActivity, mock.Anything, buildAuditRequestParam).
		Return(buildAuditResponse, nil)

	startSiteCrawlParam := &startSiteCrawlParams{
		BusinessID:   "test-business-id",
		Website:      "https://example.com",
		BusinessName: "Test Business",
		Request:      buildAuditResponse.Request,
	}
	startSiteCrawlResponse := &startSiteCrawlResponse{
		TaskID: "test-task-id",
	}
	s.workflowEnv.OnActivity(startSiteCrawlActivity, mock.Anything, startSiteCrawlParam).
		Return(startSiteCrawlResponse, nil)

	checkCrawlStatusParam := &checkCrawlStatusParams{
		BusinessID:   "test-business-id",
		Website:      "https://example.com",
		BusinessName: "Test Business",
		TaskID:       "test-task-id",
	}
	s.workflowEnv.OnActivity(checkCrawlStatusActivity, mock.Anything, checkCrawlStatusParam).
		Return(nil)

	getCrawlResultsParam := &getCrawlResultsParams{
		BusinessID:   "test-business-id",
		Website:      "https://example.com",
		BusinessName: "Test Business",
		TaskID:       "test-task-id",
	}
	getCrawlResultsResp := &getCrawlResultsResponse{
		Summary: &dataforseo.OnPageSummaryResponse{},
		Pages:   &dataforseo.OnPagePagesResponse{},
		URLs:    []string{"https://www.example.com/", "https://www.example.com/about"},
	}
	s.workflowEnv.OnActivity(getCrawlResultsActivity, mock.Anything, getCrawlResultsParam).
		Return(getCrawlResultsResp, nil)

	generateAiInsightsParam := &generateAiInsightsParams{
		BusinessID:   "test-business-id",
		Website:      "https://example.com",
		BusinessName: "Test Business",
		TaskID:       "test-task-id",
		URLs:         []string{"https://www.example.com/", "https://www.example.com/about"},
		Summary:      &dataforseo.OnPageSummaryResponse{},
		Pages:        &dataforseo.OnPagePagesResponse{},
	}
	generateAiInsightsResp := &generateAiInsightsResponse{
		Summary: "Processed 2 URLs successfully, total HTML size: 87 characters",
	}
	s.workflowEnv.OnActivity(generateAiInsightsActivity, mock.Anything, generateAiInsightsParam).
		Return(generateAiInsightsResp, nil)

	params := &AIOAuditWorkflowParams{
		BusinessID:   "test-business-id",
		Website:      "https://example.com",
		BusinessName: "Test Business",
	}
	s.workflowEnv.ExecuteWorkflow(AIOAuditWorkflow, params)

	s.NoError(s.workflowEnv.GetWorkflowError())
	s.workflowEnv.AssertExpectations(s.T())
}

func (s *AIOAuditTestSuite) TestBuildAuditRequestActivity_ReturnsSuccess() {
	defer s.ctrl.Finish()

	params := &buildAuditRequestParams{
		BusinessID:   "test-business-id",
		Website:      "https://example.com",
		BusinessName: "Test Business",
	}
	runner, err := s.activityEnv.ExecuteActivity(buildAuditRequestActivity, params)
	s.Assert().NoError(err)

	// Since the activity returns a response struct, we should be able to get the value
	s.Assert().True(runner.HasValue())

	var response *buildAuditRequestResponse
	err = runner.Get(&response)
	s.Assert().NoError(err)
	s.Assert().NotNil(response)
	s.Assert().NotNil(response.Request)
	s.Assert().Equal("https://example.com", response.Request.Target)
	s.Assert().EqualValues(2, response.Request.MaxCrawlPages)
	s.Assert().Equal("recursive", response.Request.CrawlMode)
	s.Assert().True(response.Request.LoadResources)
	s.Assert().True(response.Request.StoreRawHTML)
	s.Assert().True(response.Request.EnableAIContentAnalysis)
	s.Assert().False(response.Request.EnableJavascript)
	s.Assert().Equal("test-business-id::aio_audit", response.Request.Tag)
	s.Assert().Equal("GPTBot/1.0 (+https://openai.com/gptbot)", response.Request.CustomUserAgent)
}

func (s *AIOAuditTestSuite) TestStartSiteCrawlActivity_ReturnsErrorOnTaskFailure() {
	defer s.ctrl.Finish()

	params := &startSiteCrawlParams{
		BusinessID:   "test-business-id",
		Website:      "https://example.com",
		BusinessName: "Test Business",
		Request: &dataforseo.OnPageTaskPostRequest{
			Target:                  "https://example.com",
			MaxCrawlPages:           2,
			CrawlMode:               "recursive",
			LoadResources:           true,
			StoreRawHTML:            true,
			EnableAIContentAnalysis: true,
			EnableJavascript:        false,
			Tag:                     "test-business-id::aio_audit",
			CustomUserAgent:         "GPTBot/1.0 (+https://openai.com/gptbot)",
		},
	}

	// Mock the DataForSEO client call to return an error
	s.mockDataForSEOClient.EXPECT().
		OnPageTaskPost(gomock.Any(), gomock.Any(), gomock.Any()).
		Return(nil, assert.AnError)

	_, err := s.activityEnv.ExecuteActivity(startSiteCrawlActivity, params)
	s.Assert().Error(err)
}

func (s *AIOAuditTestSuite) TestStartSiteCrawlActivity_ReturnsErrorWhenNoTasksReturned() {
	defer s.ctrl.Finish()

	params := &startSiteCrawlParams{
		BusinessID:   "test-business-id",
		Website:      "https://example.com",
		BusinessName: "Test Business",
		Request: &dataforseo.OnPageTaskPostRequest{
			Target:                  "https://example.com",
			MaxCrawlPages:           2,
			CrawlMode:               "recursive",
			LoadResources:           true,
			StoreRawHTML:            true,
			EnableAIContentAnalysis: true,
			EnableJavascript:        false,
			Tag:                     "test-business-id::aio_audit",
			CustomUserAgent:         "GPTBot/1.0 (+https://openai.com/gptbot)",
		},
	}

	// Mock the DataForSEO client call to return empty tasks
	response := &dataforseo.OnPageResponse{
		Tasks: []*dataforseo.OnPageTask{},
	}
	s.mockDataForSEOClient.EXPECT().
		OnPageTaskPost(gomock.Any(), gomock.Any(), gomock.Any()).
		Return(response, nil)

	_, err := s.activityEnv.ExecuteActivity(startSiteCrawlActivity, params)
	s.Assert().Error(err)
}

func (s *AIOAuditTestSuite) TestStartSiteCrawlActivity_ReturnsSuccessWhenNoErrorsOccur() {
	defer s.ctrl.Finish()

	params := &startSiteCrawlParams{
		BusinessID:   "test-business-id",
		Website:      "https://example.com",
		BusinessName: "Test Business",
		Request: &dataforseo.OnPageTaskPostRequest{
			Target:                  "https://example.com",
			MaxCrawlPages:           2,
			CrawlMode:               "recursive",
			LoadResources:           true,
			StoreRawHTML:            true,
			EnableAIContentAnalysis: true,
			EnableJavascript:        false,
			Tag:                     "test-business-id::aio_audit",
			CustomUserAgent:         "GPTBot/1.0 (+https://openai.com/gptbot)",
		},
	}

	// Mock the DataForSEO client call to return success
	task := &dataforseo.OnPageTask{
		ID:         "test-task-id",
		StatusCode: 20000, // Success status code
	}
	response := &dataforseo.OnPageResponse{
		Tasks: []*dataforseo.OnPageTask{task},
	}
	s.mockDataForSEOClient.EXPECT().
		OnPageTaskPost(gomock.Any(), gomock.Any(), gomock.Any()).
		Return(response, nil)

	runner, err := s.activityEnv.ExecuteActivity(startSiteCrawlActivity, params)
	s.Assert().NoError(err)

	var result *startSiteCrawlResponse
	err = runner.Get(&result)
	s.Assert().NoError(err)
	s.Assert().NotNil(result)
	s.Assert().Equal("test-task-id", result.TaskID)
}

func (s *AIOAuditTestSuite) TestCheckCrawlStatusActivity_ReturnsNilWhenNoErrorsOccur() {
	defer s.ctrl.Finish()

	params := &checkCrawlStatusParams{
		BusinessID:   "test-business-id",
		Website:      "https://example.com",
		BusinessName: "Test Business",
		TaskID:       "test-task-id",
	}

	// Mock the DataForSEO client call to return success with our task ID in the results
	taskResult := &dataforseo.OnPageTaskResult{
		ID:     "test-task-id",
		Target: "https://example.com",
		Tag:    "test-business-id::aio_audit",
	}
	task := &dataforseo.OnPageTask{
		StatusCode: 20000, // Success status code
		Result:     []*dataforseo.OnPageTaskResult{taskResult},
	}
	response := &dataforseo.OnPageTasksReadyResponse{
		Tasks: []*dataforseo.OnPageTask{task},
	}
	s.mockDataForSEOClient.EXPECT().
		OnPageTasksReady(gomock.Any(), gomock.Any()).
		Return(response, nil)

	_, err := s.activityEnv.ExecuteActivity(checkCrawlStatusActivity, params)
	s.Assert().NoError(err)
}

func (s *AIOAuditTestSuite) TestGetCrawlResultsActivity_ReturnsNilWhenNoErrorsOccur() {
	defer s.ctrl.Finish()

	params := &getCrawlResultsParams{
		BusinessID:   "test-business-id",
		Website:      "https://example.com",
		BusinessName: "Test Business",
		TaskID:       "test-task-id",
	}

	// Mock the DataForSEO client call to return success for summary
	summaryTask := &dataforseo.OnPageSummaryTask{
		ID:         "test-task-id",
		StatusCode: 20000, // Success status code
		Result: []*dataforseo.OnPageSummaryResult{
			{
				CrawlProgress: "finished",
				CrawlStatus: &dataforseo.OnPageCrawlStatus{
					PagesCrawled: 2,
				},
				DomainInfo: &dataforseo.OnPageDomainInfo{
					Name: "www.example.com",
				},
				PageMetrics: &dataforseo.OnPagePageMetrics{
					OnpageScore: 94.33,
				},
			},
		},
	}
	summaryResponse := &dataforseo.OnPageSummaryResponse{
		Tasks: []*dataforseo.OnPageSummaryTask{summaryTask},
	}
	s.mockDataForSEOClient.EXPECT().
		OnPageSummary(gomock.Any(), "test-task-id", gomock.Any()).
		Return(summaryResponse, nil)

	// Mock the DataForSEO client call to return success for pages
	pagesTask := &dataforseo.OnPagePagesTask{
		ID:         "test-task-id",
		StatusCode: 20000, // Success status code
		Result: []*dataforseo.OnPagePagesResult{
			{
				CrawlProgress: "finished",
				CrawlStatus: &dataforseo.OnPageCrawlStatus{
					PagesCrawled: 2,
				},
				Items: []*dataforseo.OnPagePageItem{
					{
						URL: "https://www.example.com/",
						Meta: &dataforseo.OnPagePageMeta{
							Title: "Example Page",
						},
					},
					{
						URL: "https://www.example.com/about",
						Meta: &dataforseo.OnPagePageMeta{
							Title: "About Page",
						},
					},
				},
			},
		},
	}
	pagesResponse := &dataforseo.OnPagePagesResponse{
		Tasks: []*dataforseo.OnPagePagesTask{pagesTask},
	}
	s.mockDataForSEOClient.EXPECT().
		OnPagePages(gomock.Any(), gomock.Any(), gomock.Any()).
		Return(pagesResponse, nil)

	runner, err := s.activityEnv.ExecuteActivity(getCrawlResultsActivity, params)
	s.Assert().NoError(err)

	var result *getCrawlResultsResponse
	err = runner.Get(&result)
	s.Assert().NoError(err)
	s.Assert().NotNil(result)
	s.Assert().NotNil(result.Summary)
	s.Assert().Equal(summaryResponse, result.Summary)
	s.Assert().NotNil(result.Pages)
	s.Assert().Equal(pagesResponse, result.Pages)
	s.Assert().NotNil(result.URLs)
	s.Assert().Len(result.URLs, 2)
	s.Assert().Contains(result.URLs, "https://www.example.com/")
	s.Assert().Contains(result.URLs, "https://www.example.com/about")
}

func (s *AIOAuditTestSuite) TestGenerateAiInsightsActivity_ReturnsNilWhenNoErrorsOccur() {
	defer s.ctrl.Finish()

	params := &generateAiInsightsParams{
		BusinessID:   "test-business-id",
		Website:      "https://example.com",
		BusinessName: "Test Business",
		TaskID:       "test-task-id",
		URLs:         []string{"https://www.example.com/", "https://www.example.com/about"},
	}

	// Mock the DataForSEO client calls for OnPageRawHTML (called twice, once for each URL)
	rawHTMLTask1 := &dataforseo.OnPageRawHTMLTask{
		ID:         "test-request-id-1",
		StatusCode: 20000, // Success status code
		Result: []*dataforseo.OnPageRawHTMLResult{
			{
				CrawlProgress: "finished",
				Items: &dataforseo.OnPageRawHTMLItems{
					HTML: "<html><body>Home page content</body></html>",
				},
			},
		},
	}
	rawHTMLResponse1 := &dataforseo.OnPageRawHTMLResponse{
		Tasks: []*dataforseo.OnPageRawHTMLTask{rawHTMLTask1},
	}

	rawHTMLTask2 := &dataforseo.OnPageRawHTMLTask{
		ID:         "test-request-id-2",
		StatusCode: 20000, // Success status code
		Result: []*dataforseo.OnPageRawHTMLResult{
			{
				CrawlProgress: "finished",
				Items: &dataforseo.OnPageRawHTMLItems{
					HTML: "<html><body>About page content</body></html>",
				},
			},
		},
	}
	rawHTMLResponse2 := &dataforseo.OnPageRawHTMLResponse{
		Tasks: []*dataforseo.OnPageRawHTMLTask{rawHTMLTask2},
	}

	// Expect two calls to OnPageRawHTML (one for each URL)
	s.mockDataForSEOClient.EXPECT().
		OnPageRawHTML(gomock.Any(), gomock.Any(), gomock.Any()).
		Return(rawHTMLResponse1, nil).Times(1)

	s.mockDataForSEOClient.EXPECT().
		OnPageRawHTML(gomock.Any(), gomock.Any(), gomock.Any()).
		Return(rawHTMLResponse2, nil).Times(1)

	runner, err := s.activityEnv.ExecuteActivity(generateAiInsightsActivity, params)
	s.Assert().NoError(err)

	var result *generateAiInsightsResponse
	err = runner.Get(&result)
	s.Assert().NoError(err)
	s.Assert().NotNil(result)
	s.Assert().Contains(result.Summary, "Processed 2 URLs successfully")
	s.Assert().Contains(result.Summary, "total HTML size:")
}
