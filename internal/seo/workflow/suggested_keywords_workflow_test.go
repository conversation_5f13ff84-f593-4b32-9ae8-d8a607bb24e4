package seoworkflow

import (
	"context"
	"testing"
	"time"

	"github.com/golang/mock/gomock"
	"github.com/stretchr/testify/assert"
	"github.com/vendasta/generated-protos-go/vstorepb"
	"github.com/vendasta/listing-products/internal/dataforseo"
	listingprofilemodel "github.com/vendasta/listing-products/internal/listingprofile/model"
	listingprofileservice "github.com/vendasta/listing-products/internal/listingprofile/service"
	model "github.com/vendasta/listing-products/internal/seosuggestedkeywords"
	seosuggestedkeywordsservice "github.com/vendasta/listing-products/internal/seosuggestedkeywords/service"
	"go.temporal.io/sdk/testsuite"
	"go.temporal.io/sdk/worker"
)

type ExpectedKeywordForSitePost struct {
	Req  []*dataforseo.GoogleKeywordsForSiteRequest
	Resp *dataforseo.GoogleKeywordsForSiteResponse
}

type ExpectedKeywordForSiteGet struct {
	TaskID string
	Resp   *dataforseo.GoogleKeywordsForSiteResponse
}

type ExpectedTaskReady struct {
	Path    dataforseo.Path
	TaskIDs []string
	Ready   bool
}

func Test_SuggestedKeywordsWorkflow(t *testing.T) {
	type testCase struct {
		name                      string
		workflowParams            *SuggestedKeywordsWorkflowParams
		listingProfile            *listingprofilemodel.ListingProfile
		dataForSEOPost            *ExpectedKeywordForSitePost
		dataForSEOGets            []*ExpectedKeywordForSiteGet
		expectedSuggestedKeywords *model.SEOSuggestedKeywords
		expectedResponse          interface{}
		expectedWorkflowErr       error
	}
	cases := []*testCase{
		{
			name: "Test retries when results are not ready yet",
			workflowParams: &SuggestedKeywordsWorkflowParams{
				BusinessID: "AG-1",
				Website:    "www.website.com",
				Date:       "2020-01-01",
				LocationID: 100,
			},
			listingProfile: &listingprofilemodel.ListingProfile{
				BusinessID: "AG-1",
				Website:    "www.website.com",
				Location:   &vstorepb.GeoPoint{Latitude: 10, Longitude: 20},
			},
			dataForSEOPost: &ExpectedKeywordForSitePost{
				Req: []*dataforseo.GoogleKeywordsForSiteRequest{
					{
						BusinessID:   "AG-1",
						Target:       "www.website.com",
						LocationCode: 100,
						Tag:          "AG-1::VICINITY_CITY",
					},
				},
				Resp: &dataforseo.GoogleKeywordsForSiteResponse{
					Version:       "",
					StatusCode:    0,
					StatusMessage: "",
					Time:          "",
					Cost:          0,
					TasksCount:    0,
					TasksError:    0,
					Tasks: []*dataforseo.GoogleKeywordsForSiteTask{
						{
							ID:            "id1",
							StatusCode:    20100,
							StatusMessage: "",
						},
					},
				},
			},
			dataForSEOGets: []*ExpectedKeywordForSiteGet{
				{
					TaskID: "id1",
					Resp: &dataforseo.GoogleKeywordsForSiteResponse{
						Version:       "",
						StatusCode:    0,
						StatusMessage: "",
						Time:          "",
						Cost:          0,
						TasksCount:    0,
						TasksError:    0,
						Tasks: []*dataforseo.GoogleKeywordsForSiteTask{
							{
								ID:            "id1",
								StatusCode:    dataforseo.ErrorTaskInQueue,
								StatusMessage: "",
								Result:        []*dataforseo.GoogleKeywordsForSiteResult{},
							},
						},
					},
				},
				{
					TaskID: "id1",
					Resp: &dataforseo.GoogleKeywordsForSiteResponse{
						Version:       "",
						StatusCode:    0,
						StatusMessage: "",
						Time:          "",
						Cost:          0,
						TasksCount:    0,
						TasksError:    0,
						Tasks: []*dataforseo.GoogleKeywordsForSiteTask{
							{
								ID:            "id1",
								StatusCode:    20100,
								StatusMessage: "",
								Result: []*dataforseo.GoogleKeywordsForSiteResult{
									{
										Keyword:          "Keyword1",
										Competition:      "LOW",
										CompetitionIndex: 20,
										SearchVolume:     500,
										LowTopOfPageBid:  2.22,
										HighTopOfPageBid: 6.66,
									},
									{
										Keyword:          "Keyword2",
										Competition:      "HIGH",
										CompetitionIndex: 90,
										SearchVolume:     800,
										LowTopOfPageBid:  6.66,
										HighTopOfPageBid: 12.12,
									},
								},
							},
						},
					},
				},
			},
			expectedSuggestedKeywords: &model.SEOSuggestedKeywords{
				BusinessID: "AG-1",
				SuggestedKeywords: []*model.KeywordInfo{
					{
						Keyword:          "Keyword1",
						Competition:      "LOW",
						CompetitionIndex: 20,
						SearchVolume:     500,
						LowTopOfPageBid:  2.22,
						HighTopOfPageBid: 6.66,
					},
					{
						Keyword:          "Keyword2",
						Competition:      "HIGH",
						CompetitionIndex: 90,
						SearchVolume:     800,
						LowTopOfPageBid:  6.66,
						HighTopOfPageBid: 12.12,
					},
				},
				Created: time.Time{},
				Updated: time.Time{},
				Deleted: time.Time{},
			},
			expectedResponse:    nil,
			expectedWorkflowErr: nil,
		},
	}

	for _, c := range cases {
		t.Run(c.name, func(t *testing.T) {
			ctx := context.Background()
			ctrl := gomock.NewController(t)
			mockLpService := listingprofileservice.NewMockInterface(ctrl)

			mockLpService.EXPECT().Get(gomock.Any(), c.listingProfile.BusinessID, false).Return(c.listingProfile, nil).AnyTimes()

			mockDataForSEO := dataforseo.NewMockSERPClient(ctrl)

			mockDataForSEO.EXPECT().GoogleKeywordsForSiteTaskPost(gomock.Any(), c.workflowParams.Date, c.dataForSEOPost.Req, gomock.Any()).Return(c.dataForSEOPost.Resp, nil).Times(1)
			for _, get := range c.dataForSEOGets {
				mockDataForSEO.EXPECT().GoogleKeywordsForSiteTaskGet(gomock.Any(), c.workflowParams.BusinessID, c.workflowParams.Date, get.TaskID).Return(get.Resp, nil).Times(1)
			}

			mockSuggestedKeywords := seosuggestedkeywordsservice.NewMockService(ctrl)

			mockSuggestedKeywords.EXPECT().Upsert(gomock.Any(), c.expectedSuggestedKeywords).Return(nil).Times(1)

			testSuite := &testsuite.WorkflowTestSuite{}
			env := testSuite.NewTestWorkflowEnvironment()
			env.RegisterWorkflow(SuggestedKeywordsWorkflow)
			env.RegisterActivity(buildSuggestedKeywordsRequest)
			env.RegisterActivity(postSuggestedKeywordsRequest)
			env.RegisterActivity(getAndStoreSuggestedKeywords)
			ctx = NewActivityContext(ctx, nil, mockLpService, nil, nil, mockDataForSEO, nil, nil, nil, nil, nil, mockSuggestedKeywords, nil, nil, nil, nil, nil, nil, nil, nil, nil)

			env.SetWorkerOptions(worker.Options{
				BackgroundActivityContext: ctx,
			})

			env.ExecuteWorkflow(SuggestedKeywordsWorkflow, c.workflowParams)

			if !env.IsWorkflowCompleted() {
				t.Errorf("Workflow failed to complete. Error (%s)", env.GetWorkflowError())
			}

			assert.Equal(t, c.expectedWorkflowErr, env.GetWorkflowError())
		})
	}
}

func Test_translateKeywordInfoFromDataForSEOResponse(t *testing.T) {
	type testCase struct {
		name     string
		input    []*dataforseo.GoogleKeywordsForSiteResult
		expected []*model.KeywordInfo
	}
	cases := []*testCase{
		{
			name: "should return keywords from dataforseo response",
			input: []*dataforseo.GoogleKeywordsForSiteResult{
				{
					Keyword:          "Keyword1",
					Competition:      "LOW",
					CompetitionIndex: 20,
					SearchVolume:     500,
					LowTopOfPageBid:  2.22,
					HighTopOfPageBid: 6.66,
				},
				{
					Keyword:          "Keyword2",
					Competition:      "HIGH",
					CompetitionIndex: 90,
					SearchVolume:     800,
					LowTopOfPageBid:  6.66,
					HighTopOfPageBid: 12.12,
				},
			},
			expected: []*model.KeywordInfo{
				{
					Keyword:          "Keyword1",
					Competition:      "LOW",
					CompetitionIndex: 20,
					SearchVolume:     500,
					LowTopOfPageBid:  2.22,
					HighTopOfPageBid: 6.66,
				},
				{
					Keyword:          "Keyword2",
					Competition:      "HIGH",
					CompetitionIndex: 90,
					SearchVolume:     800,
					LowTopOfPageBid:  6.66,
					HighTopOfPageBid: 12.12,
				},
			},
		},
		{
			name: "should filter keywords that have duplicate words from dataforseo response",
			input: []*dataforseo.GoogleKeywordsForSiteResult{
				{
					Keyword:          "experience the experience",
					Competition:      "LOW",
					CompetitionIndex: 20,
					SearchVolume:     500,
					LowTopOfPageBid:  2.22,
					HighTopOfPageBid: 6.66,
				},
				{
					Keyword:          "Keyword2",
					Competition:      "HIGH",
					CompetitionIndex: 90,
					SearchVolume:     800,
					LowTopOfPageBid:  6.66,
					HighTopOfPageBid: 12.12,
				},
			},
			expected: []*model.KeywordInfo{
				{
					Keyword:          "Keyword2",
					Competition:      "HIGH",
					CompetitionIndex: 90,
					SearchVolume:     800,
					LowTopOfPageBid:  6.66,
					HighTopOfPageBid: 12.12,
				},
			},
		},
	}

	for _, c := range cases {
		t.Run(c.name, func(t *testing.T) {
			actual := translateKeywordInfoFromDataForSEOResponse(c.input)
			assert.Equal(t, c.expected, actual)
		})
	}
}
