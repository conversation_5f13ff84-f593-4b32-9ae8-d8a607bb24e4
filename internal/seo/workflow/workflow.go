package seoworkflow

import (
	"context"
	"errors"
	"fmt"
	"time"

	"github.com/vendasta/gosdks/openai"
	"github.com/vendasta/gosdks/statsd"
	aioauditresultservice "github.com/vendasta/listing-products/internal/seo/aioauditresult/service"
	"github.com/vendasta/listing-products/internal/seo/workflow/seofailedworkflowinfo"

	cssdk "github.com/vendasta/CS/sdks/go/v1"
	accounts "github.com/vendasta/accounts/sdks/go/v2"
	address "github.com/vendasta/address/sdks/go"
	advertising "github.com/vendasta/advertising/sdks/go/v2"
	category "github.com/vendasta/category/sdks/go/v1"
	"github.com/vendasta/gosdks/config"
	vendastatemporal "github.com/vendasta/gosdks/temporal"
	lpBq "github.com/vendasta/listing-products/internal/common/bigquery"
	lpslack "github.com/vendasta/listing-products/internal/common/gchat"
	"github.com/vendasta/listing-products/internal/constants"
	dataforseo "github.com/vendasta/listing-products/internal/dataforseo"
	dataforseocategoriesservice "github.com/vendasta/listing-products/internal/dataforseocategories/service"
	"github.com/vendasta/listing-products/internal/gcs"
	listingprofileservice "github.com/vendasta/listing-products/internal/listingprofile/service"
	keywordinfoservice "github.com/vendasta/listing-products/internal/seo/keywordinfo/service"
	seodata "github.com/vendasta/listing-products/internal/seo/model"
	googleSeoKeywordInfo "github.com/vendasta/listing-products/internal/seo/seogooglekeywordinfo/service"
	seodataservice "github.com/vendasta/listing-products/internal/seo/service"
	seoFailedWorkflowInfo "github.com/vendasta/listing-products/internal/seo/workflow/seofailedworkflowinfo/service"
	seosettingsservice "github.com/vendasta/listing-products/internal/seosettings/service"
	seosuggestedkeywordsservice "github.com/vendasta/listing-products/internal/seosuggestedkeywords/service"
	suggestionsservice "github.com/vendasta/listing-products/internal/suggestions/service"
	snapshot "github.com/vendasta/snapshot/sdks/go"
	"go.temporal.io/api/enums/v1"
	"go.temporal.io/sdk/temporal"
	"go.temporal.io/sdk/workflow"
)

const (
	taskBatchSize       = 12
	defaultZoomLevel    = 13
	dateFMT             = "2006-01-02"
	maxTaskReadyRetries = 10
)

type StoreMapsParams struct {
	Keyword      string
	Entries      []*seodata.LocalSearchData
	WorkflowURL  string
	SearchRadius float64
	SERPParams   *SERPWorkflowParams
}

func SetupTemporalAndStartWorker(
	ctx context.Context,
	client *vendastatemporal.Temporal,
	LpBqClient *lpBq.BQWrapper,
	lpService listingprofileservice.Interface,
	cloudStorageService gcs.Interface,
	seoDataService seodataservice.Service,
	dataForSEOClient dataforseo.SERPClient,
	seoSettingsService seosettingsservice.Service,
	snapshotSeo snapshot.SeoSectionServiceClientInterface,
	nap address.NapDataServiceClientInterface,
	accountsClient accounts.AccountsServiceClientInterface,
	gchat lpslack.GChatAlerter,
	seoSuggestedKeywordsService seosuggestedkeywordsservice.Service,
	dataforseoCategoryService dataforseocategoriesservice.Service,
	keywordInfo keywordinfoservice.Service,
	csGMBClient cssdk.GoogleMyBusinessClientInterface,
	categoryClient category.CategoriesClientInterface,
	suggestionService suggestionsservice.Service,
	advertisingClient advertising.AdwordsClientInterface,
	seogooglekeywordInfoService googleSeoKeywordInfo.Service,
	seofailedWorkflowInfoService seoFailedWorkflowInfo.Service,
	aioAuditResultService aioauditresultservice.Service,
	openAIClient openai.Client,
) error {
	worker, err := client.NewWorkerV2(NewActivityContext(ctx, LpBqClient, lpService, cloudStorageService, seoDataService, dataForSEOClient, seoSettingsService, snapshotSeo, nap, accountsClient, gchat, seoSuggestedKeywordsService, dataforseoCategoryService, keywordInfo, csGMBClient, categoryClient, suggestionService, advertisingClient, seogooglekeywordInfoService, seofailedWorkflowInfoService, openAIClient, aioAuditResultService),
		constants.SEOTaskList,
		vendastatemporal.WithTaskQueueActivitiesPerSecond(15),
		vendastatemporal.WithMaxConcurrentActivityExecutionSize(4),
	)
	if err != nil {
		return err
	}

	keywordWorker, err := client.NewWorkerV2(NewActivityContext(ctx, LpBqClient, lpService, cloudStorageService, seoDataService, dataForSEOClient, seoSettingsService, snapshotSeo, nap, accountsClient, gchat, seoSuggestedKeywordsService, dataforseoCategoryService, keywordInfo, csGMBClient, categoryClient, suggestionService, advertisingClient, seogooglekeywordInfoService, seofailedWorkflowInfoService, openAIClient, aioAuditResultService),
		constants.SEOTaskListKeyword,
		vendastatemporal.WithTaskQueueActivitiesPerSecond(10),
		vendastatemporal.WithMaxConcurrentActivityExecutionSize(3),
	)
	if err != nil {
		return err
	}

	aioauditWorker, err := client.NewWorkerV2(NewActivityContext(ctx, LpBqClient, lpService, cloudStorageService, seoDataService, dataForSEOClient, seoSettingsService, snapshotSeo, nap, accountsClient, gchat, seoSuggestedKeywordsService, dataforseoCategoryService, keywordInfo, csGMBClient, categoryClient, suggestionService, advertisingClient, seogooglekeywordInfoService, seofailedWorkflowInfoService, openAIClient, aioAuditResultService),
		constants.AIOAuditTaskList,
		vendastatemporal.WithTaskQueueActivitiesPerSecond(5),
		vendastatemporal.WithMaxConcurrentActivityExecutionSize(2),
	)
	if err != nil {
		return err
	}

	rateLimitedWorker, err := client.NewWorkerV2(NewActivityContext(ctx, LpBqClient, lpService, cloudStorageService, seoDataService, dataForSEOClient, seoSettingsService, snapshotSeo, nap, accountsClient, gchat, seoSuggestedKeywordsService, dataforseoCategoryService, keywordInfo, csGMBClient, categoryClient, suggestionService, advertisingClient, seogooglekeywordInfoService, seofailedWorkflowInfoService, openAIClient, aioAuditResultService),
		constants.SEOTaskListRateLimited, vendastatemporal.WithTaskQueueActivitiesPerSecond(1))

	worker.RegisterWorkflow(LocalSEOWorkflow)
	worker.RegisterWorkflow(SnapshotSEOWorkflow)
	worker.RegisterWorkflow(SuggestedKeywordsWorkflow)
	worker.RegisterWorkflow(KeywordInfoWorkflow)
	worker.RegisterWorkflow(CategoryWorkflow)
	worker.RegisterWorkflow(AIOAuditWorkflow)

	worker.RegisterActivity(buildRequests)
	worker.RegisterActivity(sortTasks)
	worker.RegisterActivity(fetchMapsResult)
	worker.RegisterActivity(storeMapsResults)
	worker.RegisterActivity(storeMapsResultsV1) // New version with searchRadius parameter
	worker.RegisterActivity(getAndStoreOrganicResult)
	worker.RegisterActivity(getSnapshotKeywords)
	worker.RegisterActivity(setKeywords)
	worker.RegisterActivity(sendAlert)
	worker.RegisterActivity(buildSuggestedKeywordsRequest)
	worker.RegisterActivity(getAndStoreSuggestedKeywords)
	worker.RegisterActivity(getGoogleDataLastUpdateDate)
	worker.RegisterActivity(buildKeywordInfoRequest)
	worker.RegisterActivity(getAndStoreKeywordInfo)
	worker.RegisterActivity(getAndStoreKeywordInfoUsingGoogleAPI)
	worker.RegisterActivity(getAndStoreKeywordInfoUsingGoogleAPIV2)
	worker.RegisterActivity(getSERPItemForCategoryTask)
	worker.RegisterActivity(storeDataForSEOCategory)
	worker.RegisterActivity(getExistingItemFromDataLake)
	worker.RegisterActivity(getConnectedGooglePlaceID)
	worker.RegisterActivity(storeFailedWorkflowInfo)

	rateLimitedWorker.RegisterActivity(processMapsRequests)
	rateLimitedWorker.RegisterActivity(processOrganicRequestBatch)
	rateLimitedWorker.RegisterActivity(postKeywordInfoRequest)
	rateLimitedWorker.RegisterActivity(postCategoryMapsRequest)
	rateLimitedWorker.RegisterActivity(postSuggestedKeywordsRequest)

	keywordWorker.RegisterWorkflow(KeywordInfoWorkflowV1)
	keywordWorker.RegisterActivity(fetchAndValidateLocationDataActivity)
	keywordWorker.RegisterActivity(getAndStoreKeywordInfoUsingGoogleAPIActivity)
	keywordWorker.RegisterActivity(storeFailedWorkflowInfo)

	aioauditWorker.RegisterWorkflow(AIOAuditWorkflow)
	aioauditWorker.RegisterActivity(buildAuditRequestActivity)
	aioauditWorker.RegisterActivity(startSiteCrawlActivity)
	aioauditWorker.RegisterActivity(checkCrawlStatusActivity)
	aioauditWorker.RegisterActivity(getCrawlResultsActivity)
	aioauditWorker.RegisterActivity(generateAiInsightsActivity)

	// TODO: Keep this registration for compatibility with old workflows now, delete later
	worker.RegisterWorkflow(KeywordInfoWorkflowV1)
	worker.RegisterActivity(getAndStoreKeywordInfoUsingGoogleAPIActivity)
	worker.RegisterActivity(fetchAndValidateLocationDataActivity)

	return nil
}

type SERPWorkflowParams struct {
	Date                  string
	BusinessID            string
	Keywords              []string
	Force                 bool // If true, will skip environment checks, and checks for previous results for businesses
	IgnoreDataLakeResults bool // If true, we will always fetch new results from DataForSEO, even if we have matching results in our DataForSEOTask table
	InitialSleepDuration  time.Duration
	SeoFailedInfoWorkFlow *seofailedworkflowinfo.SEOFailedWorkflowInfo
}

type KeywordSearchParams struct {
	BusinessID string
	Keywords   []string // Optional, if not included, we will use the Business's stored SEO keywords
}

const WorkflowURLFmt = "https://cloud.temporal.io/namespaces/listings-products-%s.iwgwg/workflows/%s/%s/history"

func getActivityCtx(ctx workflow.Context) workflow.Context {
	activityOptions := vendastatemporal.NewActivityOptions()
	activityOptions.RetryPolicy = &temporal.RetryPolicy{
		InitialInterval: time.Minute * 5,
		MaximumInterval: time.Hour,
		MaximumAttempts: 2,
	}
	return workflow.WithActivityOptions(ctx, activityOptions)
}

func getRetryCtx(ctx workflow.Context) workflow.Context {
	retryActivityOptions := vendastatemporal.NewActivityOptions()
	retryActivityOptions.RetryPolicy = &temporal.RetryPolicy{
		InitialInterval:    time.Minute * 5,
		MaximumInterval:    time.Hour,
		MaximumAttempts:    maxTaskReadyRetries,
		BackoffCoefficient: 2.0,
	}
	return workflow.WithActivityOptions(ctx, retryActivityOptions)
}

func getRateLimitCtx(ctx workflow.Context) workflow.Context {
	rateLimitActivityOptions := vendastatemporal.NewActivityOptions()
	rateLimitActivityOptions.TaskQueue = constants.SEOTaskListRateLimited
	rateLimitActivityOptions.RetryPolicy = &temporal.RetryPolicy{
		InitialInterval: time.Minute * 5,
		MaximumInterval: time.Hour,
		MaximumAttempts: 2,
	}
	return workflow.WithActivityOptions(ctx, rateLimitActivityOptions)
}

func LocalSEOWorkflow(ctx workflow.Context, params *SERPWorkflowParams) error {
	logger := workflow.GetLogger(ctx)

	activityCtx := getActivityCtx(ctx)
	retryContext := getRetryCtx(ctx)
	rateLimitContext := getRateLimitCtx(ctx)

	workflowID := workflow.GetInfo(ctx).WorkflowExecution.ID
	cwo := workflow.ChildWorkflowOptions{
		WorkflowID:        fmt.Sprintf("%s-child", workflowID),
		ParentClosePolicy: enums.PARENT_CLOSE_POLICY_ABANDON,
	}
	ctx = workflow.WithChildOptions(ctx, cwo)

	workflowURL := fmt.Sprintf(WorkflowURLFmt, config.CurEnv().Name(), workflowID, workflow.GetInfo(ctx).WorkflowExecution.RunID)
	if params.InitialSleepDuration > 0 {
		workflow.Sleep(ctx, params.InitialSleepDuration)
	}
	if params.SeoFailedInfoWorkFlow == nil {
		params.SeoFailedInfoWorkFlow = &seofailedworkflowinfo.SEOFailedWorkflowInfo{
			BusinessID:       params.BusinessID,
			WorkflowId:       workflowID,
			WorkflowType:     "LocalSEOWorkflow",
			StartDate:        params.Date,
			Keywords:         params.Keywords,
			RetryCount:       0,
			IsRetryableError: false,
			IssueResolved:    false,
		}
	}

	var requests *BuildRequestsResult
	err := workflow.ExecuteActivity(activityCtx, buildRequests, params.BusinessID, params.Keywords, params.Force, params.Date, workflowURL).Get(ctx, &requests)
	if err != nil {
		return handleWorkflowError(ctx, params, err)
	}

	// If no requests were built, exit the workflow
	if len(requests.MapsRequests) == 0 && len(requests.OrganicRequests) == 0 {
		return nil
	}

	processedMapsTasks := &ProcessedTaskResults{
		PostedTasks: []*PostedTask{},
		Errors:      []string{},
	}

	// DataForSEO has a rate limit of 2000 tasks per minute. This will send 20 tasks about every 20 seconds, or 60 tasks every minute
	for i := 0; i < len(requests.MapsRequests); i += taskBatchSize {
		var taskRequests []*dataforseo.GoogleMapsSERPTaskPostRequest
		if i+taskBatchSize > len(requests.MapsRequests) {
			taskRequests = requests.MapsRequests[i:]
		} else {
			taskRequests = requests.MapsRequests[i : i+taskBatchSize]
		}

		var batchTasks *ProcessedTaskResults
		err = workflow.ExecuteActivity(rateLimitContext, processMapsRequests, params, taskRequests, requests.HasPro).Get(ctx, &batchTasks)
		if err != nil {
			return handleWorkflowError(ctx, params, err)
		}

		processedMapsTasks.PostedTasks = append(processedMapsTasks.PostedTasks, batchTasks.PostedTasks...)
		processedMapsTasks.Errors = append(processedMapsTasks.Errors, batchTasks.Errors...)
	}
	if len(processedMapsTasks.Errors) > 0 {
		logger.Error("Errors fetching and storing batch", processedMapsTasks.Errors)
	}

	var postedTaskGroups []*PostedTaskGroup
	err = workflow.ExecuteActivity(activityCtx, sortTasks, processedMapsTasks.PostedTasks).Get(ctx, &postedTaskGroups)
	if err != nil {
		return handleWorkflowError(ctx, params, err)
	}

	processedOrganicTasks := &ProcessedTaskResults{
		PostedTasks: []*PostedTask{},
		Errors:      []string{},
	}

	// DataForSEO has a rate limit of 2000 tasks per minute. This will send 40 tasks about every 20 seconds, or 120 tasks every minute
	for i := 0; i < len(requests.OrganicRequests); i += taskBatchSize {
		var taskRequests []*dataforseo.GoogleOrganicSERPTaskPostRequest
		if i+taskBatchSize > len(requests.OrganicRequests) {
			taskRequests = requests.OrganicRequests[i:]
		} else {
			taskRequests = requests.OrganicRequests[i : i+taskBatchSize]
		}
		var batchTasks *ProcessedTaskResults
		err = workflow.ExecuteActivity(rateLimitContext, processOrganicRequestBatch, params, taskRequests, requests.HasPro).Get(ctx, &batchTasks)
		if err != nil {
			return handleWorkflowError(ctx, params, err)
		}

		processedOrganicTasks.PostedTasks = append(processedOrganicTasks.PostedTasks, batchTasks.PostedTasks...)
		processedOrganicTasks.Errors = append(processedOrganicTasks.Errors, batchTasks.Errors...)
	}

	// Sleep 5 minutes to allow tasks to complete
	workflow.Sleep(ctx, 5*time.Minute)

	var placeID string
	_ = workflow.ExecuteActivity(activityCtx, getConnectedGooglePlaceID, params.BusinessID).Get(ctx, &placeID)

	for _, taskGroup := range postedTaskGroups {
		var localSEODataEntries []*seodata.LocalSearchData
		for _, task := range taskGroup.Tasks {
			var entry *seodata.LocalSearchData
			err = workflow.ExecuteActivity(retryContext, fetchMapsResult, params, task, params.Date, placeID).Get(ctx, &entry)
			if err != nil {
				return handleWorkflowError(ctx, params, err)
			}
			if entry != nil {
				localSEODataEntries = append(localSEODataEntries, entry)
			}
		}

		// Versioning for storeMapsResults to handle searchRadius parameter addition
		version := workflow.GetVersion(ctx, "storeMapsResults-searchRadius", workflow.DefaultVersion, 1)

		storeMapsParams := &StoreMapsParams{
			Keyword:      taskGroup.Keyword,
			Entries:      localSEODataEntries,
			WorkflowURL:  workflowURL,
			SearchRadius: requests.SearchRadius,
			SERPParams:   params,
		}

		if version == workflow.DefaultVersion {
			// Old version without searchRadius parameter
			err = workflow.ExecuteActivity(retryContext, storeMapsResults, params, taskGroup.Keyword, localSEODataEntries, workflowURL).Get(ctx, nil)
		} else {
			// New version with searchRadius parameter
			err = workflow.ExecuteActivity(retryContext, storeMapsResultsV1, storeMapsParams).Get(ctx, nil)
		}
		if err != nil {
			return handleWorkflowError(ctx, params, err)
		}

	}

	for _, task := range processedOrganicTasks.PostedTasks {
		err = workflow.ExecuteActivity(retryContext, getAndStoreOrganicResult, params, task).Get(ctx, nil)
		if err != nil {
			return handleWorkflowError(ctx, params, err)
		}
	}

	return nil
}

type OrganicWorkflowParams struct {
	RequestFileName string
	TotalRequests   int
	Date            string
}

func handleWorkflowError[T SERPWorkflowParams | KeywordInfoWorkflowParamsV1 | KeywordInfoWorkflowParams | AIOAuditWorkflowParams](ctx workflow.Context, params *T, err error) error {
	retryContext := getRetryCtx(ctx)
	logger := workflow.GetLogger(ctx)
	workflowID := workflow.GetInfo(ctx).WorkflowExecution.ID
	workflowURL := fmt.Sprintf(WorkflowURLFmt, config.CurEnv().Name(), workflowID, workflow.GetInfo(ctx).WorkflowExecution.RunID)
	var applicationErr *temporal.ApplicationError
	errors.As(err, &applicationErr)
	// Ensure params is not nil
	if params == nil {
		logger.Error("params is nil")
		return fmt.Errorf("params is nil")
	}
	logger.Error(fmt.Sprintf("SEO workflow failed: %s: %s ", err.Error(), workflowURL))
	if applicationErr != nil {
		statsd.Incr(constants.SEOWorkflowFailedMetric, []string{fmt.Sprintf("message:%s", applicationErr.Type())}, 1)
	} else {
		statsd.Incr(constants.SEOWorkflowFailedMetric, []string{"message:unknown"}, 1)
	}
	// Type switch to handle different struct types
	switch v := any(params).(type) {
	case *SERPWorkflowParams:
		// Process SERPWorkflowParams
		if applicationErr != nil {
			v.SeoFailedInfoWorkFlow.ErrorType = applicationErr.Type()
			v.SeoFailedInfoWorkFlow.ErrorMessage = applicationErr.Error()
			if v.SeoFailedInfoWorkFlow.ErrorType != failedPrecondition {
				v.SeoFailedInfoWorkFlow.IsRetryableError = true
			}
		} else {
			logger.Warn("Failed to store failed workflow info", "error", "UnKnown err")
		}
		storeErr := workflow.ExecuteActivity(retryContext, storeFailedWorkflowInfo, v.SeoFailedInfoWorkFlow).Get(ctx, nil)
		if storeErr != nil {
			logger.Error("Failed to store failed workflow info", "error", storeErr.Error())
		}
	case *KeywordInfoWorkflowParams:
		// Process KeywordInfoWorkflowParams
		if v.SeoFailedWFInfo == nil {
			logger.Error("SeoFailedWFInfo is nil")
			return fmt.Errorf("SeoFailedWFInfo is nil")
		}
		for _, p := range v.SeoFailedWFInfo {
			if p != nil && applicationErr != nil {
				p.ErrorType = applicationErr.Type()
				p.ErrorMessage = applicationErr.Error()
				if p.ErrorType != failedPrecondition {
					p.IsRetryableError = true
				}
				storeErr := workflow.ExecuteActivity(retryContext, storeFailedWorkflowInfo, p).Get(ctx, nil)
				if storeErr != nil {
					logger.Error("Failed to store failed Keyword workflow info", "error", storeErr.Error())
				}

			} else {
				logger.Error(fmt.Sprintf("Failed to store failed workflow info %s => %+v", "UnKnown err", p))
			}
		}
	case *KeywordInfoWorkflowParamsV1:
		// Process KeywordInfoWorkflowParams
		if v.SeoFailedWFInfo == nil {
			logger.Error("SeoFailedWFInfo is nil")
			return fmt.Errorf("SeoFailedWFInfo is nil")
		}
		if v.SeoFailedWFInfo != nil && applicationErr != nil {
			v.SeoFailedWFInfo.ErrorType = applicationErr.Type()
			v.SeoFailedWFInfo.ErrorMessage = applicationErr.Error()
			if v.SeoFailedWFInfo.ErrorType != failedPrecondition {
				v.SeoFailedWFInfo.IsRetryableError = true
			}
			v.SeoFailedWFInfo.Keywords = v.Keywords
			storeErr := workflow.ExecuteActivity(retryContext, storeFailedWorkflowInfo, v.SeoFailedWFInfo).Get(ctx, nil)
			if storeErr != nil {
				logger.Error("Failed to store failed Keyword workflow info", "error", storeErr.Error())
			}

		} else {
			logger.Error(fmt.Sprintf("Failed to store failed workflow info %s => %+v", "UnKnown err", v.SeoFailedWFInfo))
		}
	case *AIOAuditWorkflowParams:
		// Process AIOAuditWorkflowParams
		if applicationErr != nil {
			// For AIOAudit, we'll just log the error and increment the metric
			logger.Error(fmt.Sprintf("AIOAudit workflow failed for business %s => %+v", v.BusinessID, applicationErr))
		}
		return applicationErr

	default:
		logger.Error("Invalid parameter type, expected *SERPWorkflowParams or *KeywordInfoWorkflowParams")
		caseNotSupportedError := fmt.Errorf("invalid parameter type, expected *SERPWorkflowParams or *KeywordInfoWorkflowParams but got %T", v)
		return caseNotSupportedError
	}
	return err
}
