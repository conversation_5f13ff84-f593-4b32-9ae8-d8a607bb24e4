package seoworkflow

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"go.temporal.io/sdk/activity"
	"go.temporal.io/sdk/temporal"
	"go.temporal.io/sdk/workflow"

	"github.com/vendasta/listing-products/internal/dataforseo"
)

// AIOAudit workflow types and functions
type AIOAuditWorkflowParams struct {
	BusinessID   string
	Website      string
	BusinessName string
	AuditDate    string
}

func AIOAuditWorkflow(ctx workflow.Context, params *AIOAuditWorkflowParams) error {
	logger := workflow.GetLogger(ctx)
	logger.Info("Starting AIOAudit workflow", "business_id", params.BusinessID, "website", params.Website, "business_name", params.BusinessName)

	activityCtx := getActivityCtx(ctx)

	buildAuditRequestParam := &buildAuditRequestParams{
		BusinessID:   params.BusinessID,
		Website:      params.Website,
		BusinessName: params.BusinessName,
	}
	var buildAuditResponse *buildAuditRequestResponse
	err := workflow.ExecuteActivity(activityCtx, buildAuditRequestActivity, buildAuditRequestParam).Get(ctx, &buildAuditResponse)
	if err != nil {
		return handleWorkflowError(ctx, params, err)
	}

	startSiteCrawlParam := &startSiteCrawlParams{
		BusinessID:   params.BusinessID,
		Website:      params.Website,
		BusinessName: params.BusinessName,
		Request:      buildAuditResponse.Request,
	}
	var startSiteCrawlResponse *startSiteCrawlResponse
	err = workflow.ExecuteActivity(activityCtx, startSiteCrawlActivity, startSiteCrawlParam).Get(ctx, &startSiteCrawlResponse)
	if err != nil {
		return handleWorkflowError(ctx, params, err)
	}

	logger.Info("DataForSEO task created successfully", "business_id", params.BusinessID, "task_id", startSiteCrawlResponse.TaskID)

	// Sleep 5 minutes to allow tasks to complete before checking status
	workflow.Sleep(ctx, 5*time.Minute)

	checkCrawlStatusParam := &checkCrawlStatusParams{
		BusinessID:   params.BusinessID,
		Website:      params.Website,
		BusinessName: params.BusinessName,
		TaskID:       startSiteCrawlResponse.TaskID,
	}
	err = workflow.ExecuteActivity(getRetryCtx(ctx), checkCrawlStatusActivity, checkCrawlStatusParam).Get(ctx, nil)
	if err != nil {
		return handleWorkflowError(ctx, params, err)
	}

	getCrawlResultsParam := &getCrawlResultsParams{
		BusinessID:   params.BusinessID,
		Website:      params.Website,
		BusinessName: params.BusinessName,
		TaskID:       startSiteCrawlResponse.TaskID,
	}
	var getCrawlResultsResponse *getCrawlResultsResponse
	err = workflow.ExecuteActivity(activityCtx, getCrawlResultsActivity, getCrawlResultsParam).Get(ctx, &getCrawlResultsResponse)
	if err != nil {
		return handleWorkflowError(ctx, params, err)
	}

	generateAiInsightsParam := &generateAiInsightsParams{
		BusinessID:   params.BusinessID,
		Website:      params.Website,
		BusinessName: params.BusinessName,
		TaskID:       startSiteCrawlResponse.TaskID,
		URLs:         getCrawlResultsResponse.URLs,
		Summary:      getCrawlResultsResponse.Summary,
		Pages:        getCrawlResultsResponse.Pages,
	}

	logger.Info("[AI_INSIGHTS] Preparing generateAiInsights activity", "business_id", params.BusinessID, "task_id", startSiteCrawlResponse.TaskID, "urls_count", len(getCrawlResultsResponse.URLs))
	for i, url := range getCrawlResultsResponse.URLs {
		logger.Info("[AI_INSIGHTS] URL to be processed", "business_id", params.BusinessID, "url_index", i, "url", url)
	}
	var generateAiInsightsResponse *generateAiInsightsResponse
	err = workflow.ExecuteActivity(activityCtx, generateAiInsightsActivity, generateAiInsightsParam).Get(ctx, &generateAiInsightsResponse)
	if err != nil {
		return handleWorkflowError(ctx, params, err)
	}

	logger.Info("AIOAudit workflow completed successfully", "business_id", params.BusinessID, "website", params.Website, "business_name", params.BusinessName, "raw_html_summary", generateAiInsightsResponse.Summary)
	return nil
}

// AIOAudit activity parameter types
type buildAuditRequestParams struct {
	BusinessID   string
	Website      string
	BusinessName string
}

type buildAuditRequestResponse struct {
	Request *dataforseo.OnPageTaskPostRequest
}

type startSiteCrawlParams struct {
	BusinessID   string
	Website      string
	BusinessName string
	Request      *dataforseo.OnPageTaskPostRequest
}

type startSiteCrawlResponse struct {
	TaskID string
}

type checkCrawlStatusParams struct {
	BusinessID   string
	Website      string
	BusinessName string
	TaskID       string
}

type getCrawlResultsParams struct {
	BusinessID   string
	Website      string
	BusinessName string
	TaskID       string
}

type getCrawlResultsResponse struct {
	Summary *dataforseo.OnPageSummaryResponse
	Pages   *dataforseo.OnPagePagesResponse
	URLs    []string
}

type generateAiInsightsParams struct {
	BusinessID   string
	Website      string
	BusinessName string
	TaskID       string
	URLs         []string
	Summary      *dataforseo.OnPageSummaryResponse
	Pages        *dataforseo.OnPagePagesResponse
}

type generateAiInsightsResponse struct {
	Summary string
}

// AIOAudit activity functions
func buildAuditRequestActivity(ctx context.Context, params *buildAuditRequestParams) (*buildAuditRequestResponse, error) {
	logger := activity.GetLogger(ctx)
	logger.Info("Starting buildAuditRequest activity", "business_id", params.BusinessID, "website", params.Website, "business_name", params.BusinessName)

	// Prepare DataForSEO on_page request for AIO audit
	request := &dataforseo.OnPageTaskPostRequest{
		Target:                  params.Website,
		MaxCrawlPages:           2,
		CrawlMode:               "recursive",
		LoadResources:           true,
		StoreRawHTML:            true,
		EnableAIContentAnalysis: true,
		EnableJavascript:        false,
		Tag:                     dataforseo.MakeTag(params.BusinessID, "", "aio_audit"),
		CustomUserAgent:         "GPTBot/1.0 (+https://openai.com/gptbot)",
	}

	response := &buildAuditRequestResponse{
		Request: request,
	}

	logger.Info("DataForSEO on_page request prepared successfully", "business_id", params.BusinessID, "website", params.Website, "business_name", params.BusinessName)

	return response, nil
}

func startSiteCrawlActivity(ctx context.Context, params *startSiteCrawlParams) (*startSiteCrawlResponse, error) {
	logger := activity.GetLogger(ctx)
	logger.Info("Starting startSiteCrawl activity", "business_id", params.BusinessID, "website", params.Website, "business_name", params.BusinessName)

	// Get DataForSEO client from activity services
	services := getActivityServices(ctx)

	// Call DataForSEO on_page API to start the site crawl
	response, err := services.dataForSEOClient.OnPageTaskPost(ctx, []*dataforseo.OnPageTaskPostRequest{params.Request}, dataforseo.RequestSourcePaidKeywordTracking)
	if err != nil {
		return nil, temporal.NewApplicationError("Failed to send DataForSEO on_page request", "failed_task_post", err.Error())
	}
	if len(response.Tasks) < 1 {
		return nil, temporal.NewApplicationError("no tasks returned from DataForSEO on_page", "failed_task_post")
	}
	task := response.Tasks[0]
	if task.StatusCode >= 40000 {
		return nil, temporal.NewApplicationError("Task failed to post", "failed_task_post", task, task.StatusMessage)
	}

	crawlResponse := &startSiteCrawlResponse{
		TaskID: task.ID,
	}

	logger.Info("DataForSEO on_page API call completed successfully", "business_id", params.BusinessID, "website", params.Website, "business_name", params.BusinessName, "task_id", crawlResponse.TaskID)

	return crawlResponse, nil
}

func checkCrawlStatusActivity(ctx context.Context, params *checkCrawlStatusParams) error {
	logger := activity.GetLogger(ctx)
	logger.Info("Starting checkCrawlStatus activity", "business_id", params.BusinessID, "website", params.Website, "business_name", params.BusinessName, "task_id", params.TaskID)

	// Get DataForSEO client from activity services
	services := getActivityServices(ctx)

	// Call DataForSEO on_page tasks_ready API
	response, err := services.dataForSEOClient.OnPageTasksReady(ctx, dataforseo.RequestSourcePaidKeywordTracking)
	if err != nil {
		return temporal.NewApplicationError("Failed to check DataForSEO on_page tasks status", "failed_tasks_ready", err.Error())
	}

	if len(response.Tasks) < 1 {
		return temporal.NewApplicationError("no tasks returned from DataForSEO on_page tasks_ready", "failed_tasks_ready")
	}

	task := response.Tasks[0]
	if task.StatusCode >= 40000 {
		return temporal.NewApplicationError("Task failed in tasks_ready", "failed_tasks_ready", task, task.StatusMessage)
	}

	// Check if our specific task ID is in the ready results
	taskFound := false
	if task.Result != nil {
		// Parse the result to check for our task ID
		resultBytes, err := json.Marshal(task.Result)
		if err != nil {
			logger.Error("Failed to marshal task result", "error", err.Error())
			return temporal.NewApplicationError("Failed to parse task results", "failed_parse_results", err.Error())
		}

		var taskResults []*dataforseo.OnPageTaskResult
		err = json.Unmarshal(resultBytes, &taskResults)
		if err != nil {
			logger.Error("Failed to unmarshal task results", "error", err.Error())
			return temporal.NewApplicationError("Failed to parse task results", "failed_parse_results", err.Error())
		}

		for _, result := range taskResults {
			if result.ID == params.TaskID {
				taskFound = true
				logger.Info("Task found in ready results", "business_id", params.BusinessID, "task_id", params.TaskID, "target", result.Target)
				break
			}
		}
	}

	if !taskFound {
		// Task is not ready yet, return a retryable error to trigger Temporal retry
		logger.Info("Task not ready yet, will retry", "business_id", params.BusinessID, "task_id", params.TaskID)
		return temporal.NewApplicationError("Task not ready yet", "task_not_ready")
	}

	logger.Info("Crawl status checked successfully - task is ready", "business_id", params.BusinessID, "website", params.Website, "business_name", params.BusinessName, "task_id", params.TaskID)

	return nil
}

func getCrawlResultsActivity(ctx context.Context, params *getCrawlResultsParams) (*getCrawlResultsResponse, error) {
	logger := activity.GetLogger(ctx)
	logger.Info("Starting getCrawlResults activity", "business_id", params.BusinessID, "website", params.Website, "business_name", params.BusinessName, "task_id", params.TaskID)

	// Get DataForSEO client from activity services
	services := getActivityServices(ctx)

	// Call DataForSEO on_page summary API to get crawl results
	summaryResponse, err := services.dataForSEOClient.OnPageSummary(ctx, params.TaskID, dataforseo.RequestSourcePaidKeywordTracking)
	if err != nil {
		return nil, temporal.NewApplicationError("Failed to get DataForSEO on_page summary", "failed_summary", err.Error())
	}

	if len(summaryResponse.Tasks) < 1 {
		return nil, temporal.NewApplicationError("no tasks returned from DataForSEO on_page summary", "failed_summary")
	}

	summaryTask := summaryResponse.Tasks[0]
	if summaryTask.StatusCode >= 40000 {
		return nil, temporal.NewApplicationError("Task failed in summary", "failed_summary", summaryTask, summaryTask.StatusMessage)
	}

	// Call DataForSEO on_page pages API to get detailed page information
	logger.Info("Calling DataForSEO OnPagePages API", "business_id", params.BusinessID, "task_id", params.TaskID)

	pagesRequest := &dataforseo.OnPagePagesRequest{
		ID:    params.TaskID,
		Limit: 10,
	}

	logger.Info("OnPagePages request prepared", "business_id", params.BusinessID, "task_id", params.TaskID, "request_id", pagesRequest.ID, "limit", pagesRequest.Limit)

	pagesResponse, err := services.dataForSEOClient.OnPagePages(ctx, []*dataforseo.OnPagePagesRequest{pagesRequest}, dataforseo.RequestSourcePaidKeywordTracking)
	if err != nil {
		logger.Error("OnPagePages API call failed", "business_id", params.BusinessID, "task_id", params.TaskID, "error", err.Error())
		return nil, temporal.NewApplicationError("Failed to get DataForSEO on_page pages", "failed_pages", err.Error())
	}

	logger.Info("OnPagePages API call successful", "business_id", params.BusinessID, "task_id", params.TaskID, "tasks_count", len(pagesResponse.Tasks))

	if len(pagesResponse.Tasks) < 1 {
		logger.Error("No tasks returned from OnPagePages API", "business_id", params.BusinessID, "task_id", params.TaskID)
		return nil, temporal.NewApplicationError("no tasks returned from DataForSEO on_page pages", "failed_pages")
	}

	pagesTask := pagesResponse.Tasks[0]
	logger.Info("OnPagePages task details", "business_id", params.BusinessID, "task_id", params.TaskID, "task_status_code", pagesTask.StatusCode, "task_status_message", pagesTask.StatusMessage)

	if pagesTask.StatusCode >= 40000 {
		logger.Error("OnPagePages task failed", "business_id", params.BusinessID, "task_id", params.TaskID, "task_status_code", pagesTask.StatusCode, "task_status_message", pagesTask.StatusMessage)
		return nil, temporal.NewApplicationError("Task failed in pages", "failed_pages", pagesTask, pagesTask.StatusMessage)
	}

	// Extract URLs from the pages response
	var urls []string
	logger.Info("Extracting URLs from OnPagePages response", "business_id", params.BusinessID, "task_id", params.TaskID, "results_count", len(pagesTask.Result))

	if len(pagesTask.Result) > 0 {
		result := pagesTask.Result[0]
		logger.Info("OnPagePages result details", "business_id", params.BusinessID, "task_id", params.TaskID, "items_count", result.ItemsCount, "total_items_count", result.TotalItemsCount, "crawl_progress", result.CrawlProgress)

		if len(result.Items) > 0 {
			logger.Info("Processing OnPagePages items", "business_id", params.BusinessID, "task_id", params.TaskID, "items_count", len(result.Items))
			for i, item := range result.Items {
				logger.Info("OnPagePages item details", "business_id", params.BusinessID, "task_id", params.TaskID, "item_index", i, "url", item.URL, "status_code", item.StatusCode, "resource_type", item.ResourceType)
				if item.URL != "" {
					urls = append(urls, item.URL)
				}
			}
		} else {
			logger.Warn("No items found in OnPagePages result", "business_id", params.BusinessID, "task_id", params.TaskID)
		}
	} else {
		logger.Warn("No results found in OnPagePages response", "business_id", params.BusinessID, "task_id", params.TaskID)
	}

	logger.Info("Crawl results retrieved successfully", "business_id", params.BusinessID, "website", params.Website, "business_name", params.BusinessName, "task_id", params.TaskID, "urls_count", len(urls))

	return &getCrawlResultsResponse{
		Summary: summaryResponse,
		Pages:   pagesResponse,
		URLs:    urls,
	}, nil
}

func generateAiInsightsActivity(ctx context.Context, params *generateAiInsightsParams) (*generateAiInsightsResponse, error) {
	logger := activity.GetLogger(ctx)
	logger.Info("[AI_INSIGHTS] Starting generateAiInsights activity", "business_id", params.BusinessID, "website", params.Website, "business_name", params.BusinessName, "task_id", params.TaskID, "urls_count", len(params.URLs))

	// Log each URL being processed
	for i, url := range params.URLs {
		logger.Info("[AI_INSIGHTS] URL to process", "business_id", params.BusinessID, "url_index", i, "url", url)
	}

	services := getActivityServices(ctx)
	processedCount := 0
	totalHTMLSize := 0

	// Process each URL to get raw HTML
	for i, url := range params.URLs {
		logger.Info("[AI_INSIGHTS] Processing URL for raw HTML", "business_id", params.BusinessID, "url_index", i, "url", url)

		// Create a unique ID for this request
		requestID := params.TaskID

		// Prepare the request for this URL
		request := &dataforseo.OnPageRawHTMLRequest{
			ID:  requestID,
			URL: url,
		}

		logger.Info("[AI_INSIGHTS] Calling DataForSEO OnPage Raw HTML API", "business_id", params.BusinessID, "request_id", requestID, "url", url, "request_payload", fmt.Sprintf("ID: %s, URL: %s", request.ID, request.URL))

		// Call the DataForSEO OnPage Raw HTML API
		response, err := services.dataForSEOClient.OnPageRawHTML(ctx, []*dataforseo.OnPageRawHTMLRequest{request}, dataforseo.RequestSourceCategoryWorkflow)
		if err != nil {
			logger.Error("[AI_INSIGHTS] Failed to get raw HTML from DataForSEO", "business_id", params.BusinessID, "url", url, "error", err)
			return nil, temporal.NewApplicationError("Failed to get DataForSEO on_page raw_html", "failed_raw_html", err)
		}

		logger.Info("[AI_INSIGHTS] DataForSEO OnPage Raw HTML API response received", "business_id", params.BusinessID, "url", url, "tasks_count", len(response.Tasks), "response_status", fmt.Sprintf("Response received with %d tasks", len(response.Tasks)))

		// Extract the raw HTML from the response
		if len(response.Tasks) > 0 {
			task := response.Tasks[0]
			logger.Info("[AI_INSIGHTS] Processing task from response", "business_id", params.BusinessID, "url", url, "task_index", 0, "results_count", len(task.Result), "task_status_code", task.StatusCode, "task_status_message", task.StatusMessage)

			if len(task.Result) > 0 {
				result := task.Result[0]
				logger.Info("[AI_INSIGHTS] Processing result from task", "business_id", params.BusinessID, "url", url, "result_index", 0, "items_nil", result.Items == nil, "result_type", fmt.Sprintf("%T", result.Items))

				if result.Items != nil {
					html := result.Items.HTML
					processedCount++
					totalHTMLSize += len(html)
					logger.Info("[AI_INSIGHTS] Successfully retrieved raw HTML", "business_id", params.BusinessID, "url", url, "html_length", len(html))
				} else {
					logger.Warn("[AI_INSIGHTS] Items is nil in response", "business_id", params.BusinessID, "url", url, "result_structure", fmt.Sprintf("Result type: %T, Items field: %v", result, result.Items))
				}
			} else {
				logger.Warn("[AI_INSIGHTS] No results found in task", "business_id", params.BusinessID, "url", url, "task_status", fmt.Sprintf("Task status: %d - %s", task.StatusCode, task.StatusMessage))
			}
		} else {
			logger.Warn("[AI_INSIGHTS] No tasks found in response", "business_id", params.BusinessID, "url", url, "response_structure", fmt.Sprintf("Response type: %T, Tasks field: %v", response, response.Tasks))
		}
	}

	summary := fmt.Sprintf("Processed %d URLs successfully, total HTML size: %d characters", processedCount, totalHTMLSize)
	logger.Info("[AI_INSIGHTS] Raw HTML retrieval completed", "business_id", params.BusinessID, "website", params.Website, "business_name", params.BusinessName, "processed_urls", processedCount, "total_html_size", totalHTMLSize)

	// TODO: Implement actual AI insights generation logic using the task ID, URLs, and raw HTML summary
	logger.Info("[AI_INSIGHTS] AI insights generated successfully", "business_id", params.BusinessID, "website", params.Website, "business_name", params.BusinessName, "task_id", params.TaskID, "urls_count", len(params.URLs), "raw_html_summary", summary)

	return &generateAiInsightsResponse{
		Summary: summary,
	}, nil
}
