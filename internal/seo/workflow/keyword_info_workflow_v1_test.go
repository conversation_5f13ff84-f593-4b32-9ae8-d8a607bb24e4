package seoworkflow

import (
	"context"
	"errors"
	"testing"

	"github.com/golang/mock/gomock"
	"github.com/golang/protobuf/ptypes/timestamp"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	address "github.com/vendasta/address/sdks/go"
	adver "github.com/vendasta/advertising/sdks/go/v2"
	advertising "github.com/vendasta/generated-protos-go/advertising/v1"
	nap_v1 "github.com/vendasta/generated-protos-go/nap/v1"
	listingprofilemodel "github.com/vendasta/listing-products/internal/listingprofile/model"
	listingprofileservice "github.com/vendasta/listing-products/internal/listingprofile/service"
	keywordinfoservice "github.com/vendasta/listing-products/internal/seo/keywordinfo/service"
	"github.com/vendasta/listing-products/internal/seo/workflow/seofailedworkflowinfo"
	seofailedworkflowinfoservice "github.com/vendasta/listing-products/internal/seo/workflow/seofailedworkflowinfo/service"
	"go.temporal.io/sdk/testsuite"
	"go.temporal.io/sdk/worker"
)

func Test_KeywordInfoWorkflow1(t *testing.T) {
	type testCase struct {
		name                              string
		startDate                         *timestamp.Timestamp
		endDate                           *timestamp.Timestamp
		workflowParams                    *KeywordInfoWorkflowParamsV1
		listingProfile                    *listingprofilemodel.ListingProfile
		expectedListingProfileAPICalls    int
		expectedStates                    *nap_v1.ListStatesResponse
		expectedListStateAPICalls         int
		locationData                      *LocationData
		expectedAdvertisingResp           *advertising.KeywordMetricsResponse
		expectedAdvertisingErr            error
		expectedAdvertisingAPICalls       int
		expectedKeywordInfoUpsertAPICalls int
		expectedUpdateSeoFailed           []*seofailedworkflowinfo.SEOFailedWorkflowInfo
		expectedResponse                  interface{}
		expectedWorkflowErr               error
		expectedWorkflowErrCount          int
		expectedSEOFailedWorkflowInfos    []*seofailedworkflowinfo.SEOFailedWorkflowInfo
	}
	startDate := &timestamp.Timestamp{Seconds: 1711548031, Nanos: 97696000}
	endDate := &timestamp.Timestamp{Seconds: 1711548031, Nanos: 97696000}
	Date := "2025-03-26"

	cases := []testCase{
		{
			name:      "Success_TestCase",
			startDate: startDate,
			endDate:   endDate,
			workflowParams: &KeywordInfoWorkflowParamsV1{
				LocationCountryCode: "LocationCode",
				BusinessID:          "AG-1",
				Keywords:            []string{"keyword1", "keyword2"},
				Date:                Date,
				SeoFailedWFInfo: &seofailedworkflowinfo.SEOFailedWorkflowInfo{
					BusinessID:       "AG-1",
					WorkflowId:       "default-test-workflow-id",
					WorkflowType:     "KeywordInfoWorkflow",
					StartDate:        Date,
					RetryCount:       0,
					IsRetryableError: false,
					IssueResolved:    false,
				},
			},
			listingProfile: &listingprofilemodel.ListingProfile{
				BusinessID: "AG-1",
				Website:    "www.website.com",
				Country:    "IN",
				State:      "Tamil Nadu",
				City:       "Chennai",
				RichData: &listingprofilemodel.RichData{
					SEOKeywords: []string{"Keyword1", "Keyword2"},
				},
			},
			expectedStates: &nap_v1.ListStatesResponse{
				States: []*nap_v1.State{
					{
						Name: "Tamil Nadu",
						Id:   "Tamil Nadu",
					},
				},
			},
			locationData: &LocationData{
				CountryCode: "IN",
				State:       "Tamil Nadu",
				City:        "Chennai",
			},
			expectedAdvertisingResp: &advertising.KeywordMetricsResponse{
				Results: []*advertising.SearchResult{
					{
						SearchQuery: "Keyword1",
						HistoricalMetrics: &advertising.HistoricalMetrics{
							AvgMonthlySearches:          "1300",
							CompetitionLevel:            "LOW",
							CompetitionIndex:            "31",
							TopOfPageBidLowRangeMicros:  "303790",
							TopOfPageBidHighRangeMicros: "1300000",
							MonthlySearchVolumes: []*advertising.MonthlySearchVolume{
								{
									Month:           "JANUARY",
									Year:            "2025",
									MonthlySearches: "1000",
								},
							},
						},
					},
					{
						SearchQuery: "Keyword2",
						HistoricalMetrics: &advertising.HistoricalMetrics{
							AvgMonthlySearches:          "1300",
							CompetitionLevel:            "LOW",
							CompetitionIndex:            "31",
							TopOfPageBidLowRangeMicros:  "303790",
							TopOfPageBidHighRangeMicros: "1300000",
							MonthlySearchVolumes: []*advertising.MonthlySearchVolume{
								{
									Month:           "JANUARY",
									Year:            "2025",
									MonthlySearches: "1000",
								},
							},
						},
					},
				},
			},
			expectedAdvertisingErr:            nil,
			expectedResponse:                  nil,
			expectedWorkflowErr:               nil,
			expectedListStateAPICalls:         1,
			expectedListingProfileAPICalls:    1,
			expectedAdvertisingAPICalls:       1,
			expectedKeywordInfoUpsertAPICalls: 2,
			expectedWorkflowErrCount:          0,
			expectedSEOFailedWorkflowInfos:    []*seofailedworkflowinfo.SEOFailedWorkflowInfo{},
		},
		{
			name:      "Should return error when fetching states returns error",
			startDate: startDate,
			endDate:   endDate,
			workflowParams: &KeywordInfoWorkflowParamsV1{
				LocationCountryCode: "LocationCode",
				BusinessID:          "AG-1",
				Keywords:            []string{"keyword1", "keyword2"},
				Date:                Date,
				SeoFailedWFInfo: &seofailedworkflowinfo.SEOFailedWorkflowInfo{
					BusinessID:       "AG-1",
					WorkflowId:       "default-test-workflow-id",
					WorkflowType:     "KeywordInfoWorkflow",
					StartDate:        Date,
					RetryCount:       0,
					IsRetryableError: false,
					IssueResolved:    false,
				},
			},
			listingProfile: &listingprofilemodel.ListingProfile{
				BusinessID: "AG-1",
				Website:    "www.website.com",
				Country:    "IN",
				State:      "Tamil Nadu",
				City:       "Chennai",
				RichData: &listingprofilemodel.RichData{
					SEOKeywords: []string{"Keyword1", "Keyword2"},
				},
			},
			expectedStates: &nap_v1.ListStatesResponse{
				States: []*nap_v1.State{
					{
						Name: "Kerala",
						Id:   "Kerala",
					},
				},
			},
			locationData: &LocationData{
				CountryCode: "IN",
				State:       "Tamil Nadu",
				City:        "Chennai",
			},
			expectedAdvertisingResp:           nil,
			expectedAdvertisingErr:            errors.New("failed to get response"),
			expectedListStateAPICalls:         1,
			expectedListingProfileAPICalls:    1,
			expectedAdvertisingAPICalls:       1,
			expectedKeywordInfoUpsertAPICalls: 0,
			expectedWorkflowErr:               errors.New("workflow execution error (type: KeywordInfoWorkflowV1, workflowID: default-test-workflow-id, runID: default-test-run-id): activity error (type: getAndStoreKeywordInfoUsingGoogleAPIActivity, scheduledEventID: 0, startedEventID: 0, identity: )"),
			expectedWorkflowErrCount:          1,
			expectedSEOFailedWorkflowInfos: []*seofailedworkflowinfo.SEOFailedWorkflowInfo{
				{
					BusinessID:        "AG-1",
					WorkflowId:        "default-test-workflow-id",
					WorkflowType:      "KeywordInfoWorkflow",
					StartDate:         "2025-03-26",
					Keywords:          []string{"keyword1", "keyword2"},
					ErrorMessage:      "error getting keyword metrics from google api: (type: unexpected_error, retryable: true)",
					ErrorType:         "unexpected_error",
					StatusCode:        "",
					RetryCount:        0,
					IsRetryableError:  true,
					RetriedWorkflowId: "",
					RetriedDate:       "",
					IssueResolved:     false,
				},
			},
		},
	}

	for _, c := range cases {
		t.Run(c.name, func(t *testing.T) {
			ctx := context.Background()
			ctrl := gomock.NewController(t)
			mockLpService := listingprofileservice.NewMockInterface(ctrl)
			mockLpService.EXPECT().Get(gomock.Any(), c.listingProfile.BusinessID, false).Return(c.listingProfile, nil).Times(c.expectedListingProfileAPICalls)

			mockNAP := address.MockNapDataInterface{}
			mockNAP.On("ListStates", mock.Anything, &nap_v1.ListStatesRequest{CountryId: c.listingProfile.Country}).Return(c.expectedStates, nil).Times(c.expectedListStateAPICalls)

			mockAdwordsClient := adver.NewMockAdwordsClientInterface(ctrl)
			mockAdwordsClient.EXPECT().GetKeywordHistoricalMetricsAPI(gomock.Any(), gomock.Any()).Return(c.expectedAdvertisingResp, c.expectedAdvertisingErr).AnyTimes()

			mockKeywordInfoService := keywordinfoservice.NewMockService(ctrl)
			mockKeywordInfoService.EXPECT().Upsert(gomock.Any(), gomock.Any()).Return(nil).Times(c.expectedKeywordInfoUpsertAPICalls)

			mockSeoFailedWorkflowInfoService := seofailedworkflowinfoservice.NewMockService(ctrl)
			for _, req := range c.expectedSEOFailedWorkflowInfos {
				mockSeoFailedWorkflowInfoService.EXPECT().Upsert(gomock.Any(), req).Return(nil).Times(1)
			}

			testSuite := &testsuite.WorkflowTestSuite{}
			env := testSuite.NewTestWorkflowEnvironment()
			env.RegisterWorkflow(KeywordInfoWorkflowV1)
			env.RegisterActivity(fetchAndValidateLocationDataActivity)
			env.RegisterActivity(getAndStoreKeywordInfoUsingGoogleAPIActivity)
			env.RegisterActivity(storeFailedWorkflowInfo)

			ctx = NewActivityContext(ctx, nil, mockLpService, nil, nil, nil, nil, nil, &mockNAP, nil, nil, nil, nil, mockKeywordInfoService, nil, nil, nil, mockAdwordsClient, nil, mockSeoFailedWorkflowInfoService, nil)

			env.SetWorkerOptions(worker.Options{
				BackgroundActivityContext: ctx,
			})

			env.ExecuteWorkflow(KeywordInfoWorkflowV1, c.workflowParams)
			if !env.IsWorkflowCompleted() {
				t.Errorf("Workflow failed to complete. Error (%s)", env.GetWorkflowError())
			}

			errorValue := env.GetWorkflowError()

			if c.expectedWorkflowErr != nil {
				assert.ErrorContains(t, errorValue, c.expectedWorkflowErr.Error())
			} else {
				assert.NoError(t, errorValue, "Expected no error, but got one")
			}
		})
	}
}
