package aioauditresultservice

import (
	"context"

	"time"

	"github.com/vendasta/gosdks/logging"
	model "github.com/vendasta/listing-products/internal/seo/aioauditresult"
	repository "github.com/vendasta/listing-products/internal/seo/aioauditresult/repository"
)

// ServiceImpl implements Service
type ServiceImpl struct {
	repo repository.Repository
}

// New returns a new ServiceImpl
func New(repo repository.Repository) *ServiceImpl {
	return &ServiceImpl{
		repo: repo,
	}
}

// Create will create a new AIOAuditResult if it does not exist yet.
// If the AIOAuditResult already exists, a verrors.AlreadyExists error will be returned.
// TODO: Add more parameters to this method - it is likely that the primary key alone may not be enough to create a valid AIOAuditResult
func (s *ServiceImpl) Create(ctx context.Context, businessID string, websiteURL string, auditDate string) error {
	in := &model.AIOAuditResult{
		BusinessID: businessID, WebsiteURL: websiteURL, AuditDate: auditDate,
	}

	err := s.repo.Create(ctx, in)
	if err != nil {
		logging.Debugf(ctx, "Error trying to create AIOAuditResult: %s", err.Error())
		return err
	}
	return nil
}

// Get will return the existing AIOAuditResult specified by the given identifiers, or an verrors.NotFound error
// if the targeted AIOAuditResult does not exist.
func (s *ServiceImpl) Get(ctx context.Context, businessID string, websiteURL string, auditDate string) (*model.AIOAuditResult, error) {
	out, err := s.repo.Get(ctx, model.NewKey(businessID, websiteURL, auditDate))
	if err != nil {
		logging.Infof(ctx, "Error trying to retrieve AIOAuditResult from repo: %s", err.Error())
		return nil, err
	}
	return out, nil
}

// List see Service.List
// TODO you need to add parameters that make sense in your business domain,
//
//	and use them to add the appropriate filters.
//	(Either filtering by key columns, or columns in a composite index.)
func (s *ServiceImpl) List(ctx context.Context /* TODO add filterable parameters here */, cursorIn string, pageSize int64) (results []*model.AIOAuditResult, cursor string, hasMore bool, err error) {
	keyFilter := model.Key{
		// TODO filter by key columns here
	}
	results, cursor, hasMore, err = s.repo.List(ctx, keyFilter, cursorIn, pageSize)
	if err != nil {
		logging.Infof(ctx, "Error trying to retrieve AIOAuditResult from repo: %s", err.Error())
		return nil, "", false, err
	}
	return
}

// Delete will delete the specified AIOAuditResult.
// If the AIOAuditResult does not exist or has already been deleted, a verrors.NotFound error will be returned.
func (s *ServiceImpl) Delete(ctx context.Context, businessID string, websiteURL string, auditDate string) error {
	err := s.repo.Mutate(ctx, model.NewKey(businessID, websiteURL, auditDate), repository.SetDeleted(time.Now().UTC()))
	if err != nil {
		logging.Infof(ctx, "Error trying to delete AIOAuditResult from repo: %s", err.Error())
		return err
	}
	return nil
}
func (s *ServiceImpl) UpsertAIOAuditResult(ctx context.Context, businessID, websiteURL, auditDate string, update model.AIOAuditResult) error {
	parsedTime, err := time.Parse(auditDate, "2006-01-02")
	if err != nil {
		logging.Infof(ctx, "Error trying to parse auditDate: %s", err.Error())
		return err
	}
	update.StartDate = parsedTime
	if err := s.repo.Upsert(ctx, model.NewKey(businessID, websiteURL, auditDate), repository.SetAIOAuditResult(update)); err != nil {
		logging.Infof(ctx, "Error trying to upsert AIOAuditResult from repo: %s", err.Error())
		return err
	}
	return nil
}

func (s *ServiceImpl) UpdateAIOAuditResult(ctx context.Context, businessID, websiteURL, auditDate string, update model.AIOAuditResult) error {
	if err := s.repo.Mutate(ctx, model.NewKey(businessID, websiteURL, auditDate), repository.SetAIOAuditResult(update)); err != nil {
		logging.Infof(ctx, "Error trying to update AIOAuditResult from repo: %s", err.Error())
		return err
	}
	return nil
}

func (s *ServiceImpl) GetAIOAuditResult(ctx context.Context, businessID, websiteURL, auditDate string) (*model.AIOAuditResult, error) {
	out, err := s.repo.Get(ctx, model.NewKey(businessID, websiteURL, auditDate))
	if err != nil {
		logging.Infof(ctx, "Error trying to retrieve AIOAuditResult from repo: %s", err.Error())
		return nil, err
	}
	return out, nil
}

func (s *ServiceImpl) GetAuditPageResult(ctx context.Context, businessID, websiteURL, auditDate, pageURL string) ([]*model.AuditPageData, error) {
	out, err := s.repo.Get(ctx, model.NewKey(businessID, websiteURL, auditDate))
	if err != nil {
		logging.Infof(ctx, "Error trying to retrieve AuditPageResult from repo: %s", err.Error())
		return nil, err
	}
	// Filter audit pages by pageURL if needed
	for _, page := range out.AuditPages {
		if page.PageURL == pageURL {
			return []*model.AuditPageData{page}, nil
		}
	}
	return nil, nil
}

func (s *ServiceImpl) GetAuditSummary(ctx context.Context, businessID, websiteURL, auditDate string) (string, error) {
	out, err := s.repo.Get(ctx, model.NewKey(businessID, websiteURL, auditDate))
	if err != nil {
		logging.Infof(ctx, "Error trying to retrieve AuditSummary from repo: %s", err.Error())
		return "", err
	}
	return out.AuditSummary, nil
}

func (s *ServiceImpl) UpdateAuditStatus(ctx context.Context, businessID, websiteURL, auditDate string, status model.AuditStatus) error {
	if err := s.repo.Mutate(ctx, model.NewKey(businessID, websiteURL, auditDate), repository.SetAuditStatus(string(status))); err != nil {
		logging.Infof(ctx, "Error trying to update AuditStatus from repo: %s", err.Error())
		return err
	}
	return nil
}

func (s *ServiceImpl) GetAuditStatus(ctx context.Context, businessID, websiteURL, auditDate string) (model.AuditStatus, error) {
	out, err := s.repo.Get(ctx, model.NewKey(businessID, websiteURL, auditDate))
	if err != nil {
		logging.Infof(ctx, "Error trying to retrieve AuditStatus from repo: %s", err.Error())
		return model.AuditStatus(""), err
	}
	return model.AuditStatus(out.AuditStatus), nil
}

func (s *ServiceImpl) UpsertAuditPageResult(ctx context.Context, businessID, websiteURL, auditDate, pageURL string, update model.AuditPageData) error {
	if err := s.repo.Mutate(ctx, model.NewKey(businessID, websiteURL, auditDate), repository.SetAuditPageResult(pageURL, update)); err != nil {
		logging.Infof(ctx, "Error trying to upsert AuditPageResult from repo: %s", err.Error())
		return err
	}
	return nil
}
func (s *ServiceImpl) UpdateAuditAllPages(ctx context.Context, businessID, websiteURL, auditDate string, update []*model.AuditPageData) error {
	err := s.repo.Mutate(ctx, model.NewKey(businessID, websiteURL, auditDate), repository.SetAuditAllPages(update))
	if err != nil {
		logging.Infof(ctx, "Error updating AuditAllPages: %v", err)
	}
	return err
}
func (s *ServiceImpl) UpdateAuditScoreResult(ctx context.Context, businessID, websiteURL, auditDate string, update []model.AuditScoreResults) error {
	if err := s.repo.Mutate(ctx, model.NewKey(businessID, websiteURL, auditDate), repository.SetAuditScoreResult(update)); err != nil {
		logging.Infof(ctx, "Error updating AuditScoreResult: %v", err)
		return err
	}
	return nil
}

func (s *ServiceImpl) GetAuditScoreResult(ctx context.Context, businessID, websiteURL, auditDate string) ([]*model.AuditScoreResults, error) {
	out, err := s.repo.Get(ctx, model.NewKey(businessID, websiteURL, auditDate))
	if err != nil {
		logging.Infof(ctx, "Error trying to retrieve AuditScoreResult from repo: %s", err.Error())
		return nil, err
	}
	return out.AuditScoreResults, nil
}

func (s *ServiceImpl) GetALLAIOAuditResult(ctx context.Context, businessID string) ([]*model.AIOAuditResult, error) {
	var (
		allResults []*model.AIOAuditResult
		cursor     string
		pageSize   int64 = 1000
	)
	hasMore := true
	for hasMore {
		out, nextCur, more, err := s.repo.List(ctx, model.Key{BusinessID: businessID}, cursor, pageSize)
		if err != nil {
			logging.Infof(ctx, "Error trying to retrieve AIOAuditResult from repo: %s", err.Error())
			return nil, err
		}
		allResults = append(allResults, out...)
		cursor = nextCur
		hasMore = more
	}
	return allResults, nil
}
