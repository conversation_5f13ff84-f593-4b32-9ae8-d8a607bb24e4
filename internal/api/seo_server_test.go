package api

import (
	"context"
	"errors"
	"testing"
	"time"

	"github.com/golang/mock/gomock"
	"github.com/stretchr/testify/assert"
	listing_products_v1 "github.com/vendasta/generated-protos-go/listing_products/v1"
	dataforseocategoriesservice "github.com/vendasta/listing-products/internal/dataforseocategories/service"
	listingprofileservice "github.com/vendasta/listing-products/internal/listingprofile/service"
	"github.com/vendasta/listing-products/internal/seo/aioauditresult"
	keywordinfoservice "github.com/vendasta/listing-products/internal/seo/keywordinfo/service"
	model "github.com/vendasta/listing-products/internal/seo/model"
	seodataservice "github.com/vendasta/listing-products/internal/seo/service"
	seoworkflow "github.com/vendasta/listing-products/internal/seo/workflow"
	seosettingsservice "github.com/vendasta/listing-products/internal/seosettings/service"
	"google.golang.org/protobuf/types/known/timestamppb"
)

type mocksSEOServer struct {
	service                   *seodataservice.MockService
	seoSettingsService        *seosettingsservice.MockService
	listingProfileService     *listingprofileservice.MockInterface
	workflowService           *seoworkflow.MockSEOWorkflowServiceInterface
	keywordInfoService        *keywordinfoservice.MockService
	dataForSEOCategoryService *dataforseocategoriesservice.MockService
}

func setupSEOServer(t *testing.T) (*SEOServer, mocksSEOServer, *gomock.Controller) {
	ctrl := gomock.NewController(t)
	m := mocksSEOServer{
		service:                   seodataservice.NewMockService(ctrl),
		seoSettingsService:        seosettingsservice.NewMockService(ctrl),
		listingProfileService:     listingprofileservice.NewMockInterface(ctrl),
		workflowService:           seoworkflow.NewMockSEOWorkflowServiceInterface(ctrl),
		keywordInfoService:        keywordinfoservice.NewMockService(ctrl),
		dataForSEOCategoryService: dataforseocategoriesservice.NewMockService(ctrl),
	}
	reader := &SEOServer{
		service:                   m.service,
		seoSettingsService:        m.seoSettingsService,
		listingProfileService:     m.listingProfileService,
		keywordInfoService:        m.keywordInfoService,
		dataForSEOCategoryService: m.dataForSEOCategoryService,
	}
	return reader, m, ctrl
}

func TestGetSEODataSummary(t *testing.T) {
	startTime, _ := time.Parse("2006-01-02", "2025-03-06")
	EndTime, _ := time.Parse("2006-01-02", "2025-03-07")
	type args struct {
		name                               string
		ctx                                context.Context
		req                                *listing_products_v1.GetSEODataSummaryRequest
		res                                *listing_products_v1.GetSEODataSummaryResponse
		error                              error
		MockGetCurrentAndPreviousDataCount int
		MockGetMostRecentDataCount         int
	}

	testcases := []args{
		{
			name: "success",
			ctx:  context.Background(),
			req: &listing_products_v1.GetSEODataSummaryRequest{
				BusinessId: "business123",
				Keywords:   []string{"sample keyword"},
				StartDate:  timestamppb.New(startTime),
				EndDate:    timestamppb.New(EndTime),
			},
			res: &listing_products_v1.GetSEODataSummaryResponse{
				Data: []*listing_products_v1.SEODataSummary{
					{
						Keyword:      "sample keyword",
						Date:         timestamppb.New(EndTime),
						LocalRank:    3,
						OrganicRank:  7,
						Difficulty:   55,
						SearchVolume: 950,
						SearchRadius: 5.0,
					},
				},
				PreviousData: []*listing_products_v1.SEODataSummary{
					{
						Keyword:      "sample keyword",
						Date:         timestamppb.New(startTime),
						LocalRank:    3,
						OrganicRank:  7,
						Difficulty:   55,
						SearchVolume: 950,
						SearchRadius: 5.0,
					},
				},
			},
			MockGetCurrentAndPreviousDataCount: 1,
			MockGetMostRecentDataCount:         1,
			error:                              nil,
		},
		{
			name: "Empty BusinessId",
			ctx:  context.Background(),
			req: &listing_products_v1.GetSEODataSummaryRequest{
				BusinessId: "",
				Keywords:   []string{"sample keyword"},
				StartDate:  timestamppb.New(startTime),
				EndDate:    timestamppb.New(EndTime),
			},
			res:                                &listing_products_v1.GetSEODataSummaryResponse{},
			MockGetCurrentAndPreviousDataCount: 0,
			MockGetMostRecentDataCount:         0,
			error:                              errors.New("business_id is required"),
		},
	}
	for _, c := range testcases {
		t.Run(c.name, func(t *testing.T) {
			s, m, ctrl := setupSEOServer(t)
			defer ctrl.Finish()
			m.service.EXPECT().GetCurrentAndPreviousData(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(
				[]*model.SEOData{
					{
						BusinessID:   "business123",
						Keyword:      "sample keyword",
						Date:         "2025-03-07",
						LocalRank:    3,
						OrganicRank:  7,
						Difficulty:   55,
						SearchVolume: 950,
						SearchRadius: 5,
						LocationInfo: &model.LocationInfo{
							LocationCode: 1,
							LocationName: "San Francisco",
						},
						LocalSearches:      []*model.LocalSearchData{},
						KeywordDataFetched: true,
						WorkflowURL:        "https://example.com/workflow",
						Created:            time.Now().UTC(),
						Updated:            time.Now().UTC(),
						Deleted:            time.Time{}, // Assuming not deleted
					},
				},
				[]*model.SEOData{
					{
						BusinessID:   "business123",
						Keyword:      "sample keyword",
						Date:         "2025-03-06",
						LocalRank:    3,
						OrganicRank:  7,
						Difficulty:   55,
						SearchVolume: 950,
						SearchRadius: 5.0,
						LocationInfo: &model.LocationInfo{
							LocationCode: 1,
							LocationName: "San Francisco",
						},
						LocalSearches:      []*model.LocalSearchData{},
						KeywordDataFetched: true,
						WorkflowURL:        "https://example.com/workflow",
						Created:            time.Now().AddDate(0, 0, -1).UTC(), // Previous day
						Updated:            time.Now().AddDate(0, 0, -1).UTC(), // Previous day
						Deleted:            time.Time{},                        // Assuming it's not deleted
					},
				},
				nil).Times(c.MockGetCurrentAndPreviousDataCount)
			m.keywordInfoService.EXPECT().GetMostRecentData(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
				Times(c.MockGetMostRecentDataCount)

			response, err := s.GetSEODataSummary(c.ctx, c.req)
			if err != nil {
				assert.Contains(t, c.error.Error(), err.Error())
			} else {
				assert.Equal(t, c.res, response)
			}
		})
	}
}

func TestGetLocalSearchSEOData(t *testing.T) {
	startTime, _ := time.Parse("2006-01-02", "2025-03-06")
	EndTime, _ := time.Parse("2006-01-02", "2025-03-07")
	type args struct {
		name                               string
		ctx                                context.Context
		req                                *listing_products_v1.GetLocalSearchSEODataRequest
		res                                *listing_products_v1.GetLocalSearchSEODataResponse
		error                              error
		MockGetCurrentAndPreviousDataCount int
		MockGetMostRecentDataCount         int
	}
	testcases := []args{
		{
			name: "success",
			ctx:  context.Background(),
			req: &listing_products_v1.GetLocalSearchSEODataRequest{
				BusinessId: "business123",
				Keyword:    "Dr Connor McDavid",
				StartDate:  timestamppb.New(startTime),
				EndDate:    timestamppb.New(EndTime),
			},
			res: &listing_products_v1.GetLocalSearchSEODataResponse{
				Keyword: "Dr Connor McDavid",
				LocalSearchData: []*listing_products_v1.LocalSearchData{ // Update type here
					{
						Keyword:  "Dr Connor McDavid",
						Vicinity: 14, //VICINITY C3
						SearchLocation: &listing_products_v1.Geo{
							Latitude:  52.265512488945184,
							Longitude: -106.90163834102636,
						},
						Results: []*listing_products_v1.LocalSearchResult{
							{
								BusinessName:   "Dr. Connor McDavid",
								Address:        "123 Main Street, City, State, Zip",
								Url:            "https://abc.pdqs.mobi/drconnormcdavid/",
								Rank:           "1",
								IsMainBusiness: true,
								Reviews: &listing_products_v1.LocalSearchReviews{
									Rating: 4,
									Count:  "28",
								},
								PhoneNumber: "+1306-653-6003",
							},
						},
					},
					{
						Keyword:  "Dr Connor McDavid",
						Vicinity: 15, //VICINITY C4
						SearchLocation: &listing_products_v1.Geo{
							Latitude:  52.265512488945184,
							Longitude: -106.90163834102636,
						},
						Results: []*listing_products_v1.LocalSearchResult{
							{
								BusinessName:   "Dr. Connor McDavid",
								Address:        "123 Main Street, City, State, Zip",
								Url:            "https://abc.pdqs.mobi/drconnormcdavid/",
								Rank:           "4",
								IsMainBusiness: true,
								Reviews: &listing_products_v1.LocalSearchReviews{
									Rating: 4,
									Count:  "28",
								},
								PhoneNumber: "+1306-653-6003",
							},
						},
					},
				},
			},
			error:                              nil,
			MockGetCurrentAndPreviousDataCount: 1,
		},
		{
			name: "Empty BusinessId",
			ctx:  context.Background(),
			req: &listing_products_v1.GetLocalSearchSEODataRequest{
				BusinessId: "",
				Keyword:    "Dr Connor McDavid",
				StartDate:  timestamppb.New(startTime),
				EndDate:    timestamppb.New(EndTime),
			},
			res:                                nil,
			error:                              errors.New("business_id is required"),
			MockGetCurrentAndPreviousDataCount: 0,
		},
	}
	for _, c := range testcases {
		t.Run(c.name, func(t *testing.T) {
			s, m, ctrl := setupSEOServer(t)
			defer ctrl.Finish()
			m.service.EXPECT().GetCurrentAndPreviousData(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(
				[]*model.SEOData{
					{
						Keyword: "Dr Connor McDavid",
						LocalSearches: []*model.LocalSearchData{
							{
								Keyword:  "Dr Connor McDavid",
								Vicinity: "VICINITY_C3",
								SearchLocation: &model.Geo{
									Latitude:  52.265512488945184,
									Longitude: -106.90163834102636,
								},
								Results: []*model.LocalSearchResult{
									{
										BusinessName:   "Dr. Connor McDavid",
										Address:        "123 Main Street, City, State, Zip",
										Url:            "https://abc.pdqs.mobi/drconnormcdavid/",
										Rank:           1,
										IsMainBusiness: true,
										Reviews: &model.LocalSearchReviews{
											Rating: 4,
											Count:  "28",
										},
										PhoneNumber: "+1306-653-6003",
										ClaimStatus: "Claimed",
									},
								},
							},
							{
								Keyword:  "Dr Connor McDavid",
								Vicinity: "VICINITY_C4",
								SearchLocation: &model.Geo{
									Latitude:  52.265512488945184,
									Longitude: -106.90163834102636,
								},
								Results: []*model.LocalSearchResult{
									{
										BusinessName:   "Dr. Connor McDavid",
										Address:        "123 Main Street, City, State, Zip",
										Url:            "https://abc.pdqs.mobi/drconnormcdavid/",
										Rank:           4,
										IsMainBusiness: true,
										Reviews: &model.LocalSearchReviews{
											Rating: 4,
											Count:  "28",
										},
										PhoneNumber: "+1306-653-6003",
										ClaimStatus: "Claimed",
									},
								},
							},
						},
					},
				}, nil, nil).
				Times(c.MockGetCurrentAndPreviousDataCount)
			resp, err := s.GetLocalSearchSEOData(c.ctx, c.req)
			if err != nil {
				assert.Contains(t, c.error.Error(), err.Error())
			} else {
				assert.Equal(t, c.res, resp)
			}

		})
	}
}

// Helper function to create sample AIOAuditResult for testing
func createSampleAIOAuditResult() *aioauditresult.AIOAuditResult {
	startDate := time.Date(2024, 1, 15, 10, 0, 0, 0, time.UTC)
	return &aioauditresult.AIOAuditResult{
		BusinessID:   "business123",
		BrandName:    "Test Business",
		WebsiteURL:   "https://testbusiness.com",
		AuditDate:    "2024-01-15",
		StartDate:    startDate,
		TotalPages:   5,
		AuditStatus:  "completed",
		AuditSummary: "Website audit completed successfully",
		AuditPages: []*aioauditresult.AuditPageData{
			{
				PageURL:  "https://testbusiness.com/",
				PageData: "Homepage audit data",
			},
			{
				PageURL:  "https://testbusiness.com/about",
				PageData: "About page audit data",
			},
		},
	}
}

// TestTriggerAIOAudit tests the TriggerAIOAudit endpoint
func TestTriggerAIOAudit(t *testing.T) {
	tests := []struct {
		name           string
		request        *listing_products_v1.TriggerAIOAuditRequest
		expectedError  string
		expectedResult *listing_products_v1.TriggerAIOAuditResponse
	}{
		{
			name: "successful audit trigger",
			request: &listing_products_v1.TriggerAIOAuditRequest{
				BusinessId: "business123",
				BrandName:  "Test Business",
				WebsiteUrl: "https://testbusiness.com",
			},
			expectedResult: &listing_products_v1.TriggerAIOAuditResponse{
				Message: "AIO audit workflow started successfully",
			},
		},
		{
			name: "missing business ID",
			request: &listing_products_v1.TriggerAIOAuditRequest{
				BusinessId: "",
				BrandName:  "Test Business",
				WebsiteUrl: "https://testbusiness.com",
			},
			expectedError: "business_id is required",
		},
		{
			name: "missing website",
			request: &listing_products_v1.TriggerAIOAuditRequest{
				BusinessId: "business123",
				BrandName:  "Test Business",
				WebsiteUrl: "",
			},
			expectedError: "website_url is required",
		},
		{
			name: "missing business name",
			request: &listing_products_v1.TriggerAIOAuditRequest{
				BusinessId: "business123",
				BrandName:  "",
				WebsiteUrl: "https://testbusiness.com",
			},
			expectedError: "brand_name is required",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			server, _, ctrl := setupSEOServer(t)
			defer ctrl.Finish()

			response, err := server.TriggerAIOAudit(context.Background(), tt.request)

			if tt.expectedError != "" {
				assert.Error(t, err)
				assert.Contains(t, err.Error(), tt.expectedError)
				assert.Nil(t, response)
			} else {
				assert.NoError(t, err)
				assert.Equal(t, tt.expectedResult, response)
			}
		})
	}
}

// TestGetAllAIOAudit tests the GetAllAIOAudit endpoint
func TestGetAllAIOAudit(t *testing.T) {
	tests := []struct {
		name           string
		request        *listing_products_v1.GetAllAIOAuditRequest
		expectedError  string
		expectedResult *listing_products_v1.GetAllAIOAuditResponse
	}{
		{
			name: "successful retrieval",
			request: &listing_products_v1.GetAllAIOAuditRequest{
				BusinessId: "business123",
			},
			expectedResult: &listing_products_v1.GetAllAIOAuditResponse{
				Audit: []*listing_products_v1.AIOAuditResults{},
			},
		},
		{
			name: "missing business ID",
			request: &listing_products_v1.GetAllAIOAuditRequest{
				BusinessId: "",
			},
			expectedError: "business_id is required",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			server, _, ctrl := setupSEOServer(t)
			defer ctrl.Finish()

			response, err := server.GetAllAIOAudit(context.Background(), tt.request)

			if tt.expectedError != "" {
				assert.Error(t, err)
				assert.Contains(t, err.Error(), tt.expectedError)
				assert.Nil(t, response)
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, response)
			}
		})
	}
}

// TestGetAIOAudit tests the GetAIOAudit endpoint
func TestGetAIOAudit(t *testing.T) {
	tests := []struct {
		name           string
		request        *listing_products_v1.GetAIOAuditRequest
		expectedError  string
		expectedResult *listing_products_v1.GetAIOAuditResponse
	}{
		{
			name: "successful retrieval",
			request: &listing_products_v1.GetAIOAuditRequest{
				BusinessId: "business123",
				BrandName:  "Test Business",
				WebsiteUrl: "https://testbusiness.com",
			},
			expectedResult: &listing_products_v1.GetAIOAuditResponse{
				Audit: &listing_products_v1.AIOAuditResults{
					BusinessId: "business123",
					BrandName:  "Test Business",
					WebsiteUrl: "https://testbusiness.com",
				},
			},
		},
		{
			name: "missing business ID",
			request: &listing_products_v1.GetAIOAuditRequest{
				BusinessId: "",
				BrandName:  "Test Business",
				WebsiteUrl: "https://testbusiness.com",
			},
			expectedError: "business_id is required",
		},
		{
			name: "missing website URL",
			request: &listing_products_v1.GetAIOAuditRequest{
				BusinessId: "business123",
				BrandName:  "Test Business",
				WebsiteUrl: "",
			},
			expectedError: "website_url is required",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			server, _, ctrl := setupSEOServer(t)
			defer ctrl.Finish()

			response, err := server.GetAIOAudit(context.Background(), tt.request)

			if tt.expectedError != "" {
				assert.Error(t, err)
				assert.Contains(t, err.Error(), tt.expectedError)
				assert.Nil(t, response)
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, response)
			}
		})
	}
}

// TestGetAIOAuditStatus tests the GetAIOAuditStatus endpoint
func TestGetAIOAuditStatus(t *testing.T) {
	tests := []struct {
		name           string
		request        *listing_products_v1.GetAIOAuditStatusRequest
		expectedError  string
		expectedResult *listing_products_v1.GetAIOAuditStatusResponse
	}{
		{
			name: "successful status retrieval",
			request: &listing_products_v1.GetAIOAuditStatusRequest{
				BusinessId: "business123",
				BrandName:  "Test Business",
				WebsiteUrl: "https://testbusiness.com",
			},
			expectedResult: &listing_products_v1.GetAIOAuditStatusResponse{
				AuditStatus: "completed",
			},
		},
		{
			name: "missing business ID",
			request: &listing_products_v1.GetAIOAuditStatusRequest{
				BusinessId: "",
				BrandName:  "Test Business",
				WebsiteUrl: "https://testbusiness.com",
			},
			expectedError: "business_id is required",
		},
		{
			name: "missing website URL",
			request: &listing_products_v1.GetAIOAuditStatusRequest{
				BusinessId: "business123",
				BrandName:  "Test Business",
				WebsiteUrl: "",
			},
			expectedError: "website_url is required",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			server, _, ctrl := setupSEOServer(t)
			defer ctrl.Finish()

			response, err := server.GetAIOAuditStatus(context.Background(), tt.request)

			if tt.expectedError != "" {
				assert.Error(t, err)
				assert.Contains(t, err.Error(), tt.expectedError)
				assert.Nil(t, response)
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, response)
			}
		})
	}
}

// TestGetAllAIOAuditScoreResults tests the GetAllAIOAuditScoreResults endpoint
func TestGetAllAIOAuditScoreResults(t *testing.T) {
	tests := []struct {
		name           string
		request        *listing_products_v1.GetAllAIOAuditScoreResultsRequest
		expectedError  string
		expectedResult *listing_products_v1.GetAllAIOAuditScoreResultsResponse
	}{
		{
			name: "successful score results retrieval",
			request: &listing_products_v1.GetAllAIOAuditScoreResultsRequest{
				BusinessId: "business123",
				BrandName:  "Test Business",
				WebsiteUrl: "https://testbusiness.com",
			},
			expectedResult: &listing_products_v1.GetAllAIOAuditScoreResultsResponse{
				AuditScoreResults: []*listing_products_v1.AuditScoreResults{},
			},
		},
		{
			name: "missing business ID",
			request: &listing_products_v1.GetAllAIOAuditScoreResultsRequest{
				BusinessId: "",
				BrandName:  "Test Business",
				WebsiteUrl: "https://testbusiness.com",
			},
			expectedError: "business_id is required",
		},
		{
			name: "missing website URL",
			request: &listing_products_v1.GetAllAIOAuditScoreResultsRequest{
				BusinessId: "business123",
				BrandName:  "Test Business",
				WebsiteUrl: "",
			},
			expectedError: "website_url is required",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			server, _, ctrl := setupSEOServer(t)
			defer ctrl.Finish()

			response, err := server.GetAllAIOAuditScoreResults(context.Background(), tt.request)

			if tt.expectedError != "" {
				assert.Error(t, err)
				assert.Contains(t, err.Error(), tt.expectedError)
				assert.Nil(t, response)
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, response)
			}
		})
	}
}

// TestConvertAIOAuditResultToProto tests the convertAIOAuditResultToProto helper function
func TestConvertAIOAuditResultToProto(t *testing.T) {
	tests := []struct {
		name           string
		input          *aioauditresult.AIOAuditResult
		expectedError  string
		validateResult func(*testing.T, *listing_products_v1.AIOAuditResults)
	}{
		{
			name:  "nil input",
			input: nil,
			validateResult: func(t *testing.T, result *listing_products_v1.AIOAuditResults) {
				assert.Nil(t, result)
			},
		},
		{
			name:  "valid input",
			input: createSampleAIOAuditResult(),
			validateResult: func(t *testing.T, result *listing_products_v1.AIOAuditResults) {
				assert.NotNil(t, result)
				assert.Equal(t, "business123", result.BusinessId)
				assert.Equal(t, "Test Business", result.BrandName)
				assert.Equal(t, "https://testbusiness.com", result.WebsiteUrl)
				assert.Equal(t, "2024-01-15", result.AuditDate)
				assert.NotNil(t, result.StartDate)
				assert.Equal(t, int64(5), result.TotalPages)
				assert.Equal(t, "completed", result.AuditStatus)
				assert.Equal(t, "Website audit completed successfully", result.AuditSummary)
				assert.Len(t, result.AuditPages, 2)
				assert.Equal(t, "https://testbusiness.com/", result.AuditPages[0].PageUrl)
				assert.Equal(t, "Homepage audit data", result.AuditPages[0].PageData)
			},
		},
		{
			name: "empty audit result",
			input: &aioauditresult.AIOAuditResult{
				BusinessID: "business456",
				BrandName:  "Empty Business",
				WebsiteURL: "https://emptybusiness.com",
			},
			validateResult: func(t *testing.T, result *listing_products_v1.AIOAuditResults) {
				assert.NotNil(t, result)
				assert.Equal(t, "business456", result.BusinessId)
				assert.Equal(t, "Empty Business", result.BrandName)
				assert.Equal(t, "https://emptybusiness.com", result.WebsiteUrl)
				assert.Empty(t, result.AuditDate)
				assert.Nil(t, result.StartDate)
				assert.Equal(t, int64(0), result.TotalPages)
				assert.Empty(t, result.AuditStatus)
				assert.Empty(t, result.AuditSummary)
				assert.Empty(t, result.AuditPages)
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result, err := convertAIOAuditResultToProto(tt.input)

			if tt.expectedError != "" {
				assert.Error(t, err)
				assert.Contains(t, err.Error(), tt.expectedError)
			} else {
				assert.NoError(t, err)
				tt.validateResult(t, result)
			}
		})
	}
}
