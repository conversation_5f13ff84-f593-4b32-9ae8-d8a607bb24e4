package api

import (
	"context"
	"errors"
	"testing"
	"time"

	"github.com/golang/mock/gomock"
	"github.com/stretchr/testify/assert"
	listing_products_v1 "github.com/vendasta/generated-protos-go/listing_products/v1"
	dataforseocategoriesservice "github.com/vendasta/listing-products/internal/dataforseocategories/service"
	listingprofileservice "github.com/vendasta/listing-products/internal/listingprofile/service"
	aioauditresultservice "github.com/vendasta/listing-products/internal/seo/aioauditresult/service"
	keywordinfoservice "github.com/vendasta/listing-products/internal/seo/keywordinfo/service"
	model "github.com/vendasta/listing-products/internal/seo/model"
	seodataservice "github.com/vendasta/listing-products/internal/seo/service"
	seoworkflow "github.com/vendasta/listing-products/internal/seo/workflow"
	seosettingsservice "github.com/vendasta/listing-products/internal/seosettings/service"
	"google.golang.org/protobuf/types/known/timestamppb"
)

type mocksSEOServer struct {
	service                   *seodataservice.MockService
	seoSettingsService        *seosettingsservice.MockService
	listingProfileService     *listingprofileservice.MockInterface
	workflowService           *seoworkflow.MockSEOWorkflowServiceInterface
	keywordInfoService        *keywordinfoservice.MockService
	dataForSEOCategoryService *dataforseocategoriesservice.MockService
	aioAuditResultService     *aioauditresultservice.MockService
	aioAuditWorkflowService   *seoworkflow.MockAIOAuditService
}

func setupSEOServer(t *testing.T) (*SEOServer, mocksSEOServer, *gomock.Controller) {
	ctrl := gomock.NewController(t)
	m := mocksSEOServer{
		service:                   seodataservice.NewMockService(ctrl),
		seoSettingsService:        seosettingsservice.NewMockService(ctrl),
		listingProfileService:     listingprofileservice.NewMockInterface(ctrl),
		workflowService:           seoworkflow.NewMockSEOWorkflowServiceInterface(ctrl),
		keywordInfoService:        keywordinfoservice.NewMockService(ctrl),
		dataForSEOCategoryService: dataforseocategoriesservice.NewMockService(ctrl),
	}
	reader := &SEOServer{
		service:                   m.service,
		seoSettingsService:        m.seoSettingsService,
		listingProfileService:     m.listingProfileService,
		keywordInfoService:        m.keywordInfoService,
		dataForSEOCategoryService: m.dataForSEOCategoryService,
	}
	return reader, m, ctrl
}

func TestGetSEODataSummary(t *testing.T) {
	startTime, _ := time.Parse("2006-01-02", "2025-03-06")
	EndTime, _ := time.Parse("2006-01-02", "2025-03-07")
	type args struct {
		name                               string
		ctx                                context.Context
		req                                *listing_products_v1.GetSEODataSummaryRequest
		res                                *listing_products_v1.GetSEODataSummaryResponse
		error                              error
		MockGetCurrentAndPreviousDataCount int
		MockGetMostRecentDataCount         int
	}

	testcases := []args{
		{
			name: "success",
			ctx:  context.Background(),
			req: &listing_products_v1.GetSEODataSummaryRequest{
				BusinessId: "business123",
				Keywords:   []string{"sample keyword"},
				StartDate:  timestamppb.New(startTime),
				EndDate:    timestamppb.New(EndTime),
			},
			res: &listing_products_v1.GetSEODataSummaryResponse{
				Data: []*listing_products_v1.SEODataSummary{
					{
						Keyword:      "sample keyword",
						Date:         timestamppb.New(EndTime),
						LocalRank:    3,
						OrganicRank:  7,
						Difficulty:   55,
						SearchVolume: 950,
						SearchRadius: 5.0,
					},
				},
				PreviousData: []*listing_products_v1.SEODataSummary{
					{
						Keyword:      "sample keyword",
						Date:         timestamppb.New(startTime),
						LocalRank:    3,
						OrganicRank:  7,
						Difficulty:   55,
						SearchVolume: 950,
						SearchRadius: 5.0,
					},
				},
			},
			MockGetCurrentAndPreviousDataCount: 1,
			MockGetMostRecentDataCount:         1,
			error:                              nil,
		},
		{
			name: "Empty BusinessId",
			ctx:  context.Background(),
			req: &listing_products_v1.GetSEODataSummaryRequest{
				BusinessId: "",
				Keywords:   []string{"sample keyword"},
				StartDate:  timestamppb.New(startTime),
				EndDate:    timestamppb.New(EndTime),
			},
			res:                                &listing_products_v1.GetSEODataSummaryResponse{},
			MockGetCurrentAndPreviousDataCount: 0,
			MockGetMostRecentDataCount:         0,
			error:                              errors.New("business_id is required"),
		},
	}
	for _, c := range testcases {
		t.Run(c.name, func(t *testing.T) {
			s, m, ctrl := setupSEOServer(t)
			defer ctrl.Finish()
			m.service.EXPECT().GetCurrentAndPreviousData(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(
				[]*model.SEOData{
					{
						BusinessID:   "business123",
						Keyword:      "sample keyword",
						Date:         "2025-03-07",
						LocalRank:    3,
						OrganicRank:  7,
						Difficulty:   55,
						SearchVolume: 950,
						SearchRadius: 5,
						LocationInfo: &model.LocationInfo{
							LocationCode: 1,
							LocationName: "San Francisco",
						},
						LocalSearches:      []*model.LocalSearchData{},
						KeywordDataFetched: true,
						WorkflowURL:        "https://example.com/workflow",
						Created:            time.Now().UTC(),
						Updated:            time.Now().UTC(),
						Deleted:            time.Time{}, // Assuming not deleted
					},
				},
				[]*model.SEOData{
					{
						BusinessID:   "business123",
						Keyword:      "sample keyword",
						Date:         "2025-03-06",
						LocalRank:    3,
						OrganicRank:  7,
						Difficulty:   55,
						SearchVolume: 950,
						SearchRadius: 5.0,
						LocationInfo: &model.LocationInfo{
							LocationCode: 1,
							LocationName: "San Francisco",
						},
						LocalSearches:      []*model.LocalSearchData{},
						KeywordDataFetched: true,
						WorkflowURL:        "https://example.com/workflow",
						Created:            time.Now().AddDate(0, 0, -1).UTC(), // Previous day
						Updated:            time.Now().AddDate(0, 0, -1).UTC(), // Previous day
						Deleted:            time.Time{},                        // Assuming it's not deleted
					},
				},
				nil).Times(c.MockGetCurrentAndPreviousDataCount)
			m.keywordInfoService.EXPECT().GetMostRecentData(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
				Times(c.MockGetMostRecentDataCount)

			response, err := s.GetSEODataSummary(c.ctx, c.req)
			if err != nil {
				assert.Contains(t, c.error.Error(), err.Error())
			} else {
				assert.Equal(t, c.res, response)
			}
		})
	}
}

func TestGetLocalSearchSEOData(t *testing.T) {
	startTime, _ := time.Parse("2006-01-02", "2025-03-06")
	EndTime, _ := time.Parse("2006-01-02", "2025-03-07")
	type args struct {
		name                               string
		ctx                                context.Context
		req                                *listing_products_v1.GetLocalSearchSEODataRequest
		res                                *listing_products_v1.GetLocalSearchSEODataResponse
		error                              error
		MockGetCurrentAndPreviousDataCount int
		MockGetMostRecentDataCount         int
	}
	testcases := []args{
		{
			name: "success",
			ctx:  context.Background(),
			req: &listing_products_v1.GetLocalSearchSEODataRequest{
				BusinessId: "business123",
				Keyword:    "Dr Connor McDavid",
				StartDate:  timestamppb.New(startTime),
				EndDate:    timestamppb.New(EndTime),
			},
			res: &listing_products_v1.GetLocalSearchSEODataResponse{
				Keyword: "Dr Connor McDavid",
				LocalSearchData: []*listing_products_v1.LocalSearchData{ // Update type here
					{
						Keyword:  "Dr Connor McDavid",
						Vicinity: 14, //VICINITY C3
						SearchLocation: &listing_products_v1.Geo{
							Latitude:  52.265512488945184,
							Longitude: -106.90163834102636,
						},
						Results: []*listing_products_v1.LocalSearchResult{
							{
								BusinessName:   "Dr. Connor McDavid",
								Address:        "123 Main Street, City, State, Zip",
								Url:            "https://abc.pdqs.mobi/drconnormcdavid/",
								Rank:           "1",
								IsMainBusiness: true,
								Reviews: &listing_products_v1.LocalSearchReviews{
									Rating: 4,
									Count:  "28",
								},
								PhoneNumber: "+1306-653-6003",
							},
						},
					},
					{
						Keyword:  "Dr Connor McDavid",
						Vicinity: 15, //VICINITY C4
						SearchLocation: &listing_products_v1.Geo{
							Latitude:  52.265512488945184,
							Longitude: -106.90163834102636,
						},
						Results: []*listing_products_v1.LocalSearchResult{
							{
								BusinessName:   "Dr. Connor McDavid",
								Address:        "123 Main Street, City, State, Zip",
								Url:            "https://abc.pdqs.mobi/drconnormcdavid/",
								Rank:           "4",
								IsMainBusiness: true,
								Reviews: &listing_products_v1.LocalSearchReviews{
									Rating: 4,
									Count:  "28",
								},
								PhoneNumber: "+1306-653-6003",
							},
						},
					},
				},
			},
			error:                              nil,
			MockGetCurrentAndPreviousDataCount: 1,
		},
		{
			name: "Empty BusinessId",
			ctx:  context.Background(),
			req: &listing_products_v1.GetLocalSearchSEODataRequest{
				BusinessId: "",
				Keyword:    "Dr Connor McDavid",
				StartDate:  timestamppb.New(startTime),
				EndDate:    timestamppb.New(EndTime),
			},
			res:                                nil,
			error:                              errors.New("business_id is required"),
			MockGetCurrentAndPreviousDataCount: 0,
		},
	}
	for _, c := range testcases {
		t.Run(c.name, func(t *testing.T) {
			s, m, ctrl := setupSEOServer(t)
			defer ctrl.Finish()
			m.service.EXPECT().GetCurrentAndPreviousData(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(
				[]*model.SEOData{
					{
						Keyword: "Dr Connor McDavid",
						LocalSearches: []*model.LocalSearchData{
							{
								Keyword:  "Dr Connor McDavid",
								Vicinity: "VICINITY_C3",
								SearchLocation: &model.Geo{
									Latitude:  52.265512488945184,
									Longitude: -106.90163834102636,
								},
								Results: []*model.LocalSearchResult{
									{
										BusinessName:   "Dr. Connor McDavid",
										Address:        "123 Main Street, City, State, Zip",
										Url:            "https://abc.pdqs.mobi/drconnormcdavid/",
										Rank:           1,
										IsMainBusiness: true,
										Reviews: &model.LocalSearchReviews{
											Rating: 4,
											Count:  "28",
										},
										PhoneNumber: "+1306-653-6003",
										ClaimStatus: "Claimed",
									},
								},
							},
							{
								Keyword:  "Dr Connor McDavid",
								Vicinity: "VICINITY_C4",
								SearchLocation: &model.Geo{
									Latitude:  52.265512488945184,
									Longitude: -106.90163834102636,
								},
								Results: []*model.LocalSearchResult{
									{
										BusinessName:   "Dr. Connor McDavid",
										Address:        "123 Main Street, City, State, Zip",
										Url:            "https://abc.pdqs.mobi/drconnormcdavid/",
										Rank:           4,
										IsMainBusiness: true,
										Reviews: &model.LocalSearchReviews{
											Rating: 4,
											Count:  "28",
										},
										PhoneNumber: "+1306-653-6003",
										ClaimStatus: "Claimed",
									},
								},
							},
						},
					},
				}, nil, nil).
				Times(c.MockGetCurrentAndPreviousDataCount)
			resp, err := s.GetLocalSearchSEOData(c.ctx, c.req)
			if err != nil {
				assert.Contains(t, c.error.Error(), err.Error())
			} else {
				assert.Equal(t, c.res, resp)
			}

		})
	}
}
