# cloud.google.com/go v0.116.0
## explicit; go 1.21
cloud.google.com/go
cloud.google.com/go/civil
cloud.google.com/go/internal
cloud.google.com/go/internal/detect
cloud.google.com/go/internal/fields
cloud.google.com/go/internal/optional
cloud.google.com/go/internal/protostruct
cloud.google.com/go/internal/pubsub
cloud.google.com/go/internal/testutil
cloud.google.com/go/internal/trace
cloud.google.com/go/internal/uid
cloud.google.com/go/internal/version
# cloud.google.com/go/auth v0.11.0
## explicit; go 1.21
cloud.google.com/go/auth
cloud.google.com/go/auth/credentials
cloud.google.com/go/auth/credentials/idtoken
cloud.google.com/go/auth/credentials/impersonate
cloud.google.com/go/auth/credentials/internal/externalaccount
cloud.google.com/go/auth/credentials/internal/externalaccountuser
cloud.google.com/go/auth/credentials/internal/gdch
cloud.google.com/go/auth/credentials/internal/impersonate
cloud.google.com/go/auth/credentials/internal/stsexchange
cloud.google.com/go/auth/grpctransport
cloud.google.com/go/auth/httptransport
cloud.google.com/go/auth/internal
cloud.google.com/go/auth/internal/compute
cloud.google.com/go/auth/internal/credsfile
cloud.google.com/go/auth/internal/jwt
cloud.google.com/go/auth/internal/transport
cloud.google.com/go/auth/internal/transport/cert
# cloud.google.com/go/auth/oauth2adapt v0.2.6
## explicit; go 1.21
cloud.google.com/go/auth/oauth2adapt
# cloud.google.com/go/bigquery v1.64.0
## explicit; go 1.21
cloud.google.com/go/bigquery
cloud.google.com/go/bigquery/internal
cloud.google.com/go/bigquery/internal/query
cloud.google.com/go/bigquery/storage/apiv1
cloud.google.com/go/bigquery/storage/apiv1/storagepb
# cloud.google.com/go/compute/metadata v0.6.0
## explicit; go 1.21
cloud.google.com/go/compute/metadata
# cloud.google.com/go/container v1.42.0
## explicit; go 1.21
cloud.google.com/go/container/apiv1
cloud.google.com/go/container/apiv1/containerpb
cloud.google.com/go/container/internal
# cloud.google.com/go/datastore v1.20.0
## explicit; go 1.21
cloud.google.com/go/datastore
cloud.google.com/go/datastore/apiv1/datastorepb
cloud.google.com/go/datastore/internal
cloud.google.com/go/datastore/internal/gaepb
# cloud.google.com/go/iam v1.2.2
## explicit; go 1.21
cloud.google.com/go/iam
cloud.google.com/go/iam/apiv1/iampb
# cloud.google.com/go/logging v1.12.0
## explicit; go 1.21
cloud.google.com/go/logging
cloud.google.com/go/logging/apiv2
cloud.google.com/go/logging/apiv2/loggingpb
cloud.google.com/go/logging/internal
# cloud.google.com/go/longrunning v0.6.2
## explicit; go 1.21
cloud.google.com/go/longrunning
cloud.google.com/go/longrunning/autogen
cloud.google.com/go/longrunning/autogen/longrunningpb
# cloud.google.com/go/monitoring v1.21.2
## explicit; go 1.21
cloud.google.com/go/monitoring/apiv3
cloud.google.com/go/monitoring/apiv3/v2/monitoringpb
cloud.google.com/go/monitoring/internal
# cloud.google.com/go/pubsub v1.45.1
## explicit; go 1.21
cloud.google.com/go/pubsub
cloud.google.com/go/pubsub/apiv1
cloud.google.com/go/pubsub/apiv1/pubsubpb
cloud.google.com/go/pubsub/internal
cloud.google.com/go/pubsub/internal/distribution
cloud.google.com/go/pubsub/internal/scheduler
cloud.google.com/go/pubsub/pstest
# cloud.google.com/go/secretmanager v1.14.2
## explicit; go 1.21
cloud.google.com/go/secretmanager/apiv1
cloud.google.com/go/secretmanager/apiv1/secretmanagerpb
cloud.google.com/go/secretmanager/internal
# cloud.google.com/go/storage v1.43.0
## explicit; go 1.20
cloud.google.com/go/storage
cloud.google.com/go/storage/internal
cloud.google.com/go/storage/internal/apiv2
cloud.google.com/go/storage/internal/apiv2/storagepb
# cloud.google.com/go/trace v1.11.2
## explicit; go 1.21
cloud.google.com/go/trace/apiv2
cloud.google.com/go/trace/apiv2/tracepb
cloud.google.com/go/trace/internal
# contrib.go.opencensus.io/exporter/stackdriver v0.13.4
## explicit; go 1.12
contrib.go.opencensus.io/exporter/stackdriver
contrib.go.opencensus.io/exporter/stackdriver/monitoredresource
contrib.go.opencensus.io/exporter/stackdriver/monitoredresource/aws
contrib.go.opencensus.io/exporter/stackdriver/monitoredresource/gcp
contrib.go.opencensus.io/exporter/stackdriver/propagation
# github.com/DataDog/datadog-go/v5 v5.2.0
## explicit; go 1.13
github.com/DataDog/datadog-go/v5/statsd
# github.com/GoogleCloudPlatform/opentelemetry-operations-go/exporter/trace v1.21.0
## explicit; go 1.20
github.com/GoogleCloudPlatform/opentelemetry-operations-go/exporter/trace
# github.com/GoogleCloudPlatform/opentelemetry-operations-go/internal/resourcemapping v0.45.0
## explicit; go 1.20
github.com/GoogleCloudPlatform/opentelemetry-operations-go/internal/resourcemapping
# github.com/GoogleCloudPlatform/opentelemetry-operations-go/propagator v0.45.0
## explicit; go 1.20
github.com/GoogleCloudPlatform/opentelemetry-operations-go/propagator
# github.com/Microsoft/go-winio v0.5.1
## explicit; go 1.12
github.com/Microsoft/go-winio
github.com/Microsoft/go-winio/pkg/guid
# github.com/NYTimes/gziphandler v1.1.1
## explicit; go 1.11
github.com/NYTimes/gziphandler
# github.com/Pallinder/go-randomdata v1.2.0
## explicit
github.com/Pallinder/go-randomdata
# github.com/apache/arrow/go/v15 v15.0.2
## explicit; go 1.20
github.com/apache/arrow/go/v15/arrow
github.com/apache/arrow/go/v15/arrow/array
github.com/apache/arrow/go/v15/arrow/arrio
github.com/apache/arrow/go/v15/arrow/bitutil
github.com/apache/arrow/go/v15/arrow/decimal128
github.com/apache/arrow/go/v15/arrow/decimal256
github.com/apache/arrow/go/v15/arrow/encoded
github.com/apache/arrow/go/v15/arrow/endian
github.com/apache/arrow/go/v15/arrow/float16
github.com/apache/arrow/go/v15/arrow/internal
github.com/apache/arrow/go/v15/arrow/internal/debug
github.com/apache/arrow/go/v15/arrow/internal/dictutils
github.com/apache/arrow/go/v15/arrow/internal/flatbuf
github.com/apache/arrow/go/v15/arrow/ipc
github.com/apache/arrow/go/v15/arrow/memory
github.com/apache/arrow/go/v15/arrow/memory/internal/cgoalloc
github.com/apache/arrow/go/v15/arrow/memory/mallocator
github.com/apache/arrow/go/v15/internal/bitutils
github.com/apache/arrow/go/v15/internal/hashing
github.com/apache/arrow/go/v15/internal/json
github.com/apache/arrow/go/v15/internal/utils
# github.com/aws/aws-sdk-go v1.40.32
## explicit; go 1.11
github.com/aws/aws-sdk-go/aws
github.com/aws/aws-sdk-go/aws/awserr
github.com/aws/aws-sdk-go/aws/awsutil
github.com/aws/aws-sdk-go/aws/client
github.com/aws/aws-sdk-go/aws/client/metadata
github.com/aws/aws-sdk-go/aws/corehandlers
github.com/aws/aws-sdk-go/aws/credentials
github.com/aws/aws-sdk-go/aws/credentials/ec2rolecreds
github.com/aws/aws-sdk-go/aws/credentials/endpointcreds
github.com/aws/aws-sdk-go/aws/credentials/processcreds
github.com/aws/aws-sdk-go/aws/credentials/ssocreds
github.com/aws/aws-sdk-go/aws/credentials/stscreds
github.com/aws/aws-sdk-go/aws/csm
github.com/aws/aws-sdk-go/aws/defaults
github.com/aws/aws-sdk-go/aws/ec2metadata
github.com/aws/aws-sdk-go/aws/endpoints
github.com/aws/aws-sdk-go/aws/request
github.com/aws/aws-sdk-go/aws/session
github.com/aws/aws-sdk-go/aws/signer/v4
github.com/aws/aws-sdk-go/internal/context
github.com/aws/aws-sdk-go/internal/ini
github.com/aws/aws-sdk-go/internal/sdkio
github.com/aws/aws-sdk-go/internal/sdkmath
github.com/aws/aws-sdk-go/internal/sdkrand
github.com/aws/aws-sdk-go/internal/sdkuri
github.com/aws/aws-sdk-go/internal/shareddefaults
github.com/aws/aws-sdk-go/internal/strings
github.com/aws/aws-sdk-go/internal/sync/singleflight
github.com/aws/aws-sdk-go/private/protocol
github.com/aws/aws-sdk-go/private/protocol/json/jsonutil
github.com/aws/aws-sdk-go/private/protocol/jsonrpc
github.com/aws/aws-sdk-go/private/protocol/query
github.com/aws/aws-sdk-go/private/protocol/query/queryutil
github.com/aws/aws-sdk-go/private/protocol/rest
github.com/aws/aws-sdk-go/private/protocol/restjson
github.com/aws/aws-sdk-go/private/protocol/xml/xmlutil
github.com/aws/aws-sdk-go/service/sso
github.com/aws/aws-sdk-go/service/sso/ssoiface
github.com/aws/aws-sdk-go/service/sts
github.com/aws/aws-sdk-go/service/sts/stsiface
# github.com/cactus/go-statsd-client/v5 v5.0.0
## explicit; go 1.11
github.com/cactus/go-statsd-client/v5/statsd
# github.com/census-instrumentation/opencensus-proto v0.4.1
## explicit; go 1.18
github.com/census-instrumentation/opencensus-proto/gen-go/agent/common/v1
github.com/census-instrumentation/opencensus-proto/gen-go/metrics/v1
github.com/census-instrumentation/opencensus-proto/gen-go/resource/v1
# github.com/coreos/go-oidc v2.1.0+incompatible
## explicit
github.com/coreos/go-oidc
# github.com/davecgh/go-spew v1.1.1
## explicit
github.com/davecgh/go-spew/spew
# github.com/dgrijalva/jwt-go v3.2.0+incompatible
## explicit
github.com/dgrijalva/jwt-go
# github.com/dlclark/regexp2 v1.10.0
## explicit; go 1.13
github.com/dlclark/regexp2
github.com/dlclark/regexp2/syntax
# github.com/facebookgo/clock v0.0.0-20150410010913-600d898af40a
## explicit
github.com/facebookgo/clock
# github.com/felixge/httpsnoop v1.0.4
## explicit; go 1.13
github.com/felixge/httpsnoop
# github.com/go-logr/logr v1.4.2
## explicit; go 1.18
github.com/go-logr/logr
github.com/go-logr/logr/funcr
# github.com/go-logr/stdr v1.2.2
## explicit; go 1.16
github.com/go-logr/stdr
# github.com/goccy/go-json v0.10.2
## explicit; go 1.12
github.com/goccy/go-json
github.com/goccy/go-json/internal/decoder
github.com/goccy/go-json/internal/encoder
github.com/goccy/go-json/internal/encoder/vm
github.com/goccy/go-json/internal/encoder/vm_color
github.com/goccy/go-json/internal/encoder/vm_color_indent
github.com/goccy/go-json/internal/encoder/vm_indent
github.com/goccy/go-json/internal/errors
github.com/goccy/go-json/internal/runtime
# github.com/gogo/protobuf v1.3.2
## explicit; go 1.15
github.com/gogo/protobuf/jsonpb
github.com/gogo/protobuf/proto
github.com/gogo/protobuf/sortkeys
github.com/gogo/protobuf/types
# github.com/golang-jwt/jwt/v4 v4.5.0
## explicit; go 1.16
github.com/golang-jwt/jwt/v4
# github.com/golang/groupcache v0.0.0-20210331224755-41bb18bfe9da
## explicit
github.com/golang/groupcache/lru
# github.com/golang/mock v1.6.0
## explicit; go 1.11
github.com/golang/mock/gomock
# github.com/golang/protobuf v1.5.4
## explicit; go 1.17
github.com/golang/protobuf/descriptor
github.com/golang/protobuf/jsonpb
github.com/golang/protobuf/proto
github.com/golang/protobuf/protoc-gen-go/descriptor
github.com/golang/protobuf/ptypes
github.com/golang/protobuf/ptypes/any
github.com/golang/protobuf/ptypes/duration
github.com/golang/protobuf/ptypes/empty
github.com/golang/protobuf/ptypes/struct
github.com/golang/protobuf/ptypes/timestamp
github.com/golang/protobuf/ptypes/wrappers
# github.com/gomodule/redigo v2.0.0+incompatible
## explicit
github.com/gomodule/redigo/internal
github.com/gomodule/redigo/redis
# github.com/google/flatbuffers v23.5.26+incompatible
## explicit
github.com/google/flatbuffers/go
# github.com/google/go-cmp v0.7.0
## explicit; go 1.21
github.com/google/go-cmp/cmp
github.com/google/go-cmp/cmp/internal/diff
github.com/google/go-cmp/cmp/internal/flags
github.com/google/go-cmp/cmp/internal/function
github.com/google/go-cmp/cmp/internal/value
# github.com/google/s2a-go v0.1.8
## explicit; go 1.20
github.com/google/s2a-go
github.com/google/s2a-go/fallback
github.com/google/s2a-go/internal/authinfo
github.com/google/s2a-go/internal/handshaker
github.com/google/s2a-go/internal/handshaker/service
github.com/google/s2a-go/internal/proto/common_go_proto
github.com/google/s2a-go/internal/proto/s2a_context_go_proto
github.com/google/s2a-go/internal/proto/s2a_go_proto
github.com/google/s2a-go/internal/proto/v2/common_go_proto
github.com/google/s2a-go/internal/proto/v2/s2a_context_go_proto
github.com/google/s2a-go/internal/proto/v2/s2a_go_proto
github.com/google/s2a-go/internal/record
github.com/google/s2a-go/internal/record/internal/aeadcrypter
github.com/google/s2a-go/internal/record/internal/halfconn
github.com/google/s2a-go/internal/tokenmanager
github.com/google/s2a-go/internal/v2
github.com/google/s2a-go/internal/v2/certverifier
github.com/google/s2a-go/internal/v2/remotesigner
github.com/google/s2a-go/internal/v2/tlsconfigstore
github.com/google/s2a-go/retry
github.com/google/s2a-go/stream
# github.com/google/uuid v1.6.0
## explicit
github.com/google/uuid
# github.com/googleapis/enterprise-certificate-proxy v0.3.4
## explicit; go 1.19
github.com/googleapis/enterprise-certificate-proxy/client
github.com/googleapis/enterprise-certificate-proxy/client/util
# github.com/googleapis/gax-go/v2 v2.14.0
## explicit; go 1.21
github.com/googleapis/gax-go/v2
github.com/googleapis/gax-go/v2/apierror
github.com/googleapis/gax-go/v2/apierror/internal/proto
github.com/googleapis/gax-go/v2/callctx
github.com/googleapis/gax-go/v2/internal
github.com/googleapis/gax-go/v2/internallog
github.com/googleapis/gax-go/v2/internallog/internal
github.com/googleapis/gax-go/v2/iterator
# github.com/grpc-ecosystem/go-grpc-middleware v1.3.0
## explicit; go 1.14
github.com/grpc-ecosystem/go-grpc-middleware
github.com/grpc-ecosystem/go-grpc-middleware/retry
github.com/grpc-ecosystem/go-grpc-middleware/util/backoffutils
github.com/grpc-ecosystem/go-grpc-middleware/util/metautils
# github.com/grpc-ecosystem/grpc-gateway/v2 v2.27.1
## explicit; go 1.23.0
github.com/grpc-ecosystem/grpc-gateway/v2/internal/httprule
github.com/grpc-ecosystem/grpc-gateway/v2/runtime
github.com/grpc-ecosystem/grpc-gateway/v2/utilities
# github.com/iancoleman/strcase v0.2.0
## explicit; go 1.16
github.com/iancoleman/strcase
# github.com/jmespath/go-jmespath v0.4.0
## explicit; go 1.14
github.com/jmespath/go-jmespath
# github.com/jpillora/backoff v1.0.0
## explicit; go 1.13
github.com/jpillora/backoff
# github.com/klauspost/compress v1.16.7
## explicit; go 1.18
github.com/klauspost/compress
github.com/klauspost/compress/fse
github.com/klauspost/compress/huff0
github.com/klauspost/compress/internal/cpuinfo
github.com/klauspost/compress/internal/snapref
github.com/klauspost/compress/zstd
github.com/klauspost/compress/zstd/internal/xxhash
# github.com/klauspost/cpuid/v2 v2.2.5
## explicit; go 1.15
github.com/klauspost/cpuid/v2
# github.com/kr/fs v0.1.0
## explicit
github.com/kr/fs
# github.com/lestrrat/go-jwx v0.0.0-20180221005942-b7d4802280ae
## explicit
github.com/lestrrat/go-jwx/internal/base64
github.com/lestrrat/go-jwx/jwa
github.com/lestrrat/go-jwx/jwk
# github.com/lestrrat/go-pdebug v0.0.0-20180220043741-569c97477ae8
## explicit
github.com/lestrrat/go-pdebug
# github.com/mattheath/base62 v0.0.0-20150408093626-b80cdc656a7a
## explicit
github.com/mattheath/base62
# github.com/mattheath/kala v0.0.0-20171219141654-d6276794bf0e
## explicit
github.com/mattheath/kala/bigflake
github.com/mattheath/kala/util
# github.com/nyaruka/phonenumbers v1.1.7
## explicit; go 1.18
github.com/nyaruka/phonenumbers
# github.com/pborman/uuid v1.2.1
## explicit
github.com/pborman/uuid
# github.com/pierrec/lz4/v4 v4.1.18
## explicit; go 1.14
github.com/pierrec/lz4/v4
github.com/pierrec/lz4/v4/internal/lz4block
github.com/pierrec/lz4/v4/internal/lz4errors
github.com/pierrec/lz4/v4/internal/lz4stream
github.com/pierrec/lz4/v4/internal/xxh32
# github.com/pkg/errors v0.9.1
## explicit
github.com/pkg/errors
# github.com/pkg/sftp v1.13.1
## explicit; go 1.15
github.com/pkg/sftp
# github.com/pkoukk/tiktoken-go v0.1.6
## explicit; go 1.19
github.com/pkoukk/tiktoken-go
# github.com/pkoukk/tiktoken-go-loader v0.0.1
## explicit; go 1.20
github.com/pkoukk/tiktoken-go-loader
github.com/pkoukk/tiktoken-go-loader/assets
# github.com/pmezard/go-difflib v1.0.0
## explicit
github.com/pmezard/go-difflib/difflib
# github.com/pquerna/cachecontrol v0.0.0-20180517163645-1555304b9b35
## explicit
github.com/pquerna/cachecontrol
github.com/pquerna/cachecontrol/cacheobject
# github.com/robfig/cron v1.2.0
## explicit
github.com/robfig/cron
# github.com/sashabaranov/go-openai v1.24.0
## explicit; go 1.18
github.com/sashabaranov/go-openai
github.com/sashabaranov/go-openai/internal
# github.com/short-hop/vmockhelper v1.2.1
## explicit; go 1.12
github.com/short-hop/vmockhelper
# github.com/short-hop/vrender v1.2.7
## explicit; go 1.17
github.com/short-hop/vrender
# github.com/soheilhy/cmux v0.1.4
## explicit
github.com/soheilhy/cmux
# github.com/stretchr/objx v0.5.2
## explicit; go 1.20
github.com/stretchr/objx
# github.com/stretchr/testify v1.10.0
## explicit; go 1.17
github.com/stretchr/testify/assert
github.com/stretchr/testify/assert/yaml
github.com/stretchr/testify/mock
github.com/stretchr/testify/require
github.com/stretchr/testify/suite
# github.com/twmb/murmur3 v1.1.6
## explicit; go 1.11
github.com/twmb/murmur3
# github.com/uber-go/tally/v4 v4.1.1
## explicit; go 1.15
github.com/uber-go/tally/v4
github.com/uber-go/tally/v4/internal/identity
# github.com/vendasta/AA/sdks/go v0.0.0-20220927091309-18559c2ae2b4
## explicit; go 1.15
github.com/vendasta/AA/sdks/go/v1
# github.com/vendasta/CS/sdks/go v0.0.0-20240619214750-31178e92b3e2
## explicit; go 1.12
github.com/vendasta/CS/sdks/go/v1
# github.com/vendasta/IAM/sdks/go v1.12.0
## explicit; go 1.17
github.com/vendasta/IAM/sdks/go/iaminterceptor
github.com/vendasta/IAM/sdks/go/internal
github.com/vendasta/IAM/sdks/go/v1
github.com/vendasta/IAM/sdks/go/v1/attribute
github.com/vendasta/IAM/sdks/go/v1/externalid
github.com/vendasta/IAM/sdks/go/v1/mutation
github.com/vendasta/IAM/sdks/go/v1/resourceidentifiersbuilder
github.com/vendasta/IAM/sdks/go/v1/resources
github.com/vendasta/IAM/sdks/go/v1/structuredattributes
github.com/vendasta/IAM/sdks/go/v1/structuredattributes/internal
github.com/vendasta/IAM/sdks/go/v1/subject
github.com/vendasta/IAM/sdks/go/v1/subjectcontext
github.com/vendasta/IAM/sdks/go/v1/user
github.com/vendasta/IAM/sdks/go/v2
github.com/vendasta/IAM/sdks/go/v2/attribute
github.com/vendasta/IAM/sdks/go/v2/iamrole
github.com/vendasta/IAM/sdks/go/v2/internal
github.com/vendasta/IAM/sdks/go/v2/internal/requests/gettokenforuser
github.com/vendasta/IAM/sdks/go/v2/internal/requests/updateuser
github.com/vendasta/IAM/sdks/go/v2/internal/requests/updateuserrole
github.com/vendasta/IAM/sdks/go/v2/internal/useridentifier
github.com/vendasta/IAM/sdks/go/v2/policy
github.com/vendasta/IAM/sdks/go/v2/requests/accessresource
github.com/vendasta/IAM/sdks/go/v2/requests/createsession
github.com/vendasta/IAM/sdks/go/v2/requests/createuser
github.com/vendasta/IAM/sdks/go/v2/requests/emailverification
github.com/vendasta/IAM/sdks/go/v2/requests/getmultiusers
github.com/vendasta/IAM/sdks/go/v2/requests/gettokenforuser
github.com/vendasta/IAM/sdks/go/v2/requests/listsecuritylogs
github.com/vendasta/IAM/sdks/go/v2/requests/listuserrolesmetadata
github.com/vendasta/IAM/sdks/go/v2/requests/listusers
github.com/vendasta/IAM/sdks/go/v2/requests/updateuser
github.com/vendasta/IAM/sdks/go/v2/requests/updateuserrole
github.com/vendasta/IAM/sdks/go/v2/securitylog
github.com/vendasta/IAM/sdks/go/v2/user
github.com/vendasta/IAM/sdks/go/v2/useridentifier
github.com/vendasta/IAM/sdks/go/v2/userrole
github.com/vendasta/IAM/sdks/go/v2/userrolemetadata
# github.com/vendasta/NAP/sdks/go v0.0.0-**************-99092423f220
## explicit; go 1.12
github.com/vendasta/NAP/sdks/go/v1
# github.com/vendasta/VBC/sdks/go v0.0.0-**************-31eb75946fc2
## explicit; go 1.13
github.com/vendasta/VBC/sdks/go/v1
# github.com/vendasta/account-group-media/sdks/go v0.0.0-**************-ff379e20a6d8
## explicit; go 1.12
github.com/vendasta/account-group-media/sdks/go/v1
# github.com/vendasta/account-group/sdks/go v0.0.0-**************-d6efbab5cfba
## explicit; go 1.12
github.com/vendasta/account-group/sdks/go/v1
# github.com/vendasta/accounts/sdks/go v0.0.0-**************-841215dfd34e
## explicit; go 1.12
github.com/vendasta/accounts/sdks/go/v1
github.com/vendasta/accounts/sdks/go/v2
# github.com/vendasta/address v0.0.0-**************-cbadf2a0990a
## explicit; go 1.20
github.com/vendasta/address/sdks/go
# github.com/vendasta/advertising/sdks/go v0.0.0-**************-3b28c01d1cdf
## explicit; go 1.13
github.com/vendasta/advertising/sdks/go/v2
# github.com/vendasta/category v0.0.0-**************-c1aaad33bf0b
## explicit; go 1.16
github.com/vendasta/category/sdks/go/v1
# github.com/vendasta/event-broker/sdks/go v1.9.1
## explicit; go 1.19
github.com/vendasta/event-broker/sdks/go
# github.com/vendasta/generated-protos-go/account_group v0.0.0-**************-7e6564220889
## explicit; go 1.12
github.com/vendasta/generated-protos-go/account_group/v1
# github.com/vendasta/generated-protos-go/account_group_media v0.0.0-**************-c502fb6f89aa
## explicit; go 1.12
github.com/vendasta/generated-protos-go/account_group_media/v1
# github.com/vendasta/generated-protos-go/accounts v1.14.0
## explicit; go 1.12
github.com/vendasta/generated-protos-go/accounts/v1
# github.com/vendasta/generated-protos-go/accounts/v2 v2.0.0-**************-01f50fa2ca3f
## explicit; go 1.12
github.com/vendasta/generated-protos-go/accounts/v2
# github.com/vendasta/generated-protos-go/address v0.0.0-**************-890006e52cb5
## explicit; go 1.12
github.com/vendasta/generated-protos-go/address/v1
# github.com/vendasta/generated-protos-go/advertising v0.0.0-**************-f1e5e0c2c040
## explicit; go 1.12
github.com/vendasta/generated-protos-go/advertising/v1
# github.com/vendasta/generated-protos-go/auxiliary_data v0.4.0
## explicit; go 1.12
github.com/vendasta/generated-protos-go/auxiliary_data/v1
# github.com/vendasta/generated-protos-go/catalogue v0.2.0
## explicit; go 1.12
github.com/vendasta/generated-protos-go/catalogue/v1
# github.com/vendasta/generated-protos-go/category v1.42.0
## explicit; go 1.12
github.com/vendasta/generated-protos-go/category/v1
# github.com/vendasta/generated-protos-go/event_broker v1.0.0
## explicit; go 1.12
github.com/vendasta/generated-protos-go/event_broker/v1
# github.com/vendasta/generated-protos-go/group v1.7.0
## explicit; go 1.12
github.com/vendasta/generated-protos-go/group/v1
# github.com/vendasta/generated-protos-go/iam v1.54.0
## explicit; go 1.12
github.com/vendasta/generated-protos-go/iam/attributes
github.com/vendasta/generated-protos-go/iam/policies
github.com/vendasta/generated-protos-go/iam/v1
# github.com/vendasta/generated-protos-go/iam/v2 v2.48.0
## explicit; go 1.12
github.com/vendasta/generated-protos-go/iam/v2
# github.com/vendasta/generated-protos-go/libpostal v0.0.0-**************-c502fb6f89aa
## explicit; go 1.12
github.com/vendasta/generated-protos-go/libpostal/v1
# github.com/vendasta/generated-protos-go/listing_products v1.150.1-0.20250724080726-026a29336f1d
## explicit; go 1.24.0
github.com/vendasta/generated-protos-go/listing_products/v1
# github.com/vendasta/generated-protos-go/listing_score v0.0.0-20210118160909-5ae3381fbeba
## explicit; go 1.12
github.com/vendasta/generated-protos-go/listing_score/v1
# github.com/vendasta/generated-protos-go/listing_sync_pro v1.50.0
## explicit; go 1.12
github.com/vendasta/generated-protos-go/listing_sync_pro/v1
# github.com/vendasta/generated-protos-go/listing_syndication v1.9.0
## explicit; go 1.12
github.com/vendasta/generated-protos-go/listing_syndication/v1
# github.com/vendasta/generated-protos-go/marketplace_apps v0.0.0-20240725170513-1dd94f85684e
## explicit; go 1.12
github.com/vendasta/generated-protos-go/marketplace_apps/v1
# github.com/vendasta/generated-protos-go/marketplace_apps/v2 v2.90.0
## explicit; go 1.12
github.com/vendasta/generated-protos-go/marketplace_apps/v2
# github.com/vendasta/generated-protos-go/media/v2 v2.3.1
## explicit; go 1.12
github.com/vendasta/generated-protos-go/media/v2
# github.com/vendasta/generated-protos-go/multi_location_analytics v0.0.0-**************-c502fb6f89aa
## explicit; go 1.12
github.com/vendasta/generated-protos-go/multi_location_analytics/v1
# github.com/vendasta/generated-protos-go/nap v1.0.2-0.20230605143718-f2e09fb342ac
## explicit; go 1.12
github.com/vendasta/generated-protos-go/nap/v1
# github.com/vendasta/generated-protos-go/order_fulfillment v1.37.0
## explicit; go 1.12
github.com/vendasta/generated-protos-go/order_fulfillment/v1
# github.com/vendasta/generated-protos-go/partner v1.181.0
## explicit; go 1.12
github.com/vendasta/generated-protos-go/partner/v1
# github.com/vendasta/generated-protos-go/platform_integrations v0.37.0
## explicit; go 1.12
github.com/vendasta/generated-protos-go/platform_integrations/v1
# github.com/vendasta/generated-protos-go/snapshot v0.0.0-20230630171546-d099cad42974
## explicit; go 1.12
github.com/vendasta/generated-protos-go/snapshot/v1
# github.com/vendasta/generated-protos-go/social-posts/v2 v2.28.2-0.20240318220222-4f7c0f7da5e6
## explicit; go 1.12
github.com/vendasta/generated-protos-go/social-posts/v2
# github.com/vendasta/generated-protos-go/tesseract v0.0.0-20190718201450-36e297163915
## explicit; go 1.12
github.com/vendasta/generated-protos-go/tesseract/v1
# github.com/vendasta/generated-protos-go/vanalytics v0.3.0
## explicit; go 1.12
github.com/vendasta/generated-protos-go/vanalytics/v1
# github.com/vendasta/generated-protos-go/vendasta_types v1.11.0
## explicit; go 1.24.0
github.com/vendasta/generated-protos-go/vendasta_types
# github.com/vendasta/generated-protos-go/vetl v0.0.0-20190718201450-36e297163915
## explicit; go 1.12
github.com/vendasta/generated-protos-go/vetl/v1
# github.com/vendasta/generated-protos-go/vstore v1.9.0
## explicit; go 1.12
github.com/vendasta/generated-protos-go/vstore/v1
# github.com/vendasta/generated-protos-go/vstorepb v1.9.0
## explicit; go 1.12
github.com/vendasta/generated-protos-go/vstorepb
# github.com/vendasta/generated-protos-go/yext v1.15.0
## explicit; go 1.24.0
github.com/vendasta/generated-protos-go/yext/v1
# github.com/vendasta/gosdks/basesdk v1.6.0
## explicit; go 1.12
github.com/vendasta/gosdks/basesdk
# github.com/vendasta/gosdks/basesdk/v2 v2.2.0
## explicit; go 1.12
github.com/vendasta/gosdks/basesdk/v2
# github.com/vendasta/gosdks/bifrost v1.6.2
## explicit; go 1.15
github.com/vendasta/gosdks/bifrost
# github.com/vendasta/gosdks/cache v1.5.0
## explicit; go 1.12
github.com/vendasta/gosdks/cache
# github.com/vendasta/gosdks/catalogue v1.2.1-0.20220426191613-0a1650ac9094
## explicit; go 1.12
github.com/vendasta/gosdks/catalogue
# github.com/vendasta/gosdks/config v1.2.0
## explicit; go 1.12
github.com/vendasta/gosdks/config
# github.com/vendasta/gosdks/fieldmask v1.0.1-0.20200627144944-6107a58bddc7
## explicit; go 1.12
github.com/vendasta/gosdks/fieldmask
# github.com/vendasta/gosdks/logging v1.24.0
## explicit; go 1.20
github.com/vendasta/gosdks/logging
# github.com/vendasta/gosdks/openai v1.21.2
## explicit; go 1.18
github.com/vendasta/gosdks/openai
github.com/vendasta/gosdks/openai/jsonschema
# github.com/vendasta/gosdks/pubsub v1.8.2
## explicit; go 1.12
github.com/vendasta/gosdks/pubsub
# github.com/vendasta/gosdks/registration v1.2.0
## explicit; go 1.14
github.com/vendasta/gosdks/registration
# github.com/vendasta/gosdks/serverconfig v1.5.0
## explicit; go 1.12
github.com/vendasta/gosdks/serverconfig
# github.com/vendasta/gosdks/serverconfig/v2 v2.4.4
## explicit; go 1.14
github.com/vendasta/gosdks/serverconfig/v2
# github.com/vendasta/gosdks/statsd v1.7.2-0.20230502170606-760522ccf4ee
## explicit; go 1.18
github.com/vendasta/gosdks/statsd
# github.com/vendasta/gosdks/taskqueue v1.4.0
## explicit; go 1.12
github.com/vendasta/gosdks/taskqueue
# github.com/vendasta/gosdks/temporal v0.24.0
## explicit; go 1.21
github.com/vendasta/gosdks/temporal
# github.com/vendasta/gosdks/temporal/shared/bigquery v1.1.0
## explicit; go 1.18
github.com/vendasta/gosdks/temporal/shared/bigquery
# github.com/vendasta/gosdks/tracing v0.2.0
## explicit; go 1.17
github.com/vendasta/gosdks/tracing
# github.com/vendasta/gosdks/tracing/v2 v2.3.0
## explicit; go 1.20
github.com/vendasta/gosdks/tracing/v2
# github.com/vendasta/gosdks/validation v1.5.1
## explicit; go 1.12
github.com/vendasta/gosdks/validation
github.com/vendasta/gosdks/validation/rules
# github.com/vendasta/gosdks/vax v1.9.0
## explicit; go 1.12
github.com/vendasta/gosdks/vax
# github.com/vendasta/gosdks/vendastatypes v1.3.0
## explicit; go 1.18
github.com/vendasta/gosdks/vendastatypes
github.com/vendasta/gosdks/vendastatypes/internal/atomiccache
github.com/vendasta/gosdks/vendastatypes/internal/fields
github.com/vendasta/gosdks/vendastatypes/utils
# github.com/vendasta/gosdks/verrors v1.13.1
## explicit; go 1.15
github.com/vendasta/gosdks/verrors
# github.com/vendasta/gosdks/vstrings v1.3.0
## explicit; go 1.15
github.com/vendasta/gosdks/vstrings
# github.com/vendasta/group/sdks/go v0.0.0-20250414214155-a9d96753b8f3
## explicit; go 1.12
github.com/vendasta/group/sdks/go/v2
# github.com/vendasta/iam-resources v0.0.0-20250617074028-356d5941aac8
## explicit; go 1.22.2
github.com/vendasta/iam-resources/applications/listing-products
github.com/vendasta/iam-resources/common
github.com/vendasta/iam-resources/interfaces
# github.com/vendasta/libpostal/sdks/go v0.0.0-20200924041957-092eff5490aa
## explicit; go 1.13
github.com/vendasta/libpostal/sdks/go/v1
# github.com/vendasta/listing-score/sdks/go v0.0.0-20230413151117-b67a5bbb53f2
## explicit; go 1.13
github.com/vendasta/listing-score/sdks/go/v1
# github.com/vendasta/listing-sync-pro/sdks/go v0.0.0-20240705172033-0b5733a739f6
## explicit; go 1.17
github.com/vendasta/listing-sync-pro/sdks/go
# github.com/vendasta/listing-syndication/sdks/go v0.0.0-20240926165155-25ab106bd04d
## explicit; go 1.22.2
github.com/vendasta/listing-syndication/sdks/go
# github.com/vendasta/marketplace-apps/sdks/go v0.0.0-20241114195241-ba7d313ce0b7
## explicit; go 1.15
github.com/vendasta/marketplace-apps/sdks/go/v2
# github.com/vendasta/marketplace/sdks/go v0.0.0-20210329214509-80acac3712cf
## explicit; go 1.12
github.com/vendasta/marketplace/sdks/go/v1
github.com/vendasta/marketplace/sdks/go/v1/keys
github.com/vendasta/marketplace/sdks/go/v1/webhooks
# github.com/vendasta/media v0.0.0-20210823161819-0ea0c55fcfa0
## explicit; go 1.12
github.com/vendasta/media/sdks/go
# github.com/vendasta/multi-location-analytics/sdks/go v0.0.0-20200206143804-6b6559ce4e6c
## explicit; go 1.12
github.com/vendasta/multi-location-analytics/sdks/go/v1
# github.com/vendasta/order-fulfillment/sdks/go v0.0.0-20220426205227-17c83d948601
## explicit; go 1.12
github.com/vendasta/order-fulfillment/sdks/go
# github.com/vendasta/partner/sdks/go v0.0.0-20240519151153-3507cff96a64
## explicit; go 1.12
github.com/vendasta/partner/sdks/go/v1
# github.com/vendasta/platform-integrations v0.0.0-**************-1a4ff9871fd9
## explicit; go 1.22.2
github.com/vendasta/platform-integrations/sdks/go
# github.com/vendasta/snapshot v0.0.0-**************-f012b5a27a1d
## explicit; go 1.20
github.com/vendasta/snapshot/sdks/go
# github.com/vendasta/vanalytics v0.0.0-**************-17a46488f806
## explicit; go 1.20
github.com/vendasta/vanalytics/sdks/go
# github.com/vendasta/vendastaevents/account-group/go/business-profile-updated v0.2.0
## explicit; go 1.20
github.com/vendasta/vendastaevents/account-group/go/business-profile-updated
# github.com/vendasta/vendastaevents/account-group/go/updated v1.1.0
## explicit; go 1.18
github.com/vendasta/vendastaevents/account-group/go/updated
# github.com/vendasta/vendastaevents/eventdefinition/go v0.1.0
## explicit; go 1.15
github.com/vendasta/vendastaevents/eventdefinition/go
# github.com/vendasta/vendastaevents/listing-products/go/listingprofile v0.2.0
## explicit; go 1.15
github.com/vendasta/vendastaevents/listing-products/go/listingprofile
# github.com/vendasta/vendastaevents/snapshot/go/local-seo-data-updated v0.2.0
## explicit; go 1.15
github.com/vendasta/vendastaevents/snapshot/go/local-seo-data-updated
# github.com/vendasta/vendastaevents/snapshot/go/snapshotcreated v0.1.0
## explicit; go 1.15
github.com/vendasta/vendastaevents/snapshot/go/snapshotcreated
# github.com/vendasta/vetl/sdks/go v0.0.0-**************-4458561bb2a1
## explicit; go 1.12
github.com/vendasta/vetl/sdks/go/v1
github.com/vendasta/vetl/sdks/go/v1/schema
github.com/vendasta/vetl/sdks/go/v1/tesseract
# github.com/vendasta/vstore v1.35.0
## explicit; go 1.20
github.com/vendasta/vstore/vstore/sdks/go/v0
github.com/vendasta/vstore/vstore/sdks/go/v1
github.com/vendasta/vstore/vstore/sdks/go/v1/congestion_manager
github.com/vendasta/vstore/vstore/sdks/go/v1/pool
# github.com/vendasta/yext/sdks/go v0.0.0-20250203170753-29d12004e97d
## explicit; go 1.19
github.com/vendasta/yext/sdks/go
# github.com/youmark/pkcs8 v0.0.0-20191102193632-94c173a94d60
## explicit
github.com/youmark/pkcs8
# github.com/zeebo/xxh3 v1.0.2
## explicit; go 1.17
github.com/zeebo/xxh3
# go.einride.tech/aip v0.68.0
## explicit; go 1.21
go.einride.tech/aip/filtering
go.einride.tech/aip/filtering/exprs
# go.opencensus.io v0.24.0
## explicit; go 1.13
go.opencensus.io
go.opencensus.io/internal
go.opencensus.io/internal/tagencoding
go.opencensus.io/metric/metricdata
go.opencensus.io/metric/metricexport
go.opencensus.io/metric/metricproducer
go.opencensus.io/plugin/ocgrpc
go.opencensus.io/plugin/ochttp
go.opencensus.io/plugin/ochttp/propagation/b3
go.opencensus.io/resource
go.opencensus.io/resource/resourcekeys
go.opencensus.io/stats
go.opencensus.io/stats/internal
go.opencensus.io/stats/view
go.opencensus.io/tag
go.opencensus.io/trace
go.opencensus.io/trace/internal
go.opencensus.io/trace/propagation
go.opencensus.io/trace/tracestate
# go.opentelemetry.io/auto/sdk v1.1.0
## explicit; go 1.22.0
go.opentelemetry.io/auto/sdk
go.opentelemetry.io/auto/sdk/internal/telemetry
# go.opentelemetry.io/contrib/instrumentation/google.golang.org/grpc/otelgrpc v0.54.0
## explicit; go 1.21
go.opentelemetry.io/contrib/instrumentation/google.golang.org/grpc/otelgrpc
go.opentelemetry.io/contrib/instrumentation/google.golang.org/grpc/otelgrpc/internal
# go.opentelemetry.io/contrib/instrumentation/net/http/otelhttp v0.54.0
## explicit; go 1.21
go.opentelemetry.io/contrib/instrumentation/net/http/otelhttp
go.opentelemetry.io/contrib/instrumentation/net/http/otelhttp/internal/request
go.opentelemetry.io/contrib/instrumentation/net/http/otelhttp/internal/semconv
go.opentelemetry.io/contrib/instrumentation/net/http/otelhttp/internal/semconvutil
# go.opentelemetry.io/otel v1.35.0
## explicit; go 1.22.0
go.opentelemetry.io/otel
go.opentelemetry.io/otel/attribute
go.opentelemetry.io/otel/baggage
go.opentelemetry.io/otel/codes
go.opentelemetry.io/otel/internal
go.opentelemetry.io/otel/internal/attribute
go.opentelemetry.io/otel/internal/baggage
go.opentelemetry.io/otel/internal/global
go.opentelemetry.io/otel/propagation
go.opentelemetry.io/otel/semconv/v1.17.0
go.opentelemetry.io/otel/semconv/v1.20.0
go.opentelemetry.io/otel/semconv/v1.21.0
go.opentelemetry.io/otel/semconv/v1.26.0
# go.opentelemetry.io/otel/bridge/opencensus v1.24.0
## explicit; go 1.20
go.opentelemetry.io/otel/bridge/opencensus
go.opentelemetry.io/otel/bridge/opencensus/internal
go.opentelemetry.io/otel/bridge/opencensus/internal/oc2otel
go.opentelemetry.io/otel/bridge/opencensus/internal/ocmetric
go.opentelemetry.io/otel/bridge/opencensus/internal/otel2oc
# go.opentelemetry.io/otel/metric v1.35.0
## explicit; go 1.22.0
go.opentelemetry.io/otel/metric
go.opentelemetry.io/otel/metric/embedded
go.opentelemetry.io/otel/metric/noop
# go.opentelemetry.io/otel/sdk v1.35.0
## explicit; go 1.22.0
go.opentelemetry.io/otel/sdk
go.opentelemetry.io/otel/sdk/instrumentation
go.opentelemetry.io/otel/sdk/internal/env
go.opentelemetry.io/otel/sdk/internal/x
go.opentelemetry.io/otel/sdk/resource
go.opentelemetry.io/otel/sdk/trace
go.opentelemetry.io/otel/sdk/trace/tracetest
# go.opentelemetry.io/otel/sdk/metric v1.35.0
## explicit; go 1.22.0
go.opentelemetry.io/otel/sdk/metric
go.opentelemetry.io/otel/sdk/metric/exemplar
go.opentelemetry.io/otel/sdk/metric/internal
go.opentelemetry.io/otel/sdk/metric/internal/aggregate
go.opentelemetry.io/otel/sdk/metric/internal/x
go.opentelemetry.io/otel/sdk/metric/metricdata
# go.opentelemetry.io/otel/trace v1.35.0
## explicit; go 1.22.0
go.opentelemetry.io/otel/trace
go.opentelemetry.io/otel/trace/embedded
go.opentelemetry.io/otel/trace/internal/telemetry
go.opentelemetry.io/otel/trace/noop
# go.temporal.io/api v1.32.0
## explicit; go 1.20
go.temporal.io/api/batch/v1
go.temporal.io/api/command/v1
go.temporal.io/api/common/v1
go.temporal.io/api/enums/v1
go.temporal.io/api/errordetails/v1
go.temporal.io/api/failure/v1
go.temporal.io/api/filter/v1
go.temporal.io/api/history/v1
go.temporal.io/api/internal/protojson
go.temporal.io/api/internal/protojson/errors
go.temporal.io/api/internal/protojson/genid
go.temporal.io/api/internal/protojson/json
go.temporal.io/api/internal/protojson/order
go.temporal.io/api/internal/protojson/set
go.temporal.io/api/internal/protojson/strs
go.temporal.io/api/internal/strcase
go.temporal.io/api/namespace/v1
go.temporal.io/api/nexus/v1
go.temporal.io/api/operatorservice/v1
go.temporal.io/api/protocol/v1
go.temporal.io/api/proxy
go.temporal.io/api/query/v1
go.temporal.io/api/replication/v1
go.temporal.io/api/schedule/v1
go.temporal.io/api/sdk/v1
go.temporal.io/api/serviceerror
go.temporal.io/api/taskqueue/v1
go.temporal.io/api/temporalproto
go.temporal.io/api/update/v1
go.temporal.io/api/version/v1
go.temporal.io/api/workflow/v1
go.temporal.io/api/workflowservice/v1
go.temporal.io/api/workflowservicemock/v1
# go.temporal.io/sdk v1.26.1
## explicit; go 1.20
go.temporal.io/sdk/activity
go.temporal.io/sdk/client
go.temporal.io/sdk/converter
go.temporal.io/sdk/internal
go.temporal.io/sdk/internal/common/backoff
go.temporal.io/sdk/internal/common/cache
go.temporal.io/sdk/internal/common/metrics
go.temporal.io/sdk/internal/common/retry
go.temporal.io/sdk/internal/common/serializer
go.temporal.io/sdk/internal/common/util
go.temporal.io/sdk/internal/log
go.temporal.io/sdk/internal/protocol
go.temporal.io/sdk/log
go.temporal.io/sdk/temporal
go.temporal.io/sdk/testsuite
go.temporal.io/sdk/worker
go.temporal.io/sdk/workflow
# go.temporal.io/sdk/contrib/tally v0.1.0
## explicit; go 1.16
go.temporal.io/sdk/contrib/tally
# go.uber.org/atomic v1.9.0
## explicit; go 1.13
go.uber.org/atomic
# go.uber.org/mock v0.5.2
## explicit; go 1.23
go.uber.org/mock/gomock
# go.uber.org/multierr v1.8.0
## explicit; go 1.14
go.uber.org/multierr
# go.uber.org/zap v1.21.0
## explicit; go 1.13
go.uber.org/zap
go.uber.org/zap/buffer
go.uber.org/zap/internal/bufferpool
go.uber.org/zap/internal/color
go.uber.org/zap/internal/exit
go.uber.org/zap/zapcore
# golang.org/x/crypto v0.39.0
## explicit; go 1.23.0
golang.org/x/crypto/blowfish
golang.org/x/crypto/chacha20
golang.org/x/crypto/chacha20poly1305
golang.org/x/crypto/cryptobyte
golang.org/x/crypto/cryptobyte/asn1
golang.org/x/crypto/curve25519
golang.org/x/crypto/ed25519
golang.org/x/crypto/hkdf
golang.org/x/crypto/internal/alias
golang.org/x/crypto/internal/poly1305
golang.org/x/crypto/pbkdf2
golang.org/x/crypto/scrypt
golang.org/x/crypto/ssh
golang.org/x/crypto/ssh/internal/bcrypt_pbkdf
# golang.org/x/exp v0.0.0-20231127185646-65229373498e
## explicit; go 1.20
golang.org/x/exp/constraints
golang.org/x/exp/slices
# golang.org/x/mod v0.25.0
## explicit; go 1.23.0
golang.org/x/mod/internal/lazyregexp
golang.org/x/mod/module
golang.org/x/mod/semver
# golang.org/x/net v0.41.0
## explicit; go 1.23.0
golang.org/x/net/context
golang.org/x/net/http/httpguts
golang.org/x/net/http2
golang.org/x/net/http2/hpack
golang.org/x/net/idna
golang.org/x/net/internal/httpcommon
golang.org/x/net/internal/timeseries
golang.org/x/net/trace
# golang.org/x/oauth2 v0.30.0
## explicit; go 1.23.0
golang.org/x/oauth2
golang.org/x/oauth2/authhandler
golang.org/x/oauth2/google
golang.org/x/oauth2/google/externalaccount
golang.org/x/oauth2/google/internal/externalaccountauthorizeduser
golang.org/x/oauth2/google/internal/impersonate
golang.org/x/oauth2/google/internal/stsexchange
golang.org/x/oauth2/internal
golang.org/x/oauth2/jws
golang.org/x/oauth2/jwt
# golang.org/x/sync v0.15.0
## explicit; go 1.23.0
golang.org/x/sync/errgroup
golang.org/x/sync/semaphore
# golang.org/x/sys v0.33.0
## explicit; go 1.23.0
golang.org/x/sys/cpu
golang.org/x/sys/unix
golang.org/x/sys/windows
golang.org/x/sys/windows/registry
# golang.org/x/text v0.26.0
## explicit; go 1.23.0
golang.org/x/text/internal/format
golang.org/x/text/internal/language
golang.org/x/text/internal/language/compact
golang.org/x/text/internal/tag
golang.org/x/text/language
golang.org/x/text/language/display
golang.org/x/text/secure/bidirule
golang.org/x/text/transform
golang.org/x/text/unicode/bidi
golang.org/x/text/unicode/norm
# golang.org/x/time v0.8.0
## explicit; go 1.18
golang.org/x/time/rate
# golang.org/x/tools v0.33.0
## explicit; go 1.23.0
golang.org/x/tools/cmd/goimports
golang.org/x/tools/cmd/stringer
golang.org/x/tools/go/ast/astutil
golang.org/x/tools/go/gcexportdata
golang.org/x/tools/go/packages
golang.org/x/tools/go/types/objectpath
golang.org/x/tools/go/types/typeutil
golang.org/x/tools/internal/aliases
golang.org/x/tools/internal/event
golang.org/x/tools/internal/event/core
golang.org/x/tools/internal/event/keys
golang.org/x/tools/internal/event/label
golang.org/x/tools/internal/gcimporter
golang.org/x/tools/internal/gocommand
golang.org/x/tools/internal/gopathwalk
golang.org/x/tools/internal/imports
golang.org/x/tools/internal/modindex
golang.org/x/tools/internal/packagesinternal
golang.org/x/tools/internal/pkgbits
golang.org/x/tools/internal/stdlib
golang.org/x/tools/internal/typeparams
golang.org/x/tools/internal/typesinternal
golang.org/x/tools/internal/versions
# golang.org/x/xerrors v0.0.0-20240903120638-7835f813f4da
## explicit; go 1.18
golang.org/x/xerrors
golang.org/x/xerrors/internal
# google.golang.org/api v0.210.0
## explicit; go 1.21
google.golang.org/api/bigquery/v2
google.golang.org/api/cloudtasks/v2beta2
google.golang.org/api/cloudtasks/v2beta3
google.golang.org/api/googleapi
google.golang.org/api/googleapi/transport
google.golang.org/api/iamcredentials/v1
google.golang.org/api/idtoken
google.golang.org/api/impersonate
google.golang.org/api/internal
google.golang.org/api/internal/cert
google.golang.org/api/internal/gensupport
google.golang.org/api/internal/impersonate
google.golang.org/api/internal/third_party/uritemplates
google.golang.org/api/iterator
google.golang.org/api/mybusinessbusinessinformation/v1
google.golang.org/api/mybusinessnotifications/v1
google.golang.org/api/oauth2/v2
google.golang.org/api/option
google.golang.org/api/option/internaloption
google.golang.org/api/storage/v1
google.golang.org/api/support/bundler
google.golang.org/api/transport
google.golang.org/api/transport/grpc
google.golang.org/api/transport/http
google.golang.org/api/transport/http/internal/propagation
# google.golang.org/genproto v0.0.0-20241118233622-e639e219e697
## explicit; go 1.21
google.golang.org/genproto/googleapis/cloud/location
google.golang.org/genproto/googleapis/container/v1
google.golang.org/genproto/googleapis/devtools/cloudtrace/v2
google.golang.org/genproto/googleapis/logging/type
google.golang.org/genproto/googleapis/logging/v2
google.golang.org/genproto/googleapis/monitoring/v3
google.golang.org/genproto/googleapis/type/calendarperiod
google.golang.org/genproto/googleapis/type/date
google.golang.org/genproto/googleapis/type/dayofweek
google.golang.org/genproto/googleapis/type/expr
google.golang.org/genproto/googleapis/type/latlng
# google.golang.org/genproto/googleapis/api v0.0.0-20250715232539-7130f93afb79
## explicit; go 1.23.0
google.golang.org/genproto/googleapis/api
google.golang.org/genproto/googleapis/api/annotations
google.golang.org/genproto/googleapis/api/distribution
google.golang.org/genproto/googleapis/api/expr/v1alpha1
google.golang.org/genproto/googleapis/api/httpbody
google.golang.org/genproto/googleapis/api/label
google.golang.org/genproto/googleapis/api/metric
google.golang.org/genproto/googleapis/api/monitoredres
# google.golang.org/genproto/googleapis/rpc v0.0.0-20250715232539-7130f93afb79
## explicit; go 1.23.0
google.golang.org/genproto/googleapis/rpc/code
google.golang.org/genproto/googleapis/rpc/errdetails
google.golang.org/genproto/googleapis/rpc/status
# google.golang.org/grpc v1.73.0
## explicit; go 1.23.0
google.golang.org/grpc
google.golang.org/grpc/attributes
google.golang.org/grpc/backoff
google.golang.org/grpc/balancer
google.golang.org/grpc/balancer/base
google.golang.org/grpc/balancer/endpointsharding
google.golang.org/grpc/balancer/grpclb
google.golang.org/grpc/balancer/grpclb/grpc_lb_v1
google.golang.org/grpc/balancer/grpclb/state
google.golang.org/grpc/balancer/pickfirst
google.golang.org/grpc/balancer/pickfirst/internal
google.golang.org/grpc/balancer/pickfirst/pickfirstleaf
google.golang.org/grpc/balancer/roundrobin
google.golang.org/grpc/binarylog/grpc_binarylog_v1
google.golang.org/grpc/channelz
google.golang.org/grpc/channelz/grpc_channelz_v1
google.golang.org/grpc/channelz/internal/protoconv
google.golang.org/grpc/channelz/service
google.golang.org/grpc/codes
google.golang.org/grpc/connectivity
google.golang.org/grpc/credentials
google.golang.org/grpc/credentials/alts
google.golang.org/grpc/credentials/alts/internal
google.golang.org/grpc/credentials/alts/internal/authinfo
google.golang.org/grpc/credentials/alts/internal/conn
google.golang.org/grpc/credentials/alts/internal/handshaker
google.golang.org/grpc/credentials/alts/internal/handshaker/service
google.golang.org/grpc/credentials/alts/internal/proto/grpc_gcp
google.golang.org/grpc/credentials/google
google.golang.org/grpc/credentials/insecure
google.golang.org/grpc/credentials/oauth
google.golang.org/grpc/encoding
google.golang.org/grpc/encoding/gzip
google.golang.org/grpc/encoding/proto
google.golang.org/grpc/experimental/stats
google.golang.org/grpc/grpclog
google.golang.org/grpc/grpclog/internal
google.golang.org/grpc/health/grpc_health_v1
google.golang.org/grpc/internal
google.golang.org/grpc/internal/backoff
google.golang.org/grpc/internal/balancer/gracefulswitch
google.golang.org/grpc/internal/balancerload
google.golang.org/grpc/internal/binarylog
google.golang.org/grpc/internal/buffer
google.golang.org/grpc/internal/channelz
google.golang.org/grpc/internal/credentials
google.golang.org/grpc/internal/envconfig
google.golang.org/grpc/internal/googlecloud
google.golang.org/grpc/internal/grpclog
google.golang.org/grpc/internal/grpcsync
google.golang.org/grpc/internal/grpcutil
google.golang.org/grpc/internal/idle
google.golang.org/grpc/internal/metadata
google.golang.org/grpc/internal/pretty
google.golang.org/grpc/internal/proxyattributes
google.golang.org/grpc/internal/resolver
google.golang.org/grpc/internal/resolver/delegatingresolver
google.golang.org/grpc/internal/resolver/dns
google.golang.org/grpc/internal/resolver/dns/internal
google.golang.org/grpc/internal/resolver/passthrough
google.golang.org/grpc/internal/resolver/unix
google.golang.org/grpc/internal/serviceconfig
google.golang.org/grpc/internal/stats
google.golang.org/grpc/internal/status
google.golang.org/grpc/internal/syscall
google.golang.org/grpc/internal/transport
google.golang.org/grpc/internal/transport/networktype
google.golang.org/grpc/internal/xds
google.golang.org/grpc/keepalive
google.golang.org/grpc/mem
google.golang.org/grpc/metadata
google.golang.org/grpc/peer
google.golang.org/grpc/resolver
google.golang.org/grpc/resolver/dns
google.golang.org/grpc/resolver/manual
google.golang.org/grpc/serviceconfig
google.golang.org/grpc/stats
google.golang.org/grpc/status
google.golang.org/grpc/tap
# google.golang.org/protobuf v1.36.6
## explicit; go 1.22
google.golang.org/protobuf/encoding/protojson
google.golang.org/protobuf/encoding/prototext
google.golang.org/protobuf/encoding/protowire
google.golang.org/protobuf/internal/descfmt
google.golang.org/protobuf/internal/descopts
google.golang.org/protobuf/internal/detrand
google.golang.org/protobuf/internal/editiondefaults
google.golang.org/protobuf/internal/editionssupport
google.golang.org/protobuf/internal/encoding/defval
google.golang.org/protobuf/internal/encoding/json
google.golang.org/protobuf/internal/encoding/messageset
google.golang.org/protobuf/internal/encoding/tag
google.golang.org/protobuf/internal/encoding/text
google.golang.org/protobuf/internal/errors
google.golang.org/protobuf/internal/filedesc
google.golang.org/protobuf/internal/filetype
google.golang.org/protobuf/internal/flags
google.golang.org/protobuf/internal/genid
google.golang.org/protobuf/internal/impl
google.golang.org/protobuf/internal/order
google.golang.org/protobuf/internal/pragma
google.golang.org/protobuf/internal/protolazy
google.golang.org/protobuf/internal/set
google.golang.org/protobuf/internal/strs
google.golang.org/protobuf/internal/version
google.golang.org/protobuf/proto
google.golang.org/protobuf/protoadapt
google.golang.org/protobuf/reflect/protodesc
google.golang.org/protobuf/reflect/protoreflect
google.golang.org/protobuf/reflect/protoregistry
google.golang.org/protobuf/runtime/protoiface
google.golang.org/protobuf/runtime/protoimpl
google.golang.org/protobuf/types/descriptorpb
google.golang.org/protobuf/types/gofeaturespb
google.golang.org/protobuf/types/known/anypb
google.golang.org/protobuf/types/known/durationpb
google.golang.org/protobuf/types/known/emptypb
google.golang.org/protobuf/types/known/fieldmaskpb
google.golang.org/protobuf/types/known/structpb
google.golang.org/protobuf/types/known/timestamppb
google.golang.org/protobuf/types/known/wrapperspb
# googlemaps.github.io/maps v1.3.2
## explicit; go 1.14
googlemaps.github.io/maps
googlemaps.github.io/maps/internal
googlemaps.github.io/maps/metrics
# gopkg.in/square/go-jose.v2 v2.4.1
## explicit
gopkg.in/square/go-jose.v2
gopkg.in/square/go-jose.v2/cipher
gopkg.in/square/go-jose.v2/json
# gopkg.in/yaml.v3 v3.0.1
## explicit
gopkg.in/yaml.v3
# software.sslmate.com/src/go-pkcs12 v0.0.0-20190322163127-6e380ad96778
## explicit
software.sslmate.com/src/go-pkcs12
software.sslmate.com/src/go-pkcs12/internal/rc2
