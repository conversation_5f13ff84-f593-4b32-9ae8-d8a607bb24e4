// Copyright 2019 Google LLC.
// Use of this source code is governed by a BSD-style
// license that can be found in the LICENSE file.

// Package transport provides utility methods for creating authenticated
// transports to Google's HTTP and gRPC APIs. It is intended to be used in
// conjunction with google.golang.org/api/option.
//
// This package is not intended for use by end developers. Use the
// google.golang.org/api/option package to configure API clients.
package transport
