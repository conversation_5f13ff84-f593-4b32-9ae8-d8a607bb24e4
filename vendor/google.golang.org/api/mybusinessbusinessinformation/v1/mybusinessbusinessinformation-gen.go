// Copyright 2024 Google LLC.
// Use of this source code is governed by a BSD-style
// license that can be found in the LICENSE file.

// Code generated file. DO NOT EDIT.

// Package mybusinessbusinessinformation provides access to the My Business Business Information API.
//
// For product documentation, see: https://developers.google.com/my-business/
//
// # Library status
//
// These client libraries are officially supported by Google. However, this
// library is considered complete and is in maintenance mode. This means
// that we will address critical bugs and security issues but will not add
// any new features.
//
// When possible, we recommend using our newer
// [Cloud Client Libraries for Go](https://pkg.go.dev/cloud.google.com/go)
// that are still actively being worked and iterated on.
//
// # Creating a client
//
// Usage example:
//
//	import "google.golang.org/api/mybusinessbusinessinformation/v1"
//	...
//	ctx := context.Background()
//	mybusinessbusinessinformationService, err := mybusinessbusinessinformation.NewService(ctx)
//
// In this example, Google Application Default Credentials are used for
// authentication. For information on how to create and obtain Application
// Default Credentials, see https://developers.google.com/identity/protocols/application-default-credentials.
//
// # Other authentication options
//
// To use an API key for authentication (note: some APIs do not support API
// keys), use [google.golang.org/api/option.WithAPIKey]:
//
//	mybusinessbusinessinformationService, err := mybusinessbusinessinformation.NewService(ctx, option.WithAPIKey("AIza..."))
//
// To use an OAuth token (e.g., a user token obtained via a three-legged OAuth
// flow, use [google.golang.org/api/option.WithTokenSource]:
//
//	config := &oauth2.Config{...}
//	// ...
//	token, err := config.Exchange(ctx, ...)
//	mybusinessbusinessinformationService, err := mybusinessbusinessinformation.NewService(ctx, option.WithTokenSource(config.TokenSource(ctx, token)))
//
// See [google.golang.org/api/option.ClientOption] for details on options.
package mybusinessbusinessinformation // import "google.golang.org/api/mybusinessbusinessinformation/v1"

import (
	"bytes"
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"strconv"
	"strings"

	googleapi "google.golang.org/api/googleapi"
	internal "google.golang.org/api/internal"
	gensupport "google.golang.org/api/internal/gensupport"
	option "google.golang.org/api/option"
	internaloption "google.golang.org/api/option/internaloption"
	htransport "google.golang.org/api/transport/http"
)

// Always reference these packages, just in case the auto-generated code
// below doesn't.
var _ = bytes.NewBuffer
var _ = strconv.Itoa
var _ = fmt.Sprintf
var _ = json.NewDecoder
var _ = io.Copy
var _ = url.Parse
var _ = gensupport.MarshalJSON
var _ = googleapi.Version
var _ = errors.New
var _ = strings.Replace
var _ = context.Canceled
var _ = internaloption.WithDefaultEndpoint
var _ = internal.Version

const apiId = "mybusinessbusinessinformation:v1"
const apiName = "mybusinessbusinessinformation"
const apiVersion = "v1"
const basePath = "https://mybusinessbusinessinformation.googleapis.com/"
const basePathTemplate = "https://mybusinessbusinessinformation.UNIVERSE_DOMAIN/"
const mtlsBasePath = "https://mybusinessbusinessinformation.mtls.googleapis.com/"

// NewService creates a new Service.
func NewService(ctx context.Context, opts ...option.ClientOption) (*Service, error) {
	opts = append(opts, internaloption.WithDefaultEndpoint(basePath))
	opts = append(opts, internaloption.WithDefaultEndpointTemplate(basePathTemplate))
	opts = append(opts, internaloption.WithDefaultMTLSEndpoint(mtlsBasePath))
	opts = append(opts, internaloption.EnableNewAuthLibrary())
	client, endpoint, err := htransport.NewClient(ctx, opts...)
	if err != nil {
		return nil, err
	}
	s, err := New(client)
	if err != nil {
		return nil, err
	}
	if endpoint != "" {
		s.BasePath = endpoint
	}
	return s, nil
}

// New creates a new Service. It uses the provided http.Client for requests.
//
// Deprecated: please use NewService instead.
// To provide a custom HTTP client, use option.WithHTTPClient.
// If you are using google.golang.org/api/googleapis/transport.APIKey, use option.WithAPIKey with NewService instead.
func New(client *http.Client) (*Service, error) {
	if client == nil {
		return nil, errors.New("client is nil")
	}
	s := &Service{client: client, BasePath: basePath}
	s.Accounts = NewAccountsService(s)
	s.Attributes = NewAttributesService(s)
	s.Categories = NewCategoriesService(s)
	s.Chains = NewChainsService(s)
	s.GoogleLocations = NewGoogleLocationsService(s)
	s.Locations = NewLocationsService(s)
	return s, nil
}

type Service struct {
	client    *http.Client
	BasePath  string // API endpoint base URL
	UserAgent string // optional additional User-Agent fragment

	Accounts *AccountsService

	Attributes *AttributesService

	Categories *CategoriesService

	Chains *ChainsService

	GoogleLocations *GoogleLocationsService

	Locations *LocationsService
}

func (s *Service) userAgent() string {
	if s.UserAgent == "" {
		return googleapi.UserAgent
	}
	return googleapi.UserAgent + " " + s.UserAgent
}

func NewAccountsService(s *Service) *AccountsService {
	rs := &AccountsService{s: s}
	rs.Locations = NewAccountsLocationsService(s)
	return rs
}

type AccountsService struct {
	s *Service

	Locations *AccountsLocationsService
}

func NewAccountsLocationsService(s *Service) *AccountsLocationsService {
	rs := &AccountsLocationsService{s: s}
	return rs
}

type AccountsLocationsService struct {
	s *Service
}

func NewAttributesService(s *Service) *AttributesService {
	rs := &AttributesService{s: s}
	return rs
}

type AttributesService struct {
	s *Service
}

func NewCategoriesService(s *Service) *CategoriesService {
	rs := &CategoriesService{s: s}
	return rs
}

type CategoriesService struct {
	s *Service
}

func NewChainsService(s *Service) *ChainsService {
	rs := &ChainsService{s: s}
	return rs
}

type ChainsService struct {
	s *Service
}

func NewGoogleLocationsService(s *Service) *GoogleLocationsService {
	rs := &GoogleLocationsService{s: s}
	return rs
}

type GoogleLocationsService struct {
	s *Service
}

func NewLocationsService(s *Service) *LocationsService {
	rs := &LocationsService{s: s}
	rs.Attributes = NewLocationsAttributesService(s)
	return rs
}

type LocationsService struct {
	s *Service

	Attributes *LocationsAttributesService
}

func NewLocationsAttributesService(s *Service) *LocationsAttributesService {
	rs := &LocationsAttributesService{s: s}
	return rs
}

type LocationsAttributesService struct {
	s *Service
}

// AdWordsLocationExtensions: Additional information that is surfaced in
// AdWords.
type AdWordsLocationExtensions struct {
	// AdPhone: Required. An alternate phone number to display on AdWords location
	// extensions instead of the location's primary phone number.
	AdPhone string `json:"adPhone,omitempty"`
	// ForceSendFields is a list of field names (e.g. "AdPhone") to unconditionally
	// include in API requests. By default, fields with empty or default values are
	// omitted from API requests. See
	// https://pkg.go.dev/google.golang.org/api#hdr-ForceSendFields for more
	// details.
	ForceSendFields []string `json:"-"`
	// NullFields is a list of field names (e.g. "AdPhone") to include in API
	// requests with the JSON null value. By default, fields with empty values are
	// omitted from API requests. See
	// https://pkg.go.dev/google.golang.org/api#hdr-NullFields for more details.
	NullFields []string `json:"-"`
}

func (s AdWordsLocationExtensions) MarshalJSON() ([]byte, error) {
	type NoMethod AdWordsLocationExtensions
	return gensupport.MarshalJSON(NoMethod(s), s.ForceSendFields, s.NullFields)
}

// Attribute: A location attribute. Attributes provide additional information
// about a location. The attributes that can be set on a location may vary
// based on the properties of that location (for example, category). Available
// attributes are determined by Google and may be added and removed without API
// changes.
type Attribute struct {
	// Name: Required. The resource name for this attribute.
	Name string `json:"name,omitempty"`
	// RepeatedEnumValue: When the attribute value type is REPEATED_ENUM, this
	// contains the attribute value, and the other values fields must be empty.
	RepeatedEnumValue *RepeatedEnumAttributeValue `json:"repeatedEnumValue,omitempty"`
	// UriValues: When the attribute value type is URL, this field contains the
	// value(s) for this attribute, and the other values fields must be empty.
	UriValues []*UriAttributeValue `json:"uriValues,omitempty"`
	// ValueType: Output only. The type of value that this attribute contains. This
	// should be used to determine how to interpret the value.
	//
	// Possible values:
	//   "ATTRIBUTE_VALUE_TYPE_UNSPECIFIED" - Not specified.
	//   "BOOL" - The values for this attribute are boolean values.
	//   "ENUM" - The attribute has a predetermined list of available values that
	// can be used. Metadata for this attribute will list these values.
	//   "URL" - The values for this attribute are URLs.
	//   "REPEATED_ENUM" - The attribute value is an enum with multiple possible
	// values that can be explicitly set or unset.
	ValueType string `json:"valueType,omitempty"`
	// Values: The values for this attribute. The type of the values supplied must
	// match that expected for that attribute. This is a repeated field where
	// multiple attribute values may be provided. Attribute types only support one
	// value.
	Values []interface{} `json:"values,omitempty"`
	// ForceSendFields is a list of field names (e.g. "Name") to unconditionally
	// include in API requests. By default, fields with empty or default values are
	// omitted from API requests. See
	// https://pkg.go.dev/google.golang.org/api#hdr-ForceSendFields for more
	// details.
	ForceSendFields []string `json:"-"`
	// NullFields is a list of field names (e.g. "Name") to include in API requests
	// with the JSON null value. By default, fields with empty values are omitted
	// from API requests. See
	// https://pkg.go.dev/google.golang.org/api#hdr-NullFields for more details.
	NullFields []string `json:"-"`
}

func (s Attribute) MarshalJSON() ([]byte, error) {
	type NoMethod Attribute
	return gensupport.MarshalJSON(NoMethod(s), s.ForceSendFields, s.NullFields)
}

// AttributeMetadata: Metadata for an attribute. Contains display information
// for the attribute, including a localized name and a heading for grouping
// related attributes together.
type AttributeMetadata struct {
	// Deprecated -- If true, the attribute is deprecated and should no longer be
	// used. If deprecated, updating this attribute will not result in an error,
	// but updates will not be saved. At some point after being deprecated, the
	// attribute will be removed entirely and it will become an error.
	Deprecated bool `json:"deprecated,omitempty"`
	// DisplayName: The localized display name for the attribute, if available;
	// otherwise, the English display name.
	DisplayName string `json:"displayName,omitempty"`
	// GroupDisplayName: The localized display name of the group that contains this
	// attribute, if available; otherwise, the English group name. Related
	// attributes are collected into a group and should be displayed together under
	// the heading given here.
	GroupDisplayName string `json:"groupDisplayName,omitempty"`
	// Parent: The unique identifier for the attribute.
	Parent string `json:"parent,omitempty"`
	// Repeatable: If true, the attribute supports multiple values. If false, only
	// a single value should be provided.
	Repeatable bool `json:"repeatable,omitempty"`
	// ValueMetadata: For some types of attributes (for example, enums), a list of
	// supported values and corresponding display names for those values is
	// provided.
	ValueMetadata []*AttributeValueMetadata `json:"valueMetadata,omitempty"`
	// ValueType: The value type for the attribute. Values set and retrieved should
	// be expected to be of this type.
	//
	// Possible values:
	//   "ATTRIBUTE_VALUE_TYPE_UNSPECIFIED" - Not specified.
	//   "BOOL" - The values for this attribute are boolean values.
	//   "ENUM" - The attribute has a predetermined list of available values that
	// can be used. Metadata for this attribute will list these values.
	//   "URL" - The values for this attribute are URLs.
	//   "REPEATED_ENUM" - The attribute value is an enum with multiple possible
	// values that can be explicitly set or unset.
	ValueType string `json:"valueType,omitempty"`
	// ForceSendFields is a list of field names (e.g. "Deprecated") to
	// unconditionally include in API requests. By default, fields with empty or
	// default values are omitted from API requests. See
	// https://pkg.go.dev/google.golang.org/api#hdr-ForceSendFields for more
	// details.
	ForceSendFields []string `json:"-"`
	// NullFields is a list of field names (e.g. "Deprecated") to include in API
	// requests with the JSON null value. By default, fields with empty values are
	// omitted from API requests. See
	// https://pkg.go.dev/google.golang.org/api#hdr-NullFields for more details.
	NullFields []string `json:"-"`
}

func (s AttributeMetadata) MarshalJSON() ([]byte, error) {
	type NoMethod AttributeMetadata
	return gensupport.MarshalJSON(NoMethod(s), s.ForceSendFields, s.NullFields)
}

// AttributeValueMetadata: Metadata for supported attribute values.
type AttributeValueMetadata struct {
	// DisplayName: The display name for this value, localized where available;
	// otherwise, in English. The value display name is intended to be used in
	// context with the attribute display name. For example, for a "WiFi" enum
	// attribute, this could contain "Paid" to represent paid Wi-Fi.
	DisplayName string `json:"displayName,omitempty"`
	// Value: The attribute value.
	Value interface{} `json:"value,omitempty"`
	// ForceSendFields is a list of field names (e.g. "DisplayName") to
	// unconditionally include in API requests. By default, fields with empty or
	// default values are omitted from API requests. See
	// https://pkg.go.dev/google.golang.org/api#hdr-ForceSendFields for more
	// details.
	ForceSendFields []string `json:"-"`
	// NullFields is a list of field names (e.g. "DisplayName") to include in API
	// requests with the JSON null value. By default, fields with empty values are
	// omitted from API requests. See
	// https://pkg.go.dev/google.golang.org/api#hdr-NullFields for more details.
	NullFields []string `json:"-"`
}

func (s AttributeValueMetadata) MarshalJSON() ([]byte, error) {
	type NoMethod AttributeValueMetadata
	return gensupport.MarshalJSON(NoMethod(s), s.ForceSendFields, s.NullFields)
}

// Attributes: A container for all the attributes for a given location.
type Attributes struct {
	// Attributes: A collection of attributes that need to be updated.
	Attributes []*Attribute `json:"attributes,omitempty"`
	// Name: Required. Google identifier for this location in the form of
	// `locations/{location_id}/attributes`.
	Name string `json:"name,omitempty"`

	// ServerResponse contains the HTTP response code and headers from the server.
	googleapi.ServerResponse `json:"-"`
	// ForceSendFields is a list of field names (e.g. "Attributes") to
	// unconditionally include in API requests. By default, fields with empty or
	// default values are omitted from API requests. See
	// https://pkg.go.dev/google.golang.org/api#hdr-ForceSendFields for more
	// details.
	ForceSendFields []string `json:"-"`
	// NullFields is a list of field names (e.g. "Attributes") to include in API
	// requests with the JSON null value. By default, fields with empty values are
	// omitted from API requests. See
	// https://pkg.go.dev/google.golang.org/api#hdr-NullFields for more details.
	NullFields []string `json:"-"`
}

func (s Attributes) MarshalJSON() ([]byte, error) {
	type NoMethod Attributes
	return gensupport.MarshalJSON(NoMethod(s), s.ForceSendFields, s.NullFields)
}

// BatchGetCategoriesResponse: Response message for
// BusinessCategories.BatchGetBusinessCategories.
type BatchGetCategoriesResponse struct {
	// Categories: Categories that match the GConcept ids provided in the request.
	// They will not come in the same order as category ids in the request.
	Categories []*Category `json:"categories,omitempty"`

	// ServerResponse contains the HTTP response code and headers from the server.
	googleapi.ServerResponse `json:"-"`
	// ForceSendFields is a list of field names (e.g. "Categories") to
	// unconditionally include in API requests. By default, fields with empty or
	// default values are omitted from API requests. See
	// https://pkg.go.dev/google.golang.org/api#hdr-ForceSendFields for more
	// details.
	ForceSendFields []string `json:"-"`
	// NullFields is a list of field names (e.g. "Categories") to include in API
	// requests with the JSON null value. By default, fields with empty values are
	// omitted from API requests. See
	// https://pkg.go.dev/google.golang.org/api#hdr-NullFields for more details.
	NullFields []string `json:"-"`
}

func (s BatchGetCategoriesResponse) MarshalJSON() ([]byte, error) {
	type NoMethod BatchGetCategoriesResponse
	return gensupport.MarshalJSON(NoMethod(s), s.ForceSendFields, s.NullFields)
}

// BusinessHours: Represents the time periods that this location is open for
// business. Holds a collection of TimePeriod instances.
type BusinessHours struct {
	// Periods: Required. A collection of times that this location is open for
	// business. Each period represents a range of hours when the location is open
	// during the week.
	Periods []*TimePeriod `json:"periods,omitempty"`
	// ForceSendFields is a list of field names (e.g. "Periods") to unconditionally
	// include in API requests. By default, fields with empty or default values are
	// omitted from API requests. See
	// https://pkg.go.dev/google.golang.org/api#hdr-ForceSendFields for more
	// details.
	ForceSendFields []string `json:"-"`
	// NullFields is a list of field names (e.g. "Periods") to include in API
	// requests with the JSON null value. By default, fields with empty values are
	// omitted from API requests. See
	// https://pkg.go.dev/google.golang.org/api#hdr-NullFields for more details.
	NullFields []string `json:"-"`
}

func (s BusinessHours) MarshalJSON() ([]byte, error) {
	type NoMethod BusinessHours
	return gensupport.MarshalJSON(NoMethod(s), s.ForceSendFields, s.NullFields)
}

// Categories: A collection of categories that describes the business. During
// updates, both fields must be set. Clients are prohibited from individually
// updating the primary or additional categories using the update mask.
type Categories struct {
	// AdditionalCategories: Optional. Additional categories to describe your
	// business. Categories help your customers find accurate, specific results for
	// services they're interested in. To keep your business information accurate
	// and live, make sure that you use as few categories as possible to describe
	// your overall core business. Choose categories that are as specific as
	// possible, but representative of your main business.
	AdditionalCategories []*Category `json:"additionalCategories,omitempty"`
	// PrimaryCategory: Required. Category that best describes the core business
	// this location engages in.
	PrimaryCategory *Category `json:"primaryCategory,omitempty"`
	// ForceSendFields is a list of field names (e.g. "AdditionalCategories") to
	// unconditionally include in API requests. By default, fields with empty or
	// default values are omitted from API requests. See
	// https://pkg.go.dev/google.golang.org/api#hdr-ForceSendFields for more
	// details.
	ForceSendFields []string `json:"-"`
	// NullFields is a list of field names (e.g. "AdditionalCategories") to include
	// in API requests with the JSON null value. By default, fields with empty
	// values are omitted from API requests. See
	// https://pkg.go.dev/google.golang.org/api#hdr-NullFields for more details.
	NullFields []string `json:"-"`
}

func (s Categories) MarshalJSON() ([]byte, error) {
	type NoMethod Categories
	return gensupport.MarshalJSON(NoMethod(s), s.ForceSendFields, s.NullFields)
}

// Category: A category describing what this business is (not what it does).
// For a list of valid category IDs, and the mappings to their human-readable
// names, see `categories.list`.
type Category struct {
	// DisplayName: Output only. The human-readable name of the category. This is
	// set when reading the location. When modifying the location, `category_id`
	// must be set.
	DisplayName string `json:"displayName,omitempty"`
	// MoreHoursTypes: Output only. More hours types that are available for this
	// business category.
	MoreHoursTypes []*MoreHoursType `json:"moreHoursTypes,omitempty"`
	// Name: Required. A stable ID (provided by Google) for this category. The
	// value must be specified when modifying the category (when creating or
	// updating a location).
	Name string `json:"name,omitempty"`
	// ServiceTypes: Output only. A list of all the service types that are
	// available for this business category.
	ServiceTypes []*ServiceType `json:"serviceTypes,omitempty"`
	// ForceSendFields is a list of field names (e.g. "DisplayName") to
	// unconditionally include in API requests. By default, fields with empty or
	// default values are omitted from API requests. See
	// https://pkg.go.dev/google.golang.org/api#hdr-ForceSendFields for more
	// details.
	ForceSendFields []string `json:"-"`
	// NullFields is a list of field names (e.g. "DisplayName") to include in API
	// requests with the JSON null value. By default, fields with empty values are
	// omitted from API requests. See
	// https://pkg.go.dev/google.golang.org/api#hdr-NullFields for more details.
	NullFields []string `json:"-"`
}

func (s Category) MarshalJSON() ([]byte, error) {
	type NoMethod Category
	return gensupport.MarshalJSON(NoMethod(s), s.ForceSendFields, s.NullFields)
}

// Chain: A chain is a brand that your business's locations can be affiliated
// with.
type Chain struct {
	// ChainNames: Names of the chain.
	ChainNames []*ChainName `json:"chainNames,omitempty"`
	// LocationCount: Number of locations that are part of this chain.
	LocationCount int64 `json:"locationCount,omitempty"`
	// Name: Required. The chain's resource name, in the format
	// `chains/{chain_id}`.
	Name string `json:"name,omitempty"`
	// Websites: Websites of the chain.
	Websites []*ChainUri `json:"websites,omitempty"`

	// ServerResponse contains the HTTP response code and headers from the server.
	googleapi.ServerResponse `json:"-"`
	// ForceSendFields is a list of field names (e.g. "ChainNames") to
	// unconditionally include in API requests. By default, fields with empty or
	// default values are omitted from API requests. See
	// https://pkg.go.dev/google.golang.org/api#hdr-ForceSendFields for more
	// details.
	ForceSendFields []string `json:"-"`
	// NullFields is a list of field names (e.g. "ChainNames") to include in API
	// requests with the JSON null value. By default, fields with empty values are
	// omitted from API requests. See
	// https://pkg.go.dev/google.golang.org/api#hdr-NullFields for more details.
	NullFields []string `json:"-"`
}

func (s Chain) MarshalJSON() ([]byte, error) {
	type NoMethod Chain
	return gensupport.MarshalJSON(NoMethod(s), s.ForceSendFields, s.NullFields)
}

// ChainName: Name to be used when displaying the chain.
type ChainName struct {
	// DisplayName: The display name for this chain.
	DisplayName string `json:"displayName,omitempty"`
	// LanguageCode: The BCP 47 code of language of the name.
	LanguageCode string `json:"languageCode,omitempty"`
	// ForceSendFields is a list of field names (e.g. "DisplayName") to
	// unconditionally include in API requests. By default, fields with empty or
	// default values are omitted from API requests. See
	// https://pkg.go.dev/google.golang.org/api#hdr-ForceSendFields for more
	// details.
	ForceSendFields []string `json:"-"`
	// NullFields is a list of field names (e.g. "DisplayName") to include in API
	// requests with the JSON null value. By default, fields with empty values are
	// omitted from API requests. See
	// https://pkg.go.dev/google.golang.org/api#hdr-NullFields for more details.
	NullFields []string `json:"-"`
}

func (s ChainName) MarshalJSON() ([]byte, error) {
	type NoMethod ChainName
	return gensupport.MarshalJSON(NoMethod(s), s.ForceSendFields, s.NullFields)
}

// ChainUri: Url to be used when displaying the chain.
type ChainUri struct {
	// Uri: The uri for this chain.
	Uri string `json:"uri,omitempty"`
	// ForceSendFields is a list of field names (e.g. "Uri") to unconditionally
	// include in API requests. By default, fields with empty or default values are
	// omitted from API requests. See
	// https://pkg.go.dev/google.golang.org/api#hdr-ForceSendFields for more
	// details.
	ForceSendFields []string `json:"-"`
	// NullFields is a list of field names (e.g. "Uri") to include in API requests
	// with the JSON null value. By default, fields with empty values are omitted
	// from API requests. See
	// https://pkg.go.dev/google.golang.org/api#hdr-NullFields for more details.
	NullFields []string `json:"-"`
}

func (s ChainUri) MarshalJSON() ([]byte, error) {
	type NoMethod ChainUri
	return gensupport.MarshalJSON(NoMethod(s), s.ForceSendFields, s.NullFields)
}

// Date: Represents a whole or partial calendar date, such as a birthday. The
// time of day and time zone are either specified elsewhere or are
// insignificant. The date is relative to the Gregorian Calendar. This can
// represent one of the following: * A full date, with non-zero year, month,
// and day values. * A month and day, with a zero year (for example, an
// anniversary). * A year on its own, with a zero month and a zero day. * A
// year and month, with a zero day (for example, a credit card expiration
// date). Related types: * google.type.TimeOfDay * google.type.DateTime *
// google.protobuf.Timestamp
type Date struct {
	// Day: Day of a month. Must be from 1 to 31 and valid for the year and month,
	// or 0 to specify a year by itself or a year and month where the day isn't
	// significant.
	Day int64 `json:"day,omitempty"`
	// Month: Month of a year. Must be from 1 to 12, or 0 to specify a year without
	// a month and day.
	Month int64 `json:"month,omitempty"`
	// Year: Year of the date. Must be from 1 to 9999, or 0 to specify a date
	// without a year.
	Year int64 `json:"year,omitempty"`
	// ForceSendFields is a list of field names (e.g. "Day") to unconditionally
	// include in API requests. By default, fields with empty or default values are
	// omitted from API requests. See
	// https://pkg.go.dev/google.golang.org/api#hdr-ForceSendFields for more
	// details.
	ForceSendFields []string `json:"-"`
	// NullFields is a list of field names (e.g. "Day") to include in API requests
	// with the JSON null value. By default, fields with empty values are omitted
	// from API requests. See
	// https://pkg.go.dev/google.golang.org/api#hdr-NullFields for more details.
	NullFields []string `json:"-"`
}

func (s Date) MarshalJSON() ([]byte, error) {
	type NoMethod Date
	return gensupport.MarshalJSON(NoMethod(s), s.ForceSendFields, s.NullFields)
}

// Empty: A generic empty message that you can re-use to avoid defining
// duplicated empty messages in your APIs. A typical example is to use it as
// the request or the response type of an API method. For instance: service Foo
// { rpc Bar(google.protobuf.Empty) returns (google.protobuf.Empty); }
type Empty struct {
	// ServerResponse contains the HTTP response code and headers from the server.
	googleapi.ServerResponse `json:"-"`
}

// FreeFormServiceItem: Represents a free-form service offered by the merchant.
// These are services that are not exposed as part of our structure service
// data. The merchant manually enters the names for of such services via a
// geomerchant surface.
type FreeFormServiceItem struct {
	// Category: Required. This field represents the category name (i.e. the
	// category's stable ID). The `category` and `service_type_id` should match the
	// possible combinations provided in the `Category` message.
	Category string `json:"category,omitempty"`
	// Label: Required. Language-tagged labels for the item. We recommend that item
	// names be 140 characters or less, and descriptions 250 characters or less.
	// This field should only be set if the input is a custom service item.
	// Standardized service types should be updated via service_type_id.
	Label *Label `json:"label,omitempty"`
	// ForceSendFields is a list of field names (e.g. "Category") to
	// unconditionally include in API requests. By default, fields with empty or
	// default values are omitted from API requests. See
	// https://pkg.go.dev/google.golang.org/api#hdr-ForceSendFields for more
	// details.
	ForceSendFields []string `json:"-"`
	// NullFields is a list of field names (e.g. "Category") to include in API
	// requests with the JSON null value. By default, fields with empty values are
	// omitted from API requests. See
	// https://pkg.go.dev/google.golang.org/api#hdr-NullFields for more details.
	NullFields []string `json:"-"`
}

func (s FreeFormServiceItem) MarshalJSON() ([]byte, error) {
	type NoMethod FreeFormServiceItem
	return gensupport.MarshalJSON(NoMethod(s), s.ForceSendFields, s.NullFields)
}

// GoogleLocation: Represents a Location that is present on Google. This can be
// a location that has been claimed by the user, someone else, or could be
// unclaimed.
type GoogleLocation struct {
	// Location: The sparsely populated Location information. This field can be
	// re-used in CreateLocation if it is not currently claimed by a user.
	Location *Location `json:"location,omitempty"`
	// Name: Resource name of this GoogleLocation, in the format
	// `googleLocations/{googleLocationId}`.
	Name string `json:"name,omitempty"`
	// RequestAdminRightsUri: A URL that will redirect the user to the request
	// admin rights UI. This field is only present if the location has already been
	// claimed by any user, including the current user.
	RequestAdminRightsUri string `json:"requestAdminRightsUri,omitempty"`
	// ForceSendFields is a list of field names (e.g. "Location") to
	// unconditionally include in API requests. By default, fields with empty or
	// default values are omitted from API requests. See
	// https://pkg.go.dev/google.golang.org/api#hdr-ForceSendFields for more
	// details.
	ForceSendFields []string `json:"-"`
	// NullFields is a list of field names (e.g. "Location") to include in API
	// requests with the JSON null value. By default, fields with empty values are
	// omitted from API requests. See
	// https://pkg.go.dev/google.golang.org/api#hdr-NullFields for more details.
	NullFields []string `json:"-"`
}

func (s GoogleLocation) MarshalJSON() ([]byte, error) {
	type NoMethod GoogleLocation
	return gensupport.MarshalJSON(NoMethod(s), s.ForceSendFields, s.NullFields)
}

// GoogleUpdatedLocation: Represents a location that was modified by Google.
type GoogleUpdatedLocation struct {
	// DiffMask: The fields that Google updated.
	DiffMask string `json:"diffMask,omitempty"`
	// Location: The Google-updated version of this location.
	Location *Location `json:"location,omitempty"`
	// PendingMask: The fields that have pending edits that haven't yet been pushed
	// to Maps and Search.
	PendingMask string `json:"pendingMask,omitempty"`

	// ServerResponse contains the HTTP response code and headers from the server.
	googleapi.ServerResponse `json:"-"`
	// ForceSendFields is a list of field names (e.g. "DiffMask") to
	// unconditionally include in API requests. By default, fields with empty or
	// default values are omitted from API requests. See
	// https://pkg.go.dev/google.golang.org/api#hdr-ForceSendFields for more
	// details.
	ForceSendFields []string `json:"-"`
	// NullFields is a list of field names (e.g. "DiffMask") to include in API
	// requests with the JSON null value. By default, fields with empty values are
	// omitted from API requests. See
	// https://pkg.go.dev/google.golang.org/api#hdr-NullFields for more details.
	NullFields []string `json:"-"`
}

func (s GoogleUpdatedLocation) MarshalJSON() ([]byte, error) {
	type NoMethod GoogleUpdatedLocation
	return gensupport.MarshalJSON(NoMethod(s), s.ForceSendFields, s.NullFields)
}

// Label: Label to be used when displaying the price list, section, or item.
type Label struct {
	// Description: Optional. Description of the price list, section, or item.
	Description string `json:"description,omitempty"`
	// DisplayName: Required. Display name for the price list, section, or item.
	DisplayName string `json:"displayName,omitempty"`
	// LanguageCode: Optional. The BCP-47 language code that these strings apply
	// for. Only one set of labels may be set per language.
	LanguageCode string `json:"languageCode,omitempty"`
	// ForceSendFields is a list of field names (e.g. "Description") to
	// unconditionally include in API requests. By default, fields with empty or
	// default values are omitted from API requests. See
	// https://pkg.go.dev/google.golang.org/api#hdr-ForceSendFields for more
	// details.
	ForceSendFields []string `json:"-"`
	// NullFields is a list of field names (e.g. "Description") to include in API
	// requests with the JSON null value. By default, fields with empty values are
	// omitted from API requests. See
	// https://pkg.go.dev/google.golang.org/api#hdr-NullFields for more details.
	NullFields []string `json:"-"`
}

func (s Label) MarshalJSON() ([]byte, error) {
	type NoMethod Label
	return gensupport.MarshalJSON(NoMethod(s), s.ForceSendFields, s.NullFields)
}

// LatLng: An object that represents a latitude/longitude pair. This is
// expressed as a pair of doubles to represent degrees latitude and degrees
// longitude. Unless specified otherwise, this object must conform to the WGS84
// standard. Values must be within normalized ranges.
type LatLng struct {
	// Latitude: The latitude in degrees. It must be in the range [-90.0, +90.0].
	Latitude float64 `json:"latitude,omitempty"`
	// Longitude: The longitude in degrees. It must be in the range [-180.0,
	// +180.0].
	Longitude float64 `json:"longitude,omitempty"`
	// ForceSendFields is a list of field names (e.g. "Latitude") to
	// unconditionally include in API requests. By default, fields with empty or
	// default values are omitted from API requests. See
	// https://pkg.go.dev/google.golang.org/api#hdr-ForceSendFields for more
	// details.
	ForceSendFields []string `json:"-"`
	// NullFields is a list of field names (e.g. "Latitude") to include in API
	// requests with the JSON null value. By default, fields with empty values are
	// omitted from API requests. See
	// https://pkg.go.dev/google.golang.org/api#hdr-NullFields for more details.
	NullFields []string `json:"-"`
}

func (s LatLng) MarshalJSON() ([]byte, error) {
	type NoMethod LatLng
	return gensupport.MarshalJSON(NoMethod(s), s.ForceSendFields, s.NullFields)
}

func (s *LatLng) UnmarshalJSON(data []byte) error {
	type NoMethod LatLng
	var s1 struct {
		Latitude  gensupport.JSONFloat64 `json:"latitude"`
		Longitude gensupport.JSONFloat64 `json:"longitude"`
		*NoMethod
	}
	s1.NoMethod = (*NoMethod)(s)
	if err := json.Unmarshal(data, &s1); err != nil {
		return err
	}
	s.Latitude = float64(s1.Latitude)
	s.Longitude = float64(s1.Longitude)
	return nil
}

// ListAttributeMetadataResponse: Response for
// AttributesService.ListAttributeMetadata.
type ListAttributeMetadataResponse struct {
	// AttributeMetadata: A collection of attribute metadata for the available
	// attributes.
	AttributeMetadata []*AttributeMetadata `json:"attributeMetadata,omitempty"`
	// NextPageToken: If the number of attributes exceeded the requested page size,
	// this field will be populated with a token to fetch the next page of
	// attributes on a subsequent call to `attributes.list`. If there are no more
	// attributes, this field will not be present in the response.
	NextPageToken string `json:"nextPageToken,omitempty"`

	// ServerResponse contains the HTTP response code and headers from the server.
	googleapi.ServerResponse `json:"-"`
	// ForceSendFields is a list of field names (e.g. "AttributeMetadata") to
	// unconditionally include in API requests. By default, fields with empty or
	// default values are omitted from API requests. See
	// https://pkg.go.dev/google.golang.org/api#hdr-ForceSendFields for more
	// details.
	ForceSendFields []string `json:"-"`
	// NullFields is a list of field names (e.g. "AttributeMetadata") to include in
	// API requests with the JSON null value. By default, fields with empty values
	// are omitted from API requests. See
	// https://pkg.go.dev/google.golang.org/api#hdr-NullFields for more details.
	NullFields []string `json:"-"`
}

func (s ListAttributeMetadataResponse) MarshalJSON() ([]byte, error) {
	type NoMethod ListAttributeMetadataResponse
	return gensupport.MarshalJSON(NoMethod(s), s.ForceSendFields, s.NullFields)
}

// ListCategoriesResponse: Response message for
// BusinessCategories.ListCategories.
type ListCategoriesResponse struct {
	// Categories: The matching categories based on the requested parameters.
	Categories []*Category `json:"categories,omitempty"`
	// NextPageToken: If the number of categories exceeded the requested page size,
	// this field will be populated with a token to fetch the next page of
	// categories on a subsequent call to `ListCategories`.
	NextPageToken string `json:"nextPageToken,omitempty"`

	// ServerResponse contains the HTTP response code and headers from the server.
	googleapi.ServerResponse `json:"-"`
	// ForceSendFields is a list of field names (e.g. "Categories") to
	// unconditionally include in API requests. By default, fields with empty or
	// default values are omitted from API requests. See
	// https://pkg.go.dev/google.golang.org/api#hdr-ForceSendFields for more
	// details.
	ForceSendFields []string `json:"-"`
	// NullFields is a list of field names (e.g. "Categories") to include in API
	// requests with the JSON null value. By default, fields with empty values are
	// omitted from API requests. See
	// https://pkg.go.dev/google.golang.org/api#hdr-NullFields for more details.
	NullFields []string `json:"-"`
}

func (s ListCategoriesResponse) MarshalJSON() ([]byte, error) {
	type NoMethod ListCategoriesResponse
	return gensupport.MarshalJSON(NoMethod(s), s.ForceSendFields, s.NullFields)
}

// ListLocationsResponse: Response message for Locations.ListLocations.
type ListLocationsResponse struct {
	// Locations: The locations.
	Locations []*Location `json:"locations,omitempty"`
	// NextPageToken: If the number of locations exceeded the requested page size,
	// this field is populated with a token to fetch the next page of locations on
	// a subsequent call to `ListLocations`. If there are no more locations, this
	// field is not present in the response.
	NextPageToken string `json:"nextPageToken,omitempty"`
	// TotalSize: The approximate number of Locations in the list irrespective of
	// pagination. This field will only be returned if `filter` is used as a query
	// parameter.
	TotalSize int64 `json:"totalSize,omitempty"`

	// ServerResponse contains the HTTP response code and headers from the server.
	googleapi.ServerResponse `json:"-"`
	// ForceSendFields is a list of field names (e.g. "Locations") to
	// unconditionally include in API requests. By default, fields with empty or
	// default values are omitted from API requests. See
	// https://pkg.go.dev/google.golang.org/api#hdr-ForceSendFields for more
	// details.
	ForceSendFields []string `json:"-"`
	// NullFields is a list of field names (e.g. "Locations") to include in API
	// requests with the JSON null value. By default, fields with empty values are
	// omitted from API requests. See
	// https://pkg.go.dev/google.golang.org/api#hdr-NullFields for more details.
	NullFields []string `json:"-"`
}

func (s ListLocationsResponse) MarshalJSON() ([]byte, error) {
	type NoMethod ListLocationsResponse
	return gensupport.MarshalJSON(NoMethod(s), s.ForceSendFields, s.NullFields)
}

// Location: A location. See the [help center article]
// (https://support.google.com/business/answer/3038177) for a detailed
// description of these fields, or the category endpoint
// (/my-business/reference/rest/v4/categories) for a list of valid business
// categories.
type Location struct {
	// AdWordsLocationExtensions: Optional. Additional information that is surfaced
	// in AdWords.
	AdWordsLocationExtensions *AdWordsLocationExtensions `json:"adWordsLocationExtensions,omitempty"`
	// Categories: Optional. The different categories that describe the business.
	Categories *Categories `json:"categories,omitempty"`
	// Labels: Optional. A collection of free-form strings to allow you to tag your
	// business. These labels are NOT user facing; only you can see them. Must be
	// between 1-255 characters per label.
	Labels []string `json:"labels,omitempty"`
	// LanguageCode: Immutable. The language of the location. Set during creation
	// and not updateable.
	LanguageCode string `json:"languageCode,omitempty"`
	// Latlng: Optional. User-provided latitude and longitude. When creating a
	// location, this field is ignored if the provided address geocodes
	// successfully. This field is only returned on get requests if the
	// user-provided `latlng` value was accepted during create, or the `latlng`
	// value was updated through the Google Business Profile website. This field
	// can only be updated by approved clients.
	Latlng *LatLng `json:"latlng,omitempty"`
	// Metadata: Output only. Additional non-user-editable information.
	Metadata *Metadata `json:"metadata,omitempty"`
	// MoreHours: Optional. More hours for a business's different departments or
	// specific customers.
	MoreHours []*MoreHours `json:"moreHours,omitempty"`
	// Name: Google identifier for this location in the form:
	// `locations/{location_id}`.
	Name string `json:"name,omitempty"`
	// OpenInfo: Optional. A flag that indicates whether the location is currently
	// open for business.
	OpenInfo *OpenInfo `json:"openInfo,omitempty"`
	// PhoneNumbers: Optional. The different phone numbers that customers can use
	// to get in touch with the business.
	PhoneNumbers *PhoneNumbers `json:"phoneNumbers,omitempty"`
	// Profile: Optional. Describes your business in your own voice and shares with
	// users the unique story of your business and offerings. This field is
	// required for all categories except lodging categories (e.g. hotels, motels,
	// inns).
	Profile *Profile `json:"profile,omitempty"`
	// RegularHours: Optional. Operating hours for the business.
	RegularHours *BusinessHours `json:"regularHours,omitempty"`
	// RelationshipData: Optional. All locations and chain related to this one.
	RelationshipData *RelationshipData `json:"relationshipData,omitempty"`
	// ServiceArea: Optional. Service area businesses provide their service at the
	// customer's location. If this business is a service area business, this field
	// describes the area(s) serviced by the business.
	ServiceArea *ServiceAreaBusiness `json:"serviceArea,omitempty"`
	// ServiceItems: Optional. List of services supported by merchants. A service
	// can be haircut, install water heater, etc. Duplicated service items will be
	// removed automatically.
	ServiceItems []*ServiceItem `json:"serviceItems,omitempty"`
	// SpecialHours: Optional. Special hours for the business. This typically
	// includes holiday hours, and other times outside of regular operating hours.
	// These override regular business hours. This field cannot be set without
	// regular hours.
	SpecialHours *SpecialHours `json:"specialHours,omitempty"`
	// StoreCode: Optional. External identifier for this location, which must be
	// unique within a given account. This is a means of associating the location
	// with your own records.
	StoreCode string `json:"storeCode,omitempty"`
	// StorefrontAddress: Optional. A precise, accurate address to describe your
	// business location. PO boxes or mailboxes located at remote locations are not
	// acceptable. At this time, you can specify a maximum of five `address_lines`
	// values in the address. This field should only be set for businesses that
	// have a storefront. This field should not be set for locations of type
	// `CUSTOMER_LOCATION_ONLY` but if set, any value provided will be discarded.
	StorefrontAddress *PostalAddress `json:"storefrontAddress,omitempty"`
	// Title: Required. Location name should reflect your business's real-world
	// name, as used consistently on your storefront, website, and stationery, and
	// as known to customers. Any additional information, when relevant, can be
	// included in other fields of the resource (for example, `Address`,
	// `Categories`). Don't add unnecessary information to your name (for example,
	// prefer "Google" over "Google Inc. - Mountain View Corporate Headquarters").
	// Don't include marketing taglines, store codes, special characters, hours or
	// closed/open status, phone numbers, website URLs, service/product
	// information, location/address or directions, or containment information (for
	// example, "Chase ATM in Duane Reade").
	Title string `json:"title,omitempty"`
	// WebsiteUri: Optional. A URL for this business. If possible, use a URL that
	// represents this individual business location instead of a generic
	// website/URL that represents all locations, or the brand.
	WebsiteUri string `json:"websiteUri,omitempty"`

	// ServerResponse contains the HTTP response code and headers from the server.
	googleapi.ServerResponse `json:"-"`
	// ForceSendFields is a list of field names (e.g. "AdWordsLocationExtensions")
	// to unconditionally include in API requests. By default, fields with empty or
	// default values are omitted from API requests. See
	// https://pkg.go.dev/google.golang.org/api#hdr-ForceSendFields for more
	// details.
	ForceSendFields []string `json:"-"`
	// NullFields is a list of field names (e.g. "AdWordsLocationExtensions") to
	// include in API requests with the JSON null value. By default, fields with
	// empty values are omitted from API requests. See
	// https://pkg.go.dev/google.golang.org/api#hdr-NullFields for more details.
	NullFields []string `json:"-"`
}

func (s Location) MarshalJSON() ([]byte, error) {
	type NoMethod Location
	return gensupport.MarshalJSON(NoMethod(s), s.ForceSendFields, s.NullFields)
}

// Metadata: Additional non-user-editable information about the location.
type Metadata struct {
	// CanDelete: Output only. Indicates whether the location can be deleted using
	// the API.
	CanDelete bool `json:"canDelete,omitempty"`
	// CanHaveBusinessCalls: Output only. Indicates if the listing is eligible for
	// business calls.
	CanHaveBusinessCalls bool `json:"canHaveBusinessCalls,omitempty"`
	// CanHaveFoodMenus: Output only. Indicates if the listing is eligible for food
	// menu.
	CanHaveFoodMenus bool `json:"canHaveFoodMenus,omitempty"`
	// CanModifyServiceList: Output only. Indicates if the listing can modify the
	// service list.
	CanModifyServiceList bool `json:"canModifyServiceList,omitempty"`
	// CanOperateHealthData: Output only. Indicates whether the location can
	// operate on Health data.
	CanOperateHealthData bool `json:"canOperateHealthData,omitempty"`
	// CanOperateLocalPost: Output only. Indicates if the listing can manage local
	// posts.
	CanOperateLocalPost bool `json:"canOperateLocalPost,omitempty"`
	// CanOperateLodgingData: Output only. Indicates whether the location can
	// operate on Lodging data.
	CanOperateLodgingData bool `json:"canOperateLodgingData,omitempty"`
	// DuplicateLocation: Output only. The location resource that this location
	// duplicates.
	DuplicateLocation string `json:"duplicateLocation,omitempty"`
	// HasGoogleUpdated: Output only. Indicates whether the place ID associated
	// with this location has updates that need to be updated or rejected by the
	// client. If this boolean is set, you should call the `getGoogleUpdated`
	// method to lookup information that's needs to be verified.
	HasGoogleUpdated bool `json:"hasGoogleUpdated,omitempty"`
	// HasPendingEdits: Output only. Indicates whether any of this Location's
	// properties are in the edit pending state.
	HasPendingEdits bool `json:"hasPendingEdits,omitempty"`
	// HasVoiceOfMerchant: Output only. Indicates if the listing has Voice of
	// Merchant. If this boolean is false, you should call the
	// locations.getVoiceOfMerchantState API to get details as to why they do not
	// have Voice of Merchant.
	HasVoiceOfMerchant bool `json:"hasVoiceOfMerchant,omitempty"`
	// MapsUri: Output only. A link to the location on Maps.
	MapsUri string `json:"mapsUri,omitempty"`
	// NewReviewUri: Output only. A link to the page on Google Search where a
	// customer can leave a review for the location.
	NewReviewUri string `json:"newReviewUri,omitempty"`
	// PlaceId: Output only. If this locationappears on Google Maps, this field is
	// populated with the place ID for the location. This ID can be used in various
	// Places APIs. This field can be set during Create calls, but not for Update.
	PlaceId string `json:"placeId,omitempty"`
	// ForceSendFields is a list of field names (e.g. "CanDelete") to
	// unconditionally include in API requests. By default, fields with empty or
	// default values are omitted from API requests. See
	// https://pkg.go.dev/google.golang.org/api#hdr-ForceSendFields for more
	// details.
	ForceSendFields []string `json:"-"`
	// NullFields is a list of field names (e.g. "CanDelete") to include in API
	// requests with the JSON null value. By default, fields with empty values are
	// omitted from API requests. See
	// https://pkg.go.dev/google.golang.org/api#hdr-NullFields for more details.
	NullFields []string `json:"-"`
}

func (s Metadata) MarshalJSON() ([]byte, error) {
	type NoMethod Metadata
	return gensupport.MarshalJSON(NoMethod(s), s.ForceSendFields, s.NullFields)
}

// Money: Represents an amount of money with its currency type.
type Money struct {
	// CurrencyCode: The three-letter currency code defined in ISO 4217.
	CurrencyCode string `json:"currencyCode,omitempty"`
	// Nanos: Number of nano (10^-9) units of the amount. The value must be between
	// -999,999,999 and +999,999,999 inclusive. If `units` is positive, `nanos`
	// must be positive or zero. If `units` is zero, `nanos` can be positive, zero,
	// or negative. If `units` is negative, `nanos` must be negative or zero. For
	// example $-1.75 is represented as `units`=-1 and `nanos`=-750,000,000.
	Nanos int64 `json:"nanos,omitempty"`
	// Units: The whole units of the amount. For example if `currencyCode` is
	// "USD", then 1 unit is one US dollar.
	Units int64 `json:"units,omitempty,string"`
	// ForceSendFields is a list of field names (e.g. "CurrencyCode") to
	// unconditionally include in API requests. By default, fields with empty or
	// default values are omitted from API requests. See
	// https://pkg.go.dev/google.golang.org/api#hdr-ForceSendFields for more
	// details.
	ForceSendFields []string `json:"-"`
	// NullFields is a list of field names (e.g. "CurrencyCode") to include in API
	// requests with the JSON null value. By default, fields with empty values are
	// omitted from API requests. See
	// https://pkg.go.dev/google.golang.org/api#hdr-NullFields for more details.
	NullFields []string `json:"-"`
}

func (s Money) MarshalJSON() ([]byte, error) {
	type NoMethod Money
	return gensupport.MarshalJSON(NoMethod(s), s.ForceSendFields, s.NullFields)
}

// MoreHours: The time periods during which a location is open for certain
// types of business.
type MoreHours struct {
	// HoursTypeId: Required. Type of hours. Clients should call {#link
	// businessCategories:BatchGet} to get supported hours types for categories of
	// their locations.
	HoursTypeId string `json:"hoursTypeId,omitempty"`
	// Periods: Required. A collection of times that this location is open. Each
	// period represents a range of hours when the location is open during the
	// week.
	Periods []*TimePeriod `json:"periods,omitempty"`
	// ForceSendFields is a list of field names (e.g. "HoursTypeId") to
	// unconditionally include in API requests. By default, fields with empty or
	// default values are omitted from API requests. See
	// https://pkg.go.dev/google.golang.org/api#hdr-ForceSendFields for more
	// details.
	ForceSendFields []string `json:"-"`
	// NullFields is a list of field names (e.g. "HoursTypeId") to include in API
	// requests with the JSON null value. By default, fields with empty values are
	// omitted from API requests. See
	// https://pkg.go.dev/google.golang.org/api#hdr-NullFields for more details.
	NullFields []string `json:"-"`
}

func (s MoreHours) MarshalJSON() ([]byte, error) {
	type NoMethod MoreHours
	return gensupport.MarshalJSON(NoMethod(s), s.ForceSendFields, s.NullFields)
}

// MoreHoursType: More hours types that a business can offers, in addition to
// its regular hours.
type MoreHoursType struct {
	// DisplayName: Output only. The human-readable English display name for the
	// hours type.
	DisplayName string `json:"displayName,omitempty"`
	// HoursTypeId: Output only. A stable ID provided by Google for this hours
	// type.
	HoursTypeId string `json:"hoursTypeId,omitempty"`
	// LocalizedDisplayName: Output only. The human-readable localized display name
	// for the hours type.
	LocalizedDisplayName string `json:"localizedDisplayName,omitempty"`
	// ForceSendFields is a list of field names (e.g. "DisplayName") to
	// unconditionally include in API requests. By default, fields with empty or
	// default values are omitted from API requests. See
	// https://pkg.go.dev/google.golang.org/api#hdr-ForceSendFields for more
	// details.
	ForceSendFields []string `json:"-"`
	// NullFields is a list of field names (e.g. "DisplayName") to include in API
	// requests with the JSON null value. By default, fields with empty values are
	// omitted from API requests. See
	// https://pkg.go.dev/google.golang.org/api#hdr-NullFields for more details.
	NullFields []string `json:"-"`
}

func (s MoreHoursType) MarshalJSON() ([]byte, error) {
	type NoMethod MoreHoursType
	return gensupport.MarshalJSON(NoMethod(s), s.ForceSendFields, s.NullFields)
}

// OpenInfo: Information related to the opening state of the business.
type OpenInfo struct {
	// CanReopen: Output only. Indicates whether this business is eligible for
	// re-open.
	CanReopen bool `json:"canReopen,omitempty"`
	// OpeningDate: Optional. The date on which the location first opened. If the
	// exact day is not known, month and year only can be provided. The date must
	// be in the past or be no more than one year in the future.
	OpeningDate *Date `json:"openingDate,omitempty"`
	// Status: Required. Indicates whether or not the Location is currently open
	// for business. All locations are open by default, unless updated to be
	// closed.
	//
	// Possible values:
	//   "OPEN_FOR_BUSINESS_UNSPECIFIED" - Not specified.
	//   "OPEN" - Indicates that the location is open.
	//   "CLOSED_PERMANENTLY" - Indicates that the location has been permanently
	// closed.
	//   "CLOSED_TEMPORARILY" - Indicates that the location has been temporarily
	// closed.
	Status string `json:"status,omitempty"`
	// ForceSendFields is a list of field names (e.g. "CanReopen") to
	// unconditionally include in API requests. By default, fields with empty or
	// default values are omitted from API requests. See
	// https://pkg.go.dev/google.golang.org/api#hdr-ForceSendFields for more
	// details.
	ForceSendFields []string `json:"-"`
	// NullFields is a list of field names (e.g. "CanReopen") to include in API
	// requests with the JSON null value. By default, fields with empty values are
	// omitted from API requests. See
	// https://pkg.go.dev/google.golang.org/api#hdr-NullFields for more details.
	NullFields []string `json:"-"`
}

func (s OpenInfo) MarshalJSON() ([]byte, error) {
	type NoMethod OpenInfo
	return gensupport.MarshalJSON(NoMethod(s), s.ForceSendFields, s.NullFields)
}

// PhoneNumbers: A collection of phone numbers for the business. During
// updates, both fields must be set. Clients may not update just the primary or
// additional phone numbers using the update mask. International phone format
// is preferred, such as "****** 555 0132", see more in
// (https://developers.google.com/style/phone-numbers#international-phone-numbers).
type PhoneNumbers struct {
	// AdditionalPhones: Optional. Up to two phone numbers (mobile or landline, no
	// fax) at which your business can be called, in addition to your primary phone
	// number.
	AdditionalPhones []string `json:"additionalPhones,omitempty"`
	// PrimaryPhone: Required. A phone number that connects to your individual
	// business location as directly as possible. Use a local phone number instead
	// of a central, call center helpline number whenever possible.
	PrimaryPhone string `json:"primaryPhone,omitempty"`
	// ForceSendFields is a list of field names (e.g. "AdditionalPhones") to
	// unconditionally include in API requests. By default, fields with empty or
	// default values are omitted from API requests. See
	// https://pkg.go.dev/google.golang.org/api#hdr-ForceSendFields for more
	// details.
	ForceSendFields []string `json:"-"`
	// NullFields is a list of field names (e.g. "AdditionalPhones") to include in
	// API requests with the JSON null value. By default, fields with empty values
	// are omitted from API requests. See
	// https://pkg.go.dev/google.golang.org/api#hdr-NullFields for more details.
	NullFields []string `json:"-"`
}

func (s PhoneNumbers) MarshalJSON() ([]byte, error) {
	type NoMethod PhoneNumbers
	return gensupport.MarshalJSON(NoMethod(s), s.ForceSendFields, s.NullFields)
}

// PlaceInfo: Defines an area that's represented by a place ID.
type PlaceInfo struct {
	// PlaceId: Required. The ID of the place. Must correspond to a region.
	// (https://developers.google.com/places/web-service/supported_types#table3)
	PlaceId string `json:"placeId,omitempty"`
	// PlaceName: Required. The localized name of the place. For example,
	// `Scottsdale, AZ`.
	PlaceName string `json:"placeName,omitempty"`
	// ForceSendFields is a list of field names (e.g. "PlaceId") to unconditionally
	// include in API requests. By default, fields with empty or default values are
	// omitted from API requests. See
	// https://pkg.go.dev/google.golang.org/api#hdr-ForceSendFields for more
	// details.
	ForceSendFields []string `json:"-"`
	// NullFields is a list of field names (e.g. "PlaceId") to include in API
	// requests with the JSON null value. By default, fields with empty values are
	// omitted from API requests. See
	// https://pkg.go.dev/google.golang.org/api#hdr-NullFields for more details.
	NullFields []string `json:"-"`
}

func (s PlaceInfo) MarshalJSON() ([]byte, error) {
	type NoMethod PlaceInfo
	return gensupport.MarshalJSON(NoMethod(s), s.ForceSendFields, s.NullFields)
}

// Places: Defines the union of areas represented by a set of places.
type Places struct {
	// PlaceInfos: The areas represented by place IDs. Limited to a maximum of 20
	// places.
	PlaceInfos []*PlaceInfo `json:"placeInfos,omitempty"`
	// ForceSendFields is a list of field names (e.g. "PlaceInfos") to
	// unconditionally include in API requests. By default, fields with empty or
	// default values are omitted from API requests. See
	// https://pkg.go.dev/google.golang.org/api#hdr-ForceSendFields for more
	// details.
	ForceSendFields []string `json:"-"`
	// NullFields is a list of field names (e.g. "PlaceInfos") to include in API
	// requests with the JSON null value. By default, fields with empty values are
	// omitted from API requests. See
	// https://pkg.go.dev/google.golang.org/api#hdr-NullFields for more details.
	NullFields []string `json:"-"`
}

func (s Places) MarshalJSON() ([]byte, error) {
	type NoMethod Places
	return gensupport.MarshalJSON(NoMethod(s), s.ForceSendFields, s.NullFields)
}

// PostalAddress: Represents a postal address. For example for postal delivery
// or payments addresses. Given a postal address, a postal service can deliver
// items to a premise, P.O. Box or similar. It is not intended to model
// geographical locations (roads, towns, mountains). In typical usage an
// address would be created by user input or from importing existing data,
// depending on the type of process. Advice on address input / editing: - Use
// an internationalization-ready address widget such as
// https://github.com/google/libaddressinput) - Users should not be presented
// with UI elements for input or editing of fields outside countries where that
// field is used. For more guidance on how to use this schema, see:
// https://support.google.com/business/answer/6397478
type PostalAddress struct {
	// AddressLines: Unstructured address lines describing the lower levels of an
	// address. Because values in address_lines do not have type information and
	// may sometimes contain multiple values in a single field (For example
	// "Austin, TX"), it is important that the line order is clear. The order of
	// address lines should be "envelope order" for the country/region of the
	// address. In places where this can vary (For example Japan), address_language
	// is used to make it explicit (For example "ja" for large-to-small ordering
	// and "ja-Latn" or "en" for small-to-large). This way, the most specific line
	// of an address can be selected based on the language. The minimum permitted
	// structural representation of an address consists of a region_code with all
	// remaining information placed in the address_lines. It would be possible to
	// format such an address very approximately without geocoding, but no semantic
	// reasoning could be made about any of the address components until it was at
	// least partially resolved. Creating an address only containing a region_code
	// and address_lines, and then geocoding is the recommended way to handle
	// completely unstructured addresses (as opposed to guessing which parts of the
	// address should be localities or administrative areas).
	AddressLines []string `json:"addressLines,omitempty"`
	// AdministrativeArea: Optional. Highest administrative subdivision which is
	// used for postal addresses of a country or region. For example, this can be a
	// state, a province, an oblast, or a prefecture. Specifically, for Spain this
	// is the province and not the autonomous community (For example "Barcelona"
	// and not "Catalonia"). Many countries don't use an administrative area in
	// postal addresses. For example in Switzerland this should be left
	// unpopulated.
	AdministrativeArea string `json:"administrativeArea,omitempty"`
	// LanguageCode: Optional. BCP-47 language code of the contents of this address
	// (if known). This is often the UI language of the input form or is expected
	// to match one of the languages used in the address' country/region, or their
	// transliterated equivalents. This can affect formatting in certain countries,
	// but is not critical to the correctness of the data and will never affect any
	// validation or other non-formatting related operations. If this value is not
	// known, it should be omitted (rather than specifying a possibly incorrect
	// default). Examples: "zh-Hant", "ja", "ja-Latn", "en".
	LanguageCode string `json:"languageCode,omitempty"`
	// Locality: Optional. Generally refers to the city/town portion of the
	// address. Examples: US city, IT comune, UK post town. In regions of the world
	// where localities are not well defined or do not fit into this structure
	// well, leave locality empty and use address_lines.
	Locality string `json:"locality,omitempty"`
	// Organization: Optional. The name of the organization at the address.
	Organization string `json:"organization,omitempty"`
	// PostalCode: Optional. Postal code of the address. Not all countries use or
	// require postal codes to be present, but where they are used, they may
	// trigger additional validation with other parts of the address (For example
	// state/zip validation in the U.S.A.).
	PostalCode string `json:"postalCode,omitempty"`
	// Recipients: Optional. The recipient at the address. This field may, under
	// certain circumstances, contain multiline information. For example, it might
	// contain "care of" information.
	Recipients []string `json:"recipients,omitempty"`
	// RegionCode: Required. CLDR region code of the country/region of the address.
	// This is never inferred and it is up to the user to ensure the value is
	// correct. See https://cldr.unicode.org/ and
	// https://www.unicode.org/cldr/charts/30/supplemental/territory_information.html
	// for details. Example: "CH" for Switzerland.
	RegionCode string `json:"regionCode,omitempty"`
	// Revision: The schema revision of the `PostalAddress`. This must be set to 0,
	// which is the latest revision. All new revisions **must** be backward
	// compatible with old revisions.
	Revision int64 `json:"revision,omitempty"`
	// SortingCode: Optional. Additional, country-specific, sorting code. This is
	// not used in most regions. Where it is used, the value is either a string
	// like "CEDEX", optionally followed by a number (For example "CEDEX 7"), or
	// just a number alone, representing the "sector code" (Jamaica), "delivery
	// area indicator" (Malawi) or "post office indicator" (For example Côte
	// d'Ivoire).
	SortingCode string `json:"sortingCode,omitempty"`
	// Sublocality: Optional. Sublocality of the address. For example, this can be
	// neighborhoods, boroughs, districts.
	Sublocality string `json:"sublocality,omitempty"`
	// ForceSendFields is a list of field names (e.g. "AddressLines") to
	// unconditionally include in API requests. By default, fields with empty or
	// default values are omitted from API requests. See
	// https://pkg.go.dev/google.golang.org/api#hdr-ForceSendFields for more
	// details.
	ForceSendFields []string `json:"-"`
	// NullFields is a list of field names (e.g. "AddressLines") to include in API
	// requests with the JSON null value. By default, fields with empty values are
	// omitted from API requests. See
	// https://pkg.go.dev/google.golang.org/api#hdr-NullFields for more details.
	NullFields []string `json:"-"`
}

func (s PostalAddress) MarshalJSON() ([]byte, error) {
	type NoMethod PostalAddress
	return gensupport.MarshalJSON(NoMethod(s), s.ForceSendFields, s.NullFields)
}

// Profile: All information pertaining to the location's profile.
type Profile struct {
	// Description: Required. Description of the location in your own voice, not
	// editable by anyone else.
	Description string `json:"description,omitempty"`
	// ForceSendFields is a list of field names (e.g. "Description") to
	// unconditionally include in API requests. By default, fields with empty or
	// default values are omitted from API requests. See
	// https://pkg.go.dev/google.golang.org/api#hdr-ForceSendFields for more
	// details.
	ForceSendFields []string `json:"-"`
	// NullFields is a list of field names (e.g. "Description") to include in API
	// requests with the JSON null value. By default, fields with empty values are
	// omitted from API requests. See
	// https://pkg.go.dev/google.golang.org/api#hdr-NullFields for more details.
	NullFields []string `json:"-"`
}

func (s Profile) MarshalJSON() ([]byte, error) {
	type NoMethod Profile
	return gensupport.MarshalJSON(NoMethod(s), s.ForceSendFields, s.NullFields)
}

// RelationshipData: Information of all parent and children locations related
// to this one.
type RelationshipData struct {
	// ChildrenLocations: The list of children locations that this location has
	// relations with.
	ChildrenLocations []*RelevantLocation `json:"childrenLocations,omitempty"`
	// ParentChain: The resource name of the Chain that this location is member of.
	// How to find Chain ID
	ParentChain string `json:"parentChain,omitempty"`
	// ParentLocation: The parent location that this location has relations with.
	ParentLocation *RelevantLocation `json:"parentLocation,omitempty"`
	// ForceSendFields is a list of field names (e.g. "ChildrenLocations") to
	// unconditionally include in API requests. By default, fields with empty or
	// default values are omitted from API requests. See
	// https://pkg.go.dev/google.golang.org/api#hdr-ForceSendFields for more
	// details.
	ForceSendFields []string `json:"-"`
	// NullFields is a list of field names (e.g. "ChildrenLocations") to include in
	// API requests with the JSON null value. By default, fields with empty values
	// are omitted from API requests. See
	// https://pkg.go.dev/google.golang.org/api#hdr-NullFields for more details.
	NullFields []string `json:"-"`
}

func (s RelationshipData) MarshalJSON() ([]byte, error) {
	type NoMethod RelationshipData
	return gensupport.MarshalJSON(NoMethod(s), s.ForceSendFields, s.NullFields)
}

// RelevantLocation: Information about another location that is related to
// current one. The relation can be any one of DEPARTMENT_OF or
// INDEPENDENT_ESTABLISHMENT_OF, and the location specified here can be on
// either side (parent/child) of the location.
type RelevantLocation struct {
	// PlaceId: Required. Specify the location that is on the other side of the
	// relation by its placeID.
	PlaceId string `json:"placeId,omitempty"`
	// RelationType: Required. The type of the relationship.
	//
	// Possible values:
	//   "RELATION_TYPE_UNSPECIFIED" - Type unspecified.
	//   "DEPARTMENT_OF" - This represents a relation between 2 locations which
	// share one physical area, same brand/upper management/organization, but with
	// different key attributes like store hours or phone numbers. For example,
	// Costco Pharmacy is a department in Costco Wholesale.
	//   "INDEPENDENT_ESTABLISHMENT_IN" - This represents the cases where 2
	// locations are co-located in the same physical location, but from different
	// companies (e.g. Starbucks in a Safeway, shops in a mall).
	RelationType string `json:"relationType,omitempty"`
	// ForceSendFields is a list of field names (e.g. "PlaceId") to unconditionally
	// include in API requests. By default, fields with empty or default values are
	// omitted from API requests. See
	// https://pkg.go.dev/google.golang.org/api#hdr-ForceSendFields for more
	// details.
	ForceSendFields []string `json:"-"`
	// NullFields is a list of field names (e.g. "PlaceId") to include in API
	// requests with the JSON null value. By default, fields with empty values are
	// omitted from API requests. See
	// https://pkg.go.dev/google.golang.org/api#hdr-NullFields for more details.
	NullFields []string `json:"-"`
}

func (s RelevantLocation) MarshalJSON() ([]byte, error) {
	type NoMethod RelevantLocation
	return gensupport.MarshalJSON(NoMethod(s), s.ForceSendFields, s.NullFields)
}

// RepeatedEnumAttributeValue: Values for an attribute with a `value_type` of
// REPEATED_ENUM. This consists of two lists of value IDs: those that are set
// (true) and those that are unset (false). Values absent are considered
// unknown. At least one value must be specified.
type RepeatedEnumAttributeValue struct {
	// SetValues: Enum values that are set.
	SetValues []string `json:"setValues,omitempty"`
	// UnsetValues: Enum values that are unset.
	UnsetValues []string `json:"unsetValues,omitempty"`
	// ForceSendFields is a list of field names (e.g. "SetValues") to
	// unconditionally include in API requests. By default, fields with empty or
	// default values are omitted from API requests. See
	// https://pkg.go.dev/google.golang.org/api#hdr-ForceSendFields for more
	// details.
	ForceSendFields []string `json:"-"`
	// NullFields is a list of field names (e.g. "SetValues") to include in API
	// requests with the JSON null value. By default, fields with empty values are
	// omitted from API requests. See
	// https://pkg.go.dev/google.golang.org/api#hdr-NullFields for more details.
	NullFields []string `json:"-"`
}

func (s RepeatedEnumAttributeValue) MarshalJSON() ([]byte, error) {
	type NoMethod RepeatedEnumAttributeValue
	return gensupport.MarshalJSON(NoMethod(s), s.ForceSendFields, s.NullFields)
}

// SearchChainsResponse: Response message for Locations.SearchChains.
type SearchChainsResponse struct {
	// Chains: Chains that match the queried chain_display_name in
	// SearchChainsRequest. If there are no matches, this field will be empty.
	// Results are listed in order of relevance.
	Chains []*Chain `json:"chains,omitempty"`

	// ServerResponse contains the HTTP response code and headers from the server.
	googleapi.ServerResponse `json:"-"`
	// ForceSendFields is a list of field names (e.g. "Chains") to unconditionally
	// include in API requests. By default, fields with empty or default values are
	// omitted from API requests. See
	// https://pkg.go.dev/google.golang.org/api#hdr-ForceSendFields for more
	// details.
	ForceSendFields []string `json:"-"`
	// NullFields is a list of field names (e.g. "Chains") to include in API
	// requests with the JSON null value. By default, fields with empty values are
	// omitted from API requests. See
	// https://pkg.go.dev/google.golang.org/api#hdr-NullFields for more details.
	NullFields []string `json:"-"`
}

func (s SearchChainsResponse) MarshalJSON() ([]byte, error) {
	type NoMethod SearchChainsResponse
	return gensupport.MarshalJSON(NoMethod(s), s.ForceSendFields, s.NullFields)
}

// SearchGoogleLocationsRequest: Request message for
// GoogleLocations.SearchGoogleLocations.
type SearchGoogleLocationsRequest struct {
	// Location: Location to search for. If provided, will find locations which
	// match the provided location details.
	Location *Location `json:"location,omitempty"`
	// PageSize: The number of matches to return. The default value is 3, with a
	// maximum of 10. Note that latency may increase if more are requested. There
	// is no pagination.
	PageSize int64 `json:"pageSize,omitempty"`
	// Query: Text query to search for. The search results from a query string will
	// be less accurate than if providing an exact location, but can provide more
	// inexact matches.
	Query string `json:"query,omitempty"`
	// ForceSendFields is a list of field names (e.g. "Location") to
	// unconditionally include in API requests. By default, fields with empty or
	// default values are omitted from API requests. See
	// https://pkg.go.dev/google.golang.org/api#hdr-ForceSendFields for more
	// details.
	ForceSendFields []string `json:"-"`
	// NullFields is a list of field names (e.g. "Location") to include in API
	// requests with the JSON null value. By default, fields with empty values are
	// omitted from API requests. See
	// https://pkg.go.dev/google.golang.org/api#hdr-NullFields for more details.
	NullFields []string `json:"-"`
}

func (s SearchGoogleLocationsRequest) MarshalJSON() ([]byte, error) {
	type NoMethod SearchGoogleLocationsRequest
	return gensupport.MarshalJSON(NoMethod(s), s.ForceSendFields, s.NullFields)
}

// SearchGoogleLocationsResponse: Response message for
// GoogleLocations.SearchGoogleLocations.
type SearchGoogleLocationsResponse struct {
	// GoogleLocations: A collection of GoogleLocations that are potential matches
	// to the specified request, listed in order from most to least accuracy.
	GoogleLocations []*GoogleLocation `json:"googleLocations,omitempty"`

	// ServerResponse contains the HTTP response code and headers from the server.
	googleapi.ServerResponse `json:"-"`
	// ForceSendFields is a list of field names (e.g. "GoogleLocations") to
	// unconditionally include in API requests. By default, fields with empty or
	// default values are omitted from API requests. See
	// https://pkg.go.dev/google.golang.org/api#hdr-ForceSendFields for more
	// details.
	ForceSendFields []string `json:"-"`
	// NullFields is a list of field names (e.g. "GoogleLocations") to include in
	// API requests with the JSON null value. By default, fields with empty values
	// are omitted from API requests. See
	// https://pkg.go.dev/google.golang.org/api#hdr-NullFields for more details.
	NullFields []string `json:"-"`
}

func (s SearchGoogleLocationsResponse) MarshalJSON() ([]byte, error) {
	type NoMethod SearchGoogleLocationsResponse
	return gensupport.MarshalJSON(NoMethod(s), s.ForceSendFields, s.NullFields)
}

// ServiceAreaBusiness: Service area businesses provide their service at the
// customer's location (for example, a locksmith or plumber).
type ServiceAreaBusiness struct {
	// BusinessType: Required. Indicates the type of the service area business.
	//
	// Possible values:
	//   "BUSINESS_TYPE_UNSPECIFIED" - Output only. Not specified.
	//   "CUSTOMER_LOCATION_ONLY" - Offers service only in the surrounding area
	// (not at the business address). If a business is being updated from a
	// CUSTOMER_AND_BUSINESS_LOCATION to a CUSTOMER_LOCATION_ONLY, the location
	// update must include field mask `storefront_address` and set the field to
	// empty.
	//   "CUSTOMER_AND_BUSINESS_LOCATION" - Offers service at the business address
	// and the surrounding area.
	BusinessType string `json:"businessType,omitempty"`
	// Places: The area that this business serves defined through a set of places.
	Places *Places `json:"places,omitempty"`
	// RegionCode: Immutable. CLDR region code of the country/region that this
	// service area business is based in. See http://cldr.unicode.org/ and
	// http://www.unicode.org/cldr/charts/30/supplemental/territory_information.html
	// for details. Example: "CH" for Switzerland. This field is required for
	// CUSTOMER_LOCATION_ONLY businesses, and is ignored otherwise. The region
	// specified here can be different from regions for the areas that this
	// business serves (e.g. service area businesses that provide services in
	// regions other than the one that they are based in). If this location
	// requires verification after creation, the address provided for verification
	// purposes *must* be located within this region, and the business owner or
	// their authorized representative *must* be able to receive postal mail at the
	// provided verification address.
	RegionCode string `json:"regionCode,omitempty"`
	// ForceSendFields is a list of field names (e.g. "BusinessType") to
	// unconditionally include in API requests. By default, fields with empty or
	// default values are omitted from API requests. See
	// https://pkg.go.dev/google.golang.org/api#hdr-ForceSendFields for more
	// details.
	ForceSendFields []string `json:"-"`
	// NullFields is a list of field names (e.g. "BusinessType") to include in API
	// requests with the JSON null value. By default, fields with empty values are
	// omitted from API requests. See
	// https://pkg.go.dev/google.golang.org/api#hdr-NullFields for more details.
	NullFields []string `json:"-"`
}

func (s ServiceAreaBusiness) MarshalJSON() ([]byte, error) {
	type NoMethod ServiceAreaBusiness
	return gensupport.MarshalJSON(NoMethod(s), s.ForceSendFields, s.NullFields)
}

// ServiceItem: A message that describes a single service item. It is used to
// describe the type of service that the merchant provides. For example,
// haircut can be a service.
type ServiceItem struct {
	// FreeFormServiceItem: Optional. This field will be set case of free-form
	// services data.
	FreeFormServiceItem *FreeFormServiceItem `json:"freeFormServiceItem,omitempty"`
	// Price: Optional. Represents the monetary price of the service item. We
	// recommend that currency_code and units should be set when including a price.
	// This will be treated as a fixed price for the service item.
	Price *Money `json:"price,omitempty"`
	// StructuredServiceItem: Optional. This field will be set case of structured
	// services data.
	StructuredServiceItem *StructuredServiceItem `json:"structuredServiceItem,omitempty"`
	// ForceSendFields is a list of field names (e.g. "FreeFormServiceItem") to
	// unconditionally include in API requests. By default, fields with empty or
	// default values are omitted from API requests. See
	// https://pkg.go.dev/google.golang.org/api#hdr-ForceSendFields for more
	// details.
	ForceSendFields []string `json:"-"`
	// NullFields is a list of field names (e.g. "FreeFormServiceItem") to include
	// in API requests with the JSON null value. By default, fields with empty
	// values are omitted from API requests. See
	// https://pkg.go.dev/google.golang.org/api#hdr-NullFields for more details.
	NullFields []string `json:"-"`
}

func (s ServiceItem) MarshalJSON() ([]byte, error) {
	type NoMethod ServiceItem
	return gensupport.MarshalJSON(NoMethod(s), s.ForceSendFields, s.NullFields)
}

// ServiceType: A message describing a service type that the business offers.
type ServiceType struct {
	// DisplayName: Output only. The human-readable display name for the service
	// type.
	DisplayName string `json:"displayName,omitempty"`
	// ServiceTypeId: Output only. A stable ID (provided by Google) for this
	// service type.
	ServiceTypeId string `json:"serviceTypeId,omitempty"`
	// ForceSendFields is a list of field names (e.g. "DisplayName") to
	// unconditionally include in API requests. By default, fields with empty or
	// default values are omitted from API requests. See
	// https://pkg.go.dev/google.golang.org/api#hdr-ForceSendFields for more
	// details.
	ForceSendFields []string `json:"-"`
	// NullFields is a list of field names (e.g. "DisplayName") to include in API
	// requests with the JSON null value. By default, fields with empty values are
	// omitted from API requests. See
	// https://pkg.go.dev/google.golang.org/api#hdr-NullFields for more details.
	NullFields []string `json:"-"`
}

func (s ServiceType) MarshalJSON() ([]byte, error) {
	type NoMethod ServiceType
	return gensupport.MarshalJSON(NoMethod(s), s.ForceSendFields, s.NullFields)
}

// SpecialHourPeriod: Represents a single time period when a location's
// operational hours differ from its normal business hours. A special hour
// period must represent a range of less than 24 hours. The `open_time` and
// `start_date` must predate the `close_time` and `end_date`. The `close_time`
// and `end_date` can extend to 11:59 a.m. on the day after the specified
// `start_date`. For example, the following inputs are valid:
// start_date=2015-11-23, open_time=08:00, close_time=18:00
// start_date=2015-11-23, end_date=2015-11-23, open_time=08:00,
// close_time=18:00 start_date=2015-11-23, end_date=2015-11-24,
// open_time=13:00, close_time=11:59 The following inputs are not valid:
// start_date=2015-11-23, open_time=13:00, close_time=11:59
// start_date=2015-11-23, end_date=2015-11-24, open_time=13:00,
// close_time=12:00 start_date=2015-11-23, end_date=2015-11-25,
// open_time=08:00, close_time=18:00
type SpecialHourPeriod struct {
	// CloseTime: Optional. Valid values are 00:00-24:00, where 24:00 represents
	// midnight at the end of the specified day field. Must be specified if
	// `closed` is false.
	CloseTime *TimeOfDay `json:"closeTime,omitempty"`
	// Closed: Optional. If true, `end_date`, `open_time`, and `close_time` are
	// ignored, and the date specified in `start_date` is treated as the location
	// being closed for the entire day.
	Closed bool `json:"closed,omitempty"`
	// EndDate: Optional. The calendar date this special hour period ends on. If
	// `end_date` field is not set, default to the date specified in `start_date`.
	// If set, this field must be equal to or at most 1 day after `start_date`.
	EndDate *Date `json:"endDate,omitempty"`
	// OpenTime: Optional. Valid values are 00:00-24:00 where 24:00 represents
	// midnight at the end of the specified day field. Must be specified if
	// `closed` is false.
	OpenTime *TimeOfDay `json:"openTime,omitempty"`
	// StartDate: Required. The calendar date this special hour period starts on.
	StartDate *Date `json:"startDate,omitempty"`
	// ForceSendFields is a list of field names (e.g. "CloseTime") to
	// unconditionally include in API requests. By default, fields with empty or
	// default values are omitted from API requests. See
	// https://pkg.go.dev/google.golang.org/api#hdr-ForceSendFields for more
	// details.
	ForceSendFields []string `json:"-"`
	// NullFields is a list of field names (e.g. "CloseTime") to include in API
	// requests with the JSON null value. By default, fields with empty values are
	// omitted from API requests. See
	// https://pkg.go.dev/google.golang.org/api#hdr-NullFields for more details.
	NullFields []string `json:"-"`
}

func (s SpecialHourPeriod) MarshalJSON() ([]byte, error) {
	type NoMethod SpecialHourPeriod
	return gensupport.MarshalJSON(NoMethod(s), s.ForceSendFields, s.NullFields)
}

// SpecialHours: Represents a set of time periods when a location's operational
// hours differ from its normal business hours.
type SpecialHours struct {
	// SpecialHourPeriods: Required. A list of exceptions to the business's regular
	// hours.
	SpecialHourPeriods []*SpecialHourPeriod `json:"specialHourPeriods,omitempty"`
	// ForceSendFields is a list of field names (e.g. "SpecialHourPeriods") to
	// unconditionally include in API requests. By default, fields with empty or
	// default values are omitted from API requests. See
	// https://pkg.go.dev/google.golang.org/api#hdr-ForceSendFields for more
	// details.
	ForceSendFields []string `json:"-"`
	// NullFields is a list of field names (e.g. "SpecialHourPeriods") to include
	// in API requests with the JSON null value. By default, fields with empty
	// values are omitted from API requests. See
	// https://pkg.go.dev/google.golang.org/api#hdr-NullFields for more details.
	NullFields []string `json:"-"`
}

func (s SpecialHours) MarshalJSON() ([]byte, error) {
	type NoMethod SpecialHours
	return gensupport.MarshalJSON(NoMethod(s), s.ForceSendFields, s.NullFields)
}

// StructuredServiceItem: Represents a structured service offered by the
// merchant. For eg: toilet_installation.
type StructuredServiceItem struct {
	// Description: Optional. Description of structured service item. The character
	// limit is 300.
	Description string `json:"description,omitempty"`
	// ServiceTypeId: Required. The `service_type_id` field is a Google provided
	// unique ID that can be found in `ServiceType`. This information is provided
	// by `BatchGetCategories` rpc service.
	ServiceTypeId string `json:"serviceTypeId,omitempty"`
	// ForceSendFields is a list of field names (e.g. "Description") to
	// unconditionally include in API requests. By default, fields with empty or
	// default values are omitted from API requests. See
	// https://pkg.go.dev/google.golang.org/api#hdr-ForceSendFields for more
	// details.
	ForceSendFields []string `json:"-"`
	// NullFields is a list of field names (e.g. "Description") to include in API
	// requests with the JSON null value. By default, fields with empty values are
	// omitted from API requests. See
	// https://pkg.go.dev/google.golang.org/api#hdr-NullFields for more details.
	NullFields []string `json:"-"`
}

func (s StructuredServiceItem) MarshalJSON() ([]byte, error) {
	type NoMethod StructuredServiceItem
	return gensupport.MarshalJSON(NoMethod(s), s.ForceSendFields, s.NullFields)
}

// TimeOfDay: Represents a time of day. The date and time zone are either not
// significant or are specified elsewhere. An API may choose to allow leap
// seconds. Related types are google.type.Date and `google.protobuf.Timestamp`.
type TimeOfDay struct {
	// Hours: Hours of a day in 24 hour format. Must be greater than or equal to 0
	// and typically must be less than or equal to 23. An API may choose to allow
	// the value "24:00:00" for scenarios like business closing time.
	Hours int64 `json:"hours,omitempty"`
	// Minutes: Minutes of an hour. Must be greater than or equal to 0 and less
	// than or equal to 59.
	Minutes int64 `json:"minutes,omitempty"`
	// Nanos: Fractions of seconds, in nanoseconds. Must be greater than or equal
	// to 0 and less than or equal to 999,999,999.
	Nanos int64 `json:"nanos,omitempty"`
	// Seconds: Seconds of a minute. Must be greater than or equal to 0 and
	// typically must be less than or equal to 59. An API may allow the value 60 if
	// it allows leap-seconds.
	Seconds int64 `json:"seconds,omitempty"`
	// ForceSendFields is a list of field names (e.g. "Hours") to unconditionally
	// include in API requests. By default, fields with empty or default values are
	// omitted from API requests. See
	// https://pkg.go.dev/google.golang.org/api#hdr-ForceSendFields for more
	// details.
	ForceSendFields []string `json:"-"`
	// NullFields is a list of field names (e.g. "Hours") to include in API
	// requests with the JSON null value. By default, fields with empty values are
	// omitted from API requests. See
	// https://pkg.go.dev/google.golang.org/api#hdr-NullFields for more details.
	NullFields []string `json:"-"`
}

func (s TimeOfDay) MarshalJSON() ([]byte, error) {
	type NoMethod TimeOfDay
	return gensupport.MarshalJSON(NoMethod(s), s.ForceSendFields, s.NullFields)
}

// TimePeriod: Represents a span of time that the business is open, starting on
// the specified open day/time and closing on the specified close day/time. The
// closing time must occur after the opening time, for example later in the
// same day, or on a subsequent day.
type TimePeriod struct {
	// CloseDay: Required. Indicates the day of the week this period ends on.
	//
	// Possible values:
	//   "DAY_OF_WEEK_UNSPECIFIED" - The day of the week is unspecified.
	//   "MONDAY" - Monday
	//   "TUESDAY" - Tuesday
	//   "WEDNESDAY" - Wednesday
	//   "THURSDAY" - Thursday
	//   "FRIDAY" - Friday
	//   "SATURDAY" - Saturday
	//   "SUNDAY" - Sunday
	CloseDay string `json:"closeDay,omitempty"`
	// CloseTime: Required. Valid values are 00:00-24:00, where 24:00 represents
	// midnight at the end of the specified day field.
	CloseTime *TimeOfDay `json:"closeTime,omitempty"`
	// OpenDay: Required. Indicates the day of the week this period starts on.
	//
	// Possible values:
	//   "DAY_OF_WEEK_UNSPECIFIED" - The day of the week is unspecified.
	//   "MONDAY" - Monday
	//   "TUESDAY" - Tuesday
	//   "WEDNESDAY" - Wednesday
	//   "THURSDAY" - Thursday
	//   "FRIDAY" - Friday
	//   "SATURDAY" - Saturday
	//   "SUNDAY" - Sunday
	OpenDay string `json:"openDay,omitempty"`
	// OpenTime: Required. Valid values are 00:00-24:00, where 24:00 represents
	// midnight at the end of the specified day field.
	OpenTime *TimeOfDay `json:"openTime,omitempty"`
	// ForceSendFields is a list of field names (e.g. "CloseDay") to
	// unconditionally include in API requests. By default, fields with empty or
	// default values are omitted from API requests. See
	// https://pkg.go.dev/google.golang.org/api#hdr-ForceSendFields for more
	// details.
	ForceSendFields []string `json:"-"`
	// NullFields is a list of field names (e.g. "CloseDay") to include in API
	// requests with the JSON null value. By default, fields with empty values are
	// omitted from API requests. See
	// https://pkg.go.dev/google.golang.org/api#hdr-NullFields for more details.
	NullFields []string `json:"-"`
}

func (s TimePeriod) MarshalJSON() ([]byte, error) {
	type NoMethod TimePeriod
	return gensupport.MarshalJSON(NoMethod(s), s.ForceSendFields, s.NullFields)
}

// UriAttributeValue: Values for an attribute with a `value_type` of URL.
type UriAttributeValue struct {
	// Uri: Required. The proposed URI value for this attribute.
	Uri string `json:"uri,omitempty"`
	// ForceSendFields is a list of field names (e.g. "Uri") to unconditionally
	// include in API requests. By default, fields with empty or default values are
	// omitted from API requests. See
	// https://pkg.go.dev/google.golang.org/api#hdr-ForceSendFields for more
	// details.
	ForceSendFields []string `json:"-"`
	// NullFields is a list of field names (e.g. "Uri") to include in API requests
	// with the JSON null value. By default, fields with empty values are omitted
	// from API requests. See
	// https://pkg.go.dev/google.golang.org/api#hdr-NullFields for more details.
	NullFields []string `json:"-"`
}

func (s UriAttributeValue) MarshalJSON() ([]byte, error) {
	type NoMethod UriAttributeValue
	return gensupport.MarshalJSON(NoMethod(s), s.ForceSendFields, s.NullFields)
}

type AccountsLocationsCreateCall struct {
	s          *Service
	parent     string
	location   *Location
	urlParams_ gensupport.URLParams
	ctx_       context.Context
	header_    http.Header
}

// Create: Creates a new Location that will be owned by the logged in user.
//
// - parent: The name of the account in which to create this location.
func (r *AccountsLocationsService) Create(parent string, location *Location) *AccountsLocationsCreateCall {
	c := &AccountsLocationsCreateCall{s: r.s, urlParams_: make(gensupport.URLParams)}
	c.parent = parent
	c.location = location
	return c
}

// RequestId sets the optional parameter "requestId": A unique request ID for
// the server to detect duplicated requests. We recommend using UUIDs. Max
// length is 50 characters.
func (c *AccountsLocationsCreateCall) RequestId(requestId string) *AccountsLocationsCreateCall {
	c.urlParams_.Set("requestId", requestId)
	return c
}

// ValidateOnly sets the optional parameter "validateOnly": If true, the
// request is validated without actually creating the location.
func (c *AccountsLocationsCreateCall) ValidateOnly(validateOnly bool) *AccountsLocationsCreateCall {
	c.urlParams_.Set("validateOnly", fmt.Sprint(validateOnly))
	return c
}

// Fields allows partial responses to be retrieved. See
// https://developers.google.com/gdata/docs/2.0/basics#PartialResponse for more
// details.
func (c *AccountsLocationsCreateCall) Fields(s ...googleapi.Field) *AccountsLocationsCreateCall {
	c.urlParams_.Set("fields", googleapi.CombineFields(s))
	return c
}

// Context sets the context to be used in this call's Do method.
func (c *AccountsLocationsCreateCall) Context(ctx context.Context) *AccountsLocationsCreateCall {
	c.ctx_ = ctx
	return c
}

// Header returns a http.Header that can be modified by the caller to add
// headers to the request.
func (c *AccountsLocationsCreateCall) Header() http.Header {
	if c.header_ == nil {
		c.header_ = make(http.Header)
	}
	return c.header_
}

func (c *AccountsLocationsCreateCall) doRequest(alt string) (*http.Response, error) {
	reqHeaders := gensupport.SetHeaders(c.s.userAgent(), "application/json", c.header_)
	var body io.Reader = nil
	body, err := googleapi.WithoutDataWrapper.JSONReader(c.location)
	if err != nil {
		return nil, err
	}
	c.urlParams_.Set("alt", alt)
	c.urlParams_.Set("prettyPrint", "false")
	urls := googleapi.ResolveRelative(c.s.BasePath, "v1/{+parent}/locations")
	urls += "?" + c.urlParams_.Encode()
	req, err := http.NewRequest("POST", urls, body)
	if err != nil {
		return nil, err
	}
	req.Header = reqHeaders
	googleapi.Expand(req.URL, map[string]string{
		"parent": c.parent,
	})
	return gensupport.SendRequest(c.ctx_, c.s.client, req)
}

// Do executes the "mybusinessbusinessinformation.accounts.locations.create" call.
// Any non-2xx status code is an error. Response headers are in either
// *Location.ServerResponse.Header or (if a response was returned at all) in
// error.(*googleapi.Error).Header. Use googleapi.IsNotModified to check
// whether the returned error was because http.StatusNotModified was returned.
func (c *AccountsLocationsCreateCall) Do(opts ...googleapi.CallOption) (*Location, error) {
	gensupport.SetOptions(c.urlParams_, opts...)
	res, err := c.doRequest("json")
	if res != nil && res.StatusCode == http.StatusNotModified {
		if res.Body != nil {
			res.Body.Close()
		}
		return nil, gensupport.WrapError(&googleapi.Error{
			Code:   res.StatusCode,
			Header: res.Header,
		})
	}
	if err != nil {
		return nil, err
	}
	defer googleapi.CloseBody(res)
	if err := googleapi.CheckResponse(res); err != nil {
		return nil, gensupport.WrapError(err)
	}
	ret := &Location{
		ServerResponse: googleapi.ServerResponse{
			Header:         res.Header,
			HTTPStatusCode: res.StatusCode,
		},
	}
	target := &ret
	if err := gensupport.DecodeResponse(target, res); err != nil {
		return nil, err
	}
	return ret, nil
}

type AccountsLocationsListCall struct {
	s            *Service
	parent       string
	urlParams_   gensupport.URLParams
	ifNoneMatch_ string
	ctx_         context.Context
	header_      http.Header
}

// List: Lists the locations for the specified account.
//
//   - parent: The name of the account to fetch locations from. If the parent
//     Account is of AccountType PERSONAL, only Locations that are directly owned
//     by the Account are returned, otherwise it will return all accessible
//     locations from the Account, either directly or indirectly.
func (r *AccountsLocationsService) List(parent string) *AccountsLocationsListCall {
	c := &AccountsLocationsListCall{s: r.s, urlParams_: make(gensupport.URLParams)}
	c.parent = parent
	return c
}

// Filter sets the optional parameter "filter": A filter constraining the
// locations to return. The response includes only entries that match the
// filter. If `filter` is empty, then constraints are applied and all locations
// (paginated) are retrieved for the requested account. For more information
// about valid fields and example usage, see Work with Location Data Guide
// (https://developers.google.com/my-business/content/location-data#filter_results_when_you_list_locations).
func (c *AccountsLocationsListCall) Filter(filter string) *AccountsLocationsListCall {
	c.urlParams_.Set("filter", filter)
	return c
}

// OrderBy sets the optional parameter "orderBy": Sorting order for the
// request. Multiple fields should be comma-separated, following SQL syntax.
// The default sorting order is ascending. To specify descending order, a
// suffix " desc" should be added. Valid fields to order_by are title and
// store_code. For example: "title, store_code desc" or "title" or "store_code
// desc"
func (c *AccountsLocationsListCall) OrderBy(orderBy string) *AccountsLocationsListCall {
	c.urlParams_.Set("orderBy", orderBy)
	return c
}

// PageSize sets the optional parameter "pageSize": How many locations to fetch
// per page. Default value is 10 if not set. Minimum is 1, and maximum page
// size is 100.
func (c *AccountsLocationsListCall) PageSize(pageSize int64) *AccountsLocationsListCall {
	c.urlParams_.Set("pageSize", fmt.Sprint(pageSize))
	return c
}

// PageToken sets the optional parameter "pageToken": If specified, it fetches
// the next `page` of locations. The page token is returned by previous calls
// to `ListLocations` when there were more locations than could fit in the
// requested page size.
func (c *AccountsLocationsListCall) PageToken(pageToken string) *AccountsLocationsListCall {
	c.urlParams_.Set("pageToken", pageToken)
	return c
}

// ReadMask sets the optional parameter "readMask": Required. Read mask to
// specify what fields will be returned in the response.
func (c *AccountsLocationsListCall) ReadMask(readMask string) *AccountsLocationsListCall {
	c.urlParams_.Set("readMask", readMask)
	return c
}

// Fields allows partial responses to be retrieved. See
// https://developers.google.com/gdata/docs/2.0/basics#PartialResponse for more
// details.
func (c *AccountsLocationsListCall) Fields(s ...googleapi.Field) *AccountsLocationsListCall {
	c.urlParams_.Set("fields", googleapi.CombineFields(s))
	return c
}

// IfNoneMatch sets an optional parameter which makes the operation fail if the
// object's ETag matches the given value. This is useful for getting updates
// only after the object has changed since the last request.
func (c *AccountsLocationsListCall) IfNoneMatch(entityTag string) *AccountsLocationsListCall {
	c.ifNoneMatch_ = entityTag
	return c
}

// Context sets the context to be used in this call's Do method.
func (c *AccountsLocationsListCall) Context(ctx context.Context) *AccountsLocationsListCall {
	c.ctx_ = ctx
	return c
}

// Header returns a http.Header that can be modified by the caller to add
// headers to the request.
func (c *AccountsLocationsListCall) Header() http.Header {
	if c.header_ == nil {
		c.header_ = make(http.Header)
	}
	return c.header_
}

func (c *AccountsLocationsListCall) doRequest(alt string) (*http.Response, error) {
	reqHeaders := gensupport.SetHeaders(c.s.userAgent(), "", c.header_)
	if c.ifNoneMatch_ != "" {
		reqHeaders.Set("If-None-Match", c.ifNoneMatch_)
	}
	var body io.Reader = nil
	c.urlParams_.Set("alt", alt)
	c.urlParams_.Set("prettyPrint", "false")
	urls := googleapi.ResolveRelative(c.s.BasePath, "v1/{+parent}/locations")
	urls += "?" + c.urlParams_.Encode()
	req, err := http.NewRequest("GET", urls, body)
	if err != nil {
		return nil, err
	}
	req.Header = reqHeaders
	googleapi.Expand(req.URL, map[string]string{
		"parent": c.parent,
	})
	return gensupport.SendRequest(c.ctx_, c.s.client, req)
}

// Do executes the "mybusinessbusinessinformation.accounts.locations.list" call.
// Any non-2xx status code is an error. Response headers are in either
// *ListLocationsResponse.ServerResponse.Header or (if a response was returned
// at all) in error.(*googleapi.Error).Header. Use googleapi.IsNotModified to
// check whether the returned error was because http.StatusNotModified was
// returned.
func (c *AccountsLocationsListCall) Do(opts ...googleapi.CallOption) (*ListLocationsResponse, error) {
	gensupport.SetOptions(c.urlParams_, opts...)
	res, err := c.doRequest("json")
	if res != nil && res.StatusCode == http.StatusNotModified {
		if res.Body != nil {
			res.Body.Close()
		}
		return nil, gensupport.WrapError(&googleapi.Error{
			Code:   res.StatusCode,
			Header: res.Header,
		})
	}
	if err != nil {
		return nil, err
	}
	defer googleapi.CloseBody(res)
	if err := googleapi.CheckResponse(res); err != nil {
		return nil, gensupport.WrapError(err)
	}
	ret := &ListLocationsResponse{
		ServerResponse: googleapi.ServerResponse{
			Header:         res.Header,
			HTTPStatusCode: res.StatusCode,
		},
	}
	target := &ret
	if err := gensupport.DecodeResponse(target, res); err != nil {
		return nil, err
	}
	return ret, nil
}

// Pages invokes f for each page of results.
// A non-nil error returned from f will halt the iteration.
// The provided context supersedes any context provided to the Context method.
func (c *AccountsLocationsListCall) Pages(ctx context.Context, f func(*ListLocationsResponse) error) error {
	c.ctx_ = ctx
	defer c.PageToken(c.urlParams_.Get("pageToken"))
	for {
		x, err := c.Do()
		if err != nil {
			return err
		}
		if err := f(x); err != nil {
			return err
		}
		if x.NextPageToken == "" {
			return nil
		}
		c.PageToken(x.NextPageToken)
	}
}

type AttributesListCall struct {
	s            *Service
	urlParams_   gensupport.URLParams
	ifNoneMatch_ string
	ctx_         context.Context
	header_      http.Header
}

// List: Returns the list of attributes that would be available for a location
// with the given primary category and country.
func (r *AttributesService) List() *AttributesListCall {
	c := &AttributesListCall{s: r.s, urlParams_: make(gensupport.URLParams)}
	return c
}

// CategoryName sets the optional parameter "categoryName": The primary
// category stable ID to find available attributes. Must be of the format
// categories/{category_id}.
func (c *AttributesListCall) CategoryName(categoryName string) *AttributesListCall {
	c.urlParams_.Set("categoryName", categoryName)
	return c
}

// LanguageCode sets the optional parameter "languageCode": The BCP 47 code of
// language to get attribute display names in. If this language is not
// available, they will be provided in English.
func (c *AttributesListCall) LanguageCode(languageCode string) *AttributesListCall {
	c.urlParams_.Set("languageCode", languageCode)
	return c
}

// PageSize sets the optional parameter "pageSize": How many attributes to
// include per page. Default is 200, minimum is 1.
func (c *AttributesListCall) PageSize(pageSize int64) *AttributesListCall {
	c.urlParams_.Set("pageSize", fmt.Sprint(pageSize))
	return c
}

// PageToken sets the optional parameter "pageToken": If specified, the next
// page of attribute metadata is retrieved.
func (c *AttributesListCall) PageToken(pageToken string) *AttributesListCall {
	c.urlParams_.Set("pageToken", pageToken)
	return c
}

// Parent sets the optional parameter "parent": Resource name of the location
// to look up available attributes. If this field is set, category_name,
// region_code, language_code and show_all are not required and must not be
// set.
func (c *AttributesListCall) Parent(parent string) *AttributesListCall {
	c.urlParams_.Set("parent", parent)
	return c
}

// RegionCode sets the optional parameter "regionCode": The ISO 3166-1 alpha-2
// country code to find available attributes.
func (c *AttributesListCall) RegionCode(regionCode string) *AttributesListCall {
	c.urlParams_.Set("regionCode", regionCode)
	return c
}

// ShowAll sets the optional parameter "showAll": Metadata for all available
// attributes are returned when this field is set to true, disregarding parent
// and category_name fields. language_code and region_code are required when
// show_all is set to true.
func (c *AttributesListCall) ShowAll(showAll bool) *AttributesListCall {
	c.urlParams_.Set("showAll", fmt.Sprint(showAll))
	return c
}

// Fields allows partial responses to be retrieved. See
// https://developers.google.com/gdata/docs/2.0/basics#PartialResponse for more
// details.
func (c *AttributesListCall) Fields(s ...googleapi.Field) *AttributesListCall {
	c.urlParams_.Set("fields", googleapi.CombineFields(s))
	return c
}

// IfNoneMatch sets an optional parameter which makes the operation fail if the
// object's ETag matches the given value. This is useful for getting updates
// only after the object has changed since the last request.
func (c *AttributesListCall) IfNoneMatch(entityTag string) *AttributesListCall {
	c.ifNoneMatch_ = entityTag
	return c
}

// Context sets the context to be used in this call's Do method.
func (c *AttributesListCall) Context(ctx context.Context) *AttributesListCall {
	c.ctx_ = ctx
	return c
}

// Header returns a http.Header that can be modified by the caller to add
// headers to the request.
func (c *AttributesListCall) Header() http.Header {
	if c.header_ == nil {
		c.header_ = make(http.Header)
	}
	return c.header_
}

func (c *AttributesListCall) doRequest(alt string) (*http.Response, error) {
	reqHeaders := gensupport.SetHeaders(c.s.userAgent(), "", c.header_)
	if c.ifNoneMatch_ != "" {
		reqHeaders.Set("If-None-Match", c.ifNoneMatch_)
	}
	var body io.Reader = nil
	c.urlParams_.Set("alt", alt)
	c.urlParams_.Set("prettyPrint", "false")
	urls := googleapi.ResolveRelative(c.s.BasePath, "v1/attributes")
	urls += "?" + c.urlParams_.Encode()
	req, err := http.NewRequest("GET", urls, body)
	if err != nil {
		return nil, err
	}
	req.Header = reqHeaders
	return gensupport.SendRequest(c.ctx_, c.s.client, req)
}

// Do executes the "mybusinessbusinessinformation.attributes.list" call.
// Any non-2xx status code is an error. Response headers are in either
// *ListAttributeMetadataResponse.ServerResponse.Header or (if a response was
// returned at all) in error.(*googleapi.Error).Header. Use
// googleapi.IsNotModified to check whether the returned error was because
// http.StatusNotModified was returned.
func (c *AttributesListCall) Do(opts ...googleapi.CallOption) (*ListAttributeMetadataResponse, error) {
	gensupport.SetOptions(c.urlParams_, opts...)
	res, err := c.doRequest("json")
	if res != nil && res.StatusCode == http.StatusNotModified {
		if res.Body != nil {
			res.Body.Close()
		}
		return nil, gensupport.WrapError(&googleapi.Error{
			Code:   res.StatusCode,
			Header: res.Header,
		})
	}
	if err != nil {
		return nil, err
	}
	defer googleapi.CloseBody(res)
	if err := googleapi.CheckResponse(res); err != nil {
		return nil, gensupport.WrapError(err)
	}
	ret := &ListAttributeMetadataResponse{
		ServerResponse: googleapi.ServerResponse{
			Header:         res.Header,
			HTTPStatusCode: res.StatusCode,
		},
	}
	target := &ret
	if err := gensupport.DecodeResponse(target, res); err != nil {
		return nil, err
	}
	return ret, nil
}

// Pages invokes f for each page of results.
// A non-nil error returned from f will halt the iteration.
// The provided context supersedes any context provided to the Context method.
func (c *AttributesListCall) Pages(ctx context.Context, f func(*ListAttributeMetadataResponse) error) error {
	c.ctx_ = ctx
	defer c.PageToken(c.urlParams_.Get("pageToken"))
	for {
		x, err := c.Do()
		if err != nil {
			return err
		}
		if err := f(x); err != nil {
			return err
		}
		if x.NextPageToken == "" {
			return nil
		}
		c.PageToken(x.NextPageToken)
	}
}

type CategoriesBatchGetCall struct {
	s            *Service
	urlParams_   gensupport.URLParams
	ifNoneMatch_ string
	ctx_         context.Context
	header_      http.Header
}

// BatchGet: Returns a list of business categories for the provided language
// and GConcept ids.
func (r *CategoriesService) BatchGet() *CategoriesBatchGetCall {
	c := &CategoriesBatchGetCall{s: r.s, urlParams_: make(gensupport.URLParams)}
	return c
}

// LanguageCode sets the optional parameter "languageCode": Required. The BCP
// 47 code of language that the category names should be returned in.
func (c *CategoriesBatchGetCall) LanguageCode(languageCode string) *CategoriesBatchGetCall {
	c.urlParams_.Set("languageCode", languageCode)
	return c
}

// Names sets the optional parameter "names": Required. At least one name must
// be set. The GConcept ids the localized category names should be returned
// for. To return details for more than one category, repeat this parameter in
// the request.
func (c *CategoriesBatchGetCall) Names(names ...string) *CategoriesBatchGetCall {
	c.urlParams_.SetMulti("names", append([]string{}, names...))
	return c
}

// RegionCode sets the optional parameter "regionCode": The ISO 3166-1 alpha-2
// country code used to infer non-standard language.
func (c *CategoriesBatchGetCall) RegionCode(regionCode string) *CategoriesBatchGetCall {
	c.urlParams_.Set("regionCode", regionCode)
	return c
}

// View sets the optional parameter "view": Required. Specifies which parts to
// the Category resource should be returned in the response.
//
// Possible values:
//
//	"CATEGORY_VIEW_UNSPECIFIED" - Not specified, equivalent to
//
// CATEGORY_METADATA_ONLY.
//
//	"BASIC" - The server response will only include Category fields
//
// display_name, category_id and language_code. It omits any service type
// metadata related fields.
//
//	"FULL" - Returns all the fields in the response.
func (c *CategoriesBatchGetCall) View(view string) *CategoriesBatchGetCall {
	c.urlParams_.Set("view", view)
	return c
}

// Fields allows partial responses to be retrieved. See
// https://developers.google.com/gdata/docs/2.0/basics#PartialResponse for more
// details.
func (c *CategoriesBatchGetCall) Fields(s ...googleapi.Field) *CategoriesBatchGetCall {
	c.urlParams_.Set("fields", googleapi.CombineFields(s))
	return c
}

// IfNoneMatch sets an optional parameter which makes the operation fail if the
// object's ETag matches the given value. This is useful for getting updates
// only after the object has changed since the last request.
func (c *CategoriesBatchGetCall) IfNoneMatch(entityTag string) *CategoriesBatchGetCall {
	c.ifNoneMatch_ = entityTag
	return c
}

// Context sets the context to be used in this call's Do method.
func (c *CategoriesBatchGetCall) Context(ctx context.Context) *CategoriesBatchGetCall {
	c.ctx_ = ctx
	return c
}

// Header returns a http.Header that can be modified by the caller to add
// headers to the request.
func (c *CategoriesBatchGetCall) Header() http.Header {
	if c.header_ == nil {
		c.header_ = make(http.Header)
	}
	return c.header_
}

func (c *CategoriesBatchGetCall) doRequest(alt string) (*http.Response, error) {
	reqHeaders := gensupport.SetHeaders(c.s.userAgent(), "", c.header_)
	if c.ifNoneMatch_ != "" {
		reqHeaders.Set("If-None-Match", c.ifNoneMatch_)
	}
	var body io.Reader = nil
	c.urlParams_.Set("alt", alt)
	c.urlParams_.Set("prettyPrint", "false")
	urls := googleapi.ResolveRelative(c.s.BasePath, "v1/categories:batchGet")
	urls += "?" + c.urlParams_.Encode()
	req, err := http.NewRequest("GET", urls, body)
	if err != nil {
		return nil, err
	}
	req.Header = reqHeaders
	return gensupport.SendRequest(c.ctx_, c.s.client, req)
}

// Do executes the "mybusinessbusinessinformation.categories.batchGet" call.
// Any non-2xx status code is an error. Response headers are in either
// *BatchGetCategoriesResponse.ServerResponse.Header or (if a response was
// returned at all) in error.(*googleapi.Error).Header. Use
// googleapi.IsNotModified to check whether the returned error was because
// http.StatusNotModified was returned.
func (c *CategoriesBatchGetCall) Do(opts ...googleapi.CallOption) (*BatchGetCategoriesResponse, error) {
	gensupport.SetOptions(c.urlParams_, opts...)
	res, err := c.doRequest("json")
	if res != nil && res.StatusCode == http.StatusNotModified {
		if res.Body != nil {
			res.Body.Close()
		}
		return nil, gensupport.WrapError(&googleapi.Error{
			Code:   res.StatusCode,
			Header: res.Header,
		})
	}
	if err != nil {
		return nil, err
	}
	defer googleapi.CloseBody(res)
	if err := googleapi.CheckResponse(res); err != nil {
		return nil, gensupport.WrapError(err)
	}
	ret := &BatchGetCategoriesResponse{
		ServerResponse: googleapi.ServerResponse{
			Header:         res.Header,
			HTTPStatusCode: res.StatusCode,
		},
	}
	target := &ret
	if err := gensupport.DecodeResponse(target, res); err != nil {
		return nil, err
	}
	return ret, nil
}

type CategoriesListCall struct {
	s            *Service
	urlParams_   gensupport.URLParams
	ifNoneMatch_ string
	ctx_         context.Context
	header_      http.Header
}

// List: Returns a list of business categories. Search will match the category
// name but not the category ID. Search only matches the front of a category
// name (that is, 'food' may return 'Food Court' but not 'Fast Food
// Restaurant').
func (r *CategoriesService) List() *CategoriesListCall {
	c := &CategoriesListCall{s: r.s, urlParams_: make(gensupport.URLParams)}
	return c
}

// Filter sets the optional parameter "filter": Filter string from user. The
// only field that supported is `displayName`. Eg: `filter=displayName=foo`.
func (c *CategoriesListCall) Filter(filter string) *CategoriesListCall {
	c.urlParams_.Set("filter", filter)
	return c
}

// LanguageCode sets the optional parameter "languageCode": Required. The BCP
// 47 code of language.
func (c *CategoriesListCall) LanguageCode(languageCode string) *CategoriesListCall {
	c.urlParams_.Set("languageCode", languageCode)
	return c
}

// PageSize sets the optional parameter "pageSize": How many categories to
// fetch per page. Default is 100, minimum is 1, and maximum page size is 100.
func (c *CategoriesListCall) PageSize(pageSize int64) *CategoriesListCall {
	c.urlParams_.Set("pageSize", fmt.Sprint(pageSize))
	return c
}

// PageToken sets the optional parameter "pageToken": If specified, the next
// page of categories will be fetched.
func (c *CategoriesListCall) PageToken(pageToken string) *CategoriesListCall {
	c.urlParams_.Set("pageToken", pageToken)
	return c
}

// RegionCode sets the optional parameter "regionCode": Required. The ISO
// 3166-1 alpha-2 country code.
func (c *CategoriesListCall) RegionCode(regionCode string) *CategoriesListCall {
	c.urlParams_.Set("regionCode", regionCode)
	return c
}

// View sets the optional parameter "view": Required. Specifies which parts to
// the Category resource should be returned in the response.
//
// Possible values:
//
//	"CATEGORY_VIEW_UNSPECIFIED" - Not specified, equivalent to
//
// CATEGORY_METADATA_ONLY.
//
//	"BASIC" - The server response will only include Category fields
//
// display_name, category_id and language_code. It omits any service type
// metadata related fields.
//
//	"FULL" - Returns all the fields in the response.
func (c *CategoriesListCall) View(view string) *CategoriesListCall {
	c.urlParams_.Set("view", view)
	return c
}

// Fields allows partial responses to be retrieved. See
// https://developers.google.com/gdata/docs/2.0/basics#PartialResponse for more
// details.
func (c *CategoriesListCall) Fields(s ...googleapi.Field) *CategoriesListCall {
	c.urlParams_.Set("fields", googleapi.CombineFields(s))
	return c
}

// IfNoneMatch sets an optional parameter which makes the operation fail if the
// object's ETag matches the given value. This is useful for getting updates
// only after the object has changed since the last request.
func (c *CategoriesListCall) IfNoneMatch(entityTag string) *CategoriesListCall {
	c.ifNoneMatch_ = entityTag
	return c
}

// Context sets the context to be used in this call's Do method.
func (c *CategoriesListCall) Context(ctx context.Context) *CategoriesListCall {
	c.ctx_ = ctx
	return c
}

// Header returns a http.Header that can be modified by the caller to add
// headers to the request.
func (c *CategoriesListCall) Header() http.Header {
	if c.header_ == nil {
		c.header_ = make(http.Header)
	}
	return c.header_
}

func (c *CategoriesListCall) doRequest(alt string) (*http.Response, error) {
	reqHeaders := gensupport.SetHeaders(c.s.userAgent(), "", c.header_)
	if c.ifNoneMatch_ != "" {
		reqHeaders.Set("If-None-Match", c.ifNoneMatch_)
	}
	var body io.Reader = nil
	c.urlParams_.Set("alt", alt)
	c.urlParams_.Set("prettyPrint", "false")
	urls := googleapi.ResolveRelative(c.s.BasePath, "v1/categories")
	urls += "?" + c.urlParams_.Encode()
	req, err := http.NewRequest("GET", urls, body)
	if err != nil {
		return nil, err
	}
	req.Header = reqHeaders
	return gensupport.SendRequest(c.ctx_, c.s.client, req)
}

// Do executes the "mybusinessbusinessinformation.categories.list" call.
// Any non-2xx status code is an error. Response headers are in either
// *ListCategoriesResponse.ServerResponse.Header or (if a response was returned
// at all) in error.(*googleapi.Error).Header. Use googleapi.IsNotModified to
// check whether the returned error was because http.StatusNotModified was
// returned.
func (c *CategoriesListCall) Do(opts ...googleapi.CallOption) (*ListCategoriesResponse, error) {
	gensupport.SetOptions(c.urlParams_, opts...)
	res, err := c.doRequest("json")
	if res != nil && res.StatusCode == http.StatusNotModified {
		if res.Body != nil {
			res.Body.Close()
		}
		return nil, gensupport.WrapError(&googleapi.Error{
			Code:   res.StatusCode,
			Header: res.Header,
		})
	}
	if err != nil {
		return nil, err
	}
	defer googleapi.CloseBody(res)
	if err := googleapi.CheckResponse(res); err != nil {
		return nil, gensupport.WrapError(err)
	}
	ret := &ListCategoriesResponse{
		ServerResponse: googleapi.ServerResponse{
			Header:         res.Header,
			HTTPStatusCode: res.StatusCode,
		},
	}
	target := &ret
	if err := gensupport.DecodeResponse(target, res); err != nil {
		return nil, err
	}
	return ret, nil
}

// Pages invokes f for each page of results.
// A non-nil error returned from f will halt the iteration.
// The provided context supersedes any context provided to the Context method.
func (c *CategoriesListCall) Pages(ctx context.Context, f func(*ListCategoriesResponse) error) error {
	c.ctx_ = ctx
	defer c.PageToken(c.urlParams_.Get("pageToken"))
	for {
		x, err := c.Do()
		if err != nil {
			return err
		}
		if err := f(x); err != nil {
			return err
		}
		if x.NextPageToken == "" {
			return nil
		}
		c.PageToken(x.NextPageToken)
	}
}

type ChainsGetCall struct {
	s            *Service
	name         string
	urlParams_   gensupport.URLParams
	ifNoneMatch_ string
	ctx_         context.Context
	header_      http.Header
}

// Get: Gets the specified chain. Returns `NOT_FOUND` if the chain does not
// exist.
//
// - name: The chain's resource name, in the format `chains/{chain_place_id}`.
func (r *ChainsService) Get(name string) *ChainsGetCall {
	c := &ChainsGetCall{s: r.s, urlParams_: make(gensupport.URLParams)}
	c.name = name
	return c
}

// Fields allows partial responses to be retrieved. See
// https://developers.google.com/gdata/docs/2.0/basics#PartialResponse for more
// details.
func (c *ChainsGetCall) Fields(s ...googleapi.Field) *ChainsGetCall {
	c.urlParams_.Set("fields", googleapi.CombineFields(s))
	return c
}

// IfNoneMatch sets an optional parameter which makes the operation fail if the
// object's ETag matches the given value. This is useful for getting updates
// only after the object has changed since the last request.
func (c *ChainsGetCall) IfNoneMatch(entityTag string) *ChainsGetCall {
	c.ifNoneMatch_ = entityTag
	return c
}

// Context sets the context to be used in this call's Do method.
func (c *ChainsGetCall) Context(ctx context.Context) *ChainsGetCall {
	c.ctx_ = ctx
	return c
}

// Header returns a http.Header that can be modified by the caller to add
// headers to the request.
func (c *ChainsGetCall) Header() http.Header {
	if c.header_ == nil {
		c.header_ = make(http.Header)
	}
	return c.header_
}

func (c *ChainsGetCall) doRequest(alt string) (*http.Response, error) {
	reqHeaders := gensupport.SetHeaders(c.s.userAgent(), "", c.header_)
	if c.ifNoneMatch_ != "" {
		reqHeaders.Set("If-None-Match", c.ifNoneMatch_)
	}
	var body io.Reader = nil
	c.urlParams_.Set("alt", alt)
	c.urlParams_.Set("prettyPrint", "false")
	urls := googleapi.ResolveRelative(c.s.BasePath, "v1/{+name}")
	urls += "?" + c.urlParams_.Encode()
	req, err := http.NewRequest("GET", urls, body)
	if err != nil {
		return nil, err
	}
	req.Header = reqHeaders
	googleapi.Expand(req.URL, map[string]string{
		"name": c.name,
	})
	return gensupport.SendRequest(c.ctx_, c.s.client, req)
}

// Do executes the "mybusinessbusinessinformation.chains.get" call.
// Any non-2xx status code is an error. Response headers are in either
// *Chain.ServerResponse.Header or (if a response was returned at all) in
// error.(*googleapi.Error).Header. Use googleapi.IsNotModified to check
// whether the returned error was because http.StatusNotModified was returned.
func (c *ChainsGetCall) Do(opts ...googleapi.CallOption) (*Chain, error) {
	gensupport.SetOptions(c.urlParams_, opts...)
	res, err := c.doRequest("json")
	if res != nil && res.StatusCode == http.StatusNotModified {
		if res.Body != nil {
			res.Body.Close()
		}
		return nil, gensupport.WrapError(&googleapi.Error{
			Code:   res.StatusCode,
			Header: res.Header,
		})
	}
	if err != nil {
		return nil, err
	}
	defer googleapi.CloseBody(res)
	if err := googleapi.CheckResponse(res); err != nil {
		return nil, gensupport.WrapError(err)
	}
	ret := &Chain{
		ServerResponse: googleapi.ServerResponse{
			Header:         res.Header,
			HTTPStatusCode: res.StatusCode,
		},
	}
	target := &ret
	if err := gensupport.DecodeResponse(target, res); err != nil {
		return nil, err
	}
	return ret, nil
}

type ChainsSearchCall struct {
	s            *Service
	urlParams_   gensupport.URLParams
	ifNoneMatch_ string
	ctx_         context.Context
	header_      http.Header
}

// Search: Searches the chain based on chain name.
func (r *ChainsService) Search() *ChainsSearchCall {
	c := &ChainsSearchCall{s: r.s, urlParams_: make(gensupport.URLParams)}
	return c
}

// ChainName sets the optional parameter "chainName": Required. Search for a
// chain by its name. Exact/partial/fuzzy/related queries are supported.
// Examples: "walmart", "wal-mart", "walmmmart", "沃尔玛"
func (c *ChainsSearchCall) ChainName(chainName string) *ChainsSearchCall {
	c.urlParams_.Set("chainName", chainName)
	return c
}

// PageSize sets the optional parameter "pageSize": The maximum number of
// matched chains to return from this query. The default is 10. The maximum
// possible value is 500.
func (c *ChainsSearchCall) PageSize(pageSize int64) *ChainsSearchCall {
	c.urlParams_.Set("pageSize", fmt.Sprint(pageSize))
	return c
}

// Fields allows partial responses to be retrieved. See
// https://developers.google.com/gdata/docs/2.0/basics#PartialResponse for more
// details.
func (c *ChainsSearchCall) Fields(s ...googleapi.Field) *ChainsSearchCall {
	c.urlParams_.Set("fields", googleapi.CombineFields(s))
	return c
}

// IfNoneMatch sets an optional parameter which makes the operation fail if the
// object's ETag matches the given value. This is useful for getting updates
// only after the object has changed since the last request.
func (c *ChainsSearchCall) IfNoneMatch(entityTag string) *ChainsSearchCall {
	c.ifNoneMatch_ = entityTag
	return c
}

// Context sets the context to be used in this call's Do method.
func (c *ChainsSearchCall) Context(ctx context.Context) *ChainsSearchCall {
	c.ctx_ = ctx
	return c
}

// Header returns a http.Header that can be modified by the caller to add
// headers to the request.
func (c *ChainsSearchCall) Header() http.Header {
	if c.header_ == nil {
		c.header_ = make(http.Header)
	}
	return c.header_
}

func (c *ChainsSearchCall) doRequest(alt string) (*http.Response, error) {
	reqHeaders := gensupport.SetHeaders(c.s.userAgent(), "", c.header_)
	if c.ifNoneMatch_ != "" {
		reqHeaders.Set("If-None-Match", c.ifNoneMatch_)
	}
	var body io.Reader = nil
	c.urlParams_.Set("alt", alt)
	c.urlParams_.Set("prettyPrint", "false")
	urls := googleapi.ResolveRelative(c.s.BasePath, "v1/chains:search")
	urls += "?" + c.urlParams_.Encode()
	req, err := http.NewRequest("GET", urls, body)
	if err != nil {
		return nil, err
	}
	req.Header = reqHeaders
	return gensupport.SendRequest(c.ctx_, c.s.client, req)
}

// Do executes the "mybusinessbusinessinformation.chains.search" call.
// Any non-2xx status code is an error. Response headers are in either
// *SearchChainsResponse.ServerResponse.Header or (if a response was returned
// at all) in error.(*googleapi.Error).Header. Use googleapi.IsNotModified to
// check whether the returned error was because http.StatusNotModified was
// returned.
func (c *ChainsSearchCall) Do(opts ...googleapi.CallOption) (*SearchChainsResponse, error) {
	gensupport.SetOptions(c.urlParams_, opts...)
	res, err := c.doRequest("json")
	if res != nil && res.StatusCode == http.StatusNotModified {
		if res.Body != nil {
			res.Body.Close()
		}
		return nil, gensupport.WrapError(&googleapi.Error{
			Code:   res.StatusCode,
			Header: res.Header,
		})
	}
	if err != nil {
		return nil, err
	}
	defer googleapi.CloseBody(res)
	if err := googleapi.CheckResponse(res); err != nil {
		return nil, gensupport.WrapError(err)
	}
	ret := &SearchChainsResponse{
		ServerResponse: googleapi.ServerResponse{
			Header:         res.Header,
			HTTPStatusCode: res.StatusCode,
		},
	}
	target := &ret
	if err := gensupport.DecodeResponse(target, res); err != nil {
		return nil, err
	}
	return ret, nil
}

type GoogleLocationsSearchCall struct {
	s                            *Service
	searchgooglelocationsrequest *SearchGoogleLocationsRequest
	urlParams_                   gensupport.URLParams
	ctx_                         context.Context
	header_                      http.Header
}

// Search: Search all of the possible locations that are a match to the
// specified request.
func (r *GoogleLocationsService) Search(searchgooglelocationsrequest *SearchGoogleLocationsRequest) *GoogleLocationsSearchCall {
	c := &GoogleLocationsSearchCall{s: r.s, urlParams_: make(gensupport.URLParams)}
	c.searchgooglelocationsrequest = searchgooglelocationsrequest
	return c
}

// Fields allows partial responses to be retrieved. See
// https://developers.google.com/gdata/docs/2.0/basics#PartialResponse for more
// details.
func (c *GoogleLocationsSearchCall) Fields(s ...googleapi.Field) *GoogleLocationsSearchCall {
	c.urlParams_.Set("fields", googleapi.CombineFields(s))
	return c
}

// Context sets the context to be used in this call's Do method.
func (c *GoogleLocationsSearchCall) Context(ctx context.Context) *GoogleLocationsSearchCall {
	c.ctx_ = ctx
	return c
}

// Header returns a http.Header that can be modified by the caller to add
// headers to the request.
func (c *GoogleLocationsSearchCall) Header() http.Header {
	if c.header_ == nil {
		c.header_ = make(http.Header)
	}
	return c.header_
}

func (c *GoogleLocationsSearchCall) doRequest(alt string) (*http.Response, error) {
	reqHeaders := gensupport.SetHeaders(c.s.userAgent(), "application/json", c.header_)
	var body io.Reader = nil
	body, err := googleapi.WithoutDataWrapper.JSONReader(c.searchgooglelocationsrequest)
	if err != nil {
		return nil, err
	}
	c.urlParams_.Set("alt", alt)
	c.urlParams_.Set("prettyPrint", "false")
	urls := googleapi.ResolveRelative(c.s.BasePath, "v1/googleLocations:search")
	urls += "?" + c.urlParams_.Encode()
	req, err := http.NewRequest("POST", urls, body)
	if err != nil {
		return nil, err
	}
	req.Header = reqHeaders
	return gensupport.SendRequest(c.ctx_, c.s.client, req)
}

// Do executes the "mybusinessbusinessinformation.googleLocations.search" call.
// Any non-2xx status code is an error. Response headers are in either
// *SearchGoogleLocationsResponse.ServerResponse.Header or (if a response was
// returned at all) in error.(*googleapi.Error).Header. Use
// googleapi.IsNotModified to check whether the returned error was because
// http.StatusNotModified was returned.
func (c *GoogleLocationsSearchCall) Do(opts ...googleapi.CallOption) (*SearchGoogleLocationsResponse, error) {
	gensupport.SetOptions(c.urlParams_, opts...)
	res, err := c.doRequest("json")
	if res != nil && res.StatusCode == http.StatusNotModified {
		if res.Body != nil {
			res.Body.Close()
		}
		return nil, gensupport.WrapError(&googleapi.Error{
			Code:   res.StatusCode,
			Header: res.Header,
		})
	}
	if err != nil {
		return nil, err
	}
	defer googleapi.CloseBody(res)
	if err := googleapi.CheckResponse(res); err != nil {
		return nil, gensupport.WrapError(err)
	}
	ret := &SearchGoogleLocationsResponse{
		ServerResponse: googleapi.ServerResponse{
			Header:         res.Header,
			HTTPStatusCode: res.StatusCode,
		},
	}
	target := &ret
	if err := gensupport.DecodeResponse(target, res); err != nil {
		return nil, err
	}
	return ret, nil
}

type LocationsDeleteCall struct {
	s          *Service
	name       string
	urlParams_ gensupport.URLParams
	ctx_       context.Context
	header_    http.Header
}

// Delete: Deletes a location. If this location cannot be deleted using the API
// and it is marked so in the
// `google.mybusiness.businessinformation.v1.LocationState`, use the Google
// Business Profile (https://business.google.com/manage/) website.
//
// - name: The name of the location to delete.
func (r *LocationsService) Delete(name string) *LocationsDeleteCall {
	c := &LocationsDeleteCall{s: r.s, urlParams_: make(gensupport.URLParams)}
	c.name = name
	return c
}

// Fields allows partial responses to be retrieved. See
// https://developers.google.com/gdata/docs/2.0/basics#PartialResponse for more
// details.
func (c *LocationsDeleteCall) Fields(s ...googleapi.Field) *LocationsDeleteCall {
	c.urlParams_.Set("fields", googleapi.CombineFields(s))
	return c
}

// Context sets the context to be used in this call's Do method.
func (c *LocationsDeleteCall) Context(ctx context.Context) *LocationsDeleteCall {
	c.ctx_ = ctx
	return c
}

// Header returns a http.Header that can be modified by the caller to add
// headers to the request.
func (c *LocationsDeleteCall) Header() http.Header {
	if c.header_ == nil {
		c.header_ = make(http.Header)
	}
	return c.header_
}

func (c *LocationsDeleteCall) doRequest(alt string) (*http.Response, error) {
	reqHeaders := gensupport.SetHeaders(c.s.userAgent(), "", c.header_)
	var body io.Reader = nil
	c.urlParams_.Set("alt", alt)
	c.urlParams_.Set("prettyPrint", "false")
	urls := googleapi.ResolveRelative(c.s.BasePath, "v1/{+name}")
	urls += "?" + c.urlParams_.Encode()
	req, err := http.NewRequest("DELETE", urls, body)
	if err != nil {
		return nil, err
	}
	req.Header = reqHeaders
	googleapi.Expand(req.URL, map[string]string{
		"name": c.name,
	})
	return gensupport.SendRequest(c.ctx_, c.s.client, req)
}

// Do executes the "mybusinessbusinessinformation.locations.delete" call.
// Any non-2xx status code is an error. Response headers are in either
// *Empty.ServerResponse.Header or (if a response was returned at all) in
// error.(*googleapi.Error).Header. Use googleapi.IsNotModified to check
// whether the returned error was because http.StatusNotModified was returned.
func (c *LocationsDeleteCall) Do(opts ...googleapi.CallOption) (*Empty, error) {
	gensupport.SetOptions(c.urlParams_, opts...)
	res, err := c.doRequest("json")
	if res != nil && res.StatusCode == http.StatusNotModified {
		if res.Body != nil {
			res.Body.Close()
		}
		return nil, gensupport.WrapError(&googleapi.Error{
			Code:   res.StatusCode,
			Header: res.Header,
		})
	}
	if err != nil {
		return nil, err
	}
	defer googleapi.CloseBody(res)
	if err := googleapi.CheckResponse(res); err != nil {
		return nil, gensupport.WrapError(err)
	}
	ret := &Empty{
		ServerResponse: googleapi.ServerResponse{
			Header:         res.Header,
			HTTPStatusCode: res.StatusCode,
		},
	}
	target := &ret
	if err := gensupport.DecodeResponse(target, res); err != nil {
		return nil, err
	}
	return ret, nil
}

type LocationsGetCall struct {
	s            *Service
	name         string
	urlParams_   gensupport.URLParams
	ifNoneMatch_ string
	ctx_         context.Context
	header_      http.Header
}

// Get: Returns the specified location.
//
// - name: The name of the location to fetch.
func (r *LocationsService) Get(name string) *LocationsGetCall {
	c := &LocationsGetCall{s: r.s, urlParams_: make(gensupport.URLParams)}
	c.name = name
	return c
}

// ReadMask sets the optional parameter "readMask": Required. Read mask to
// specify what fields will be returned in the response.
func (c *LocationsGetCall) ReadMask(readMask string) *LocationsGetCall {
	c.urlParams_.Set("readMask", readMask)
	return c
}

// Fields allows partial responses to be retrieved. See
// https://developers.google.com/gdata/docs/2.0/basics#PartialResponse for more
// details.
func (c *LocationsGetCall) Fields(s ...googleapi.Field) *LocationsGetCall {
	c.urlParams_.Set("fields", googleapi.CombineFields(s))
	return c
}

// IfNoneMatch sets an optional parameter which makes the operation fail if the
// object's ETag matches the given value. This is useful for getting updates
// only after the object has changed since the last request.
func (c *LocationsGetCall) IfNoneMatch(entityTag string) *LocationsGetCall {
	c.ifNoneMatch_ = entityTag
	return c
}

// Context sets the context to be used in this call's Do method.
func (c *LocationsGetCall) Context(ctx context.Context) *LocationsGetCall {
	c.ctx_ = ctx
	return c
}

// Header returns a http.Header that can be modified by the caller to add
// headers to the request.
func (c *LocationsGetCall) Header() http.Header {
	if c.header_ == nil {
		c.header_ = make(http.Header)
	}
	return c.header_
}

func (c *LocationsGetCall) doRequest(alt string) (*http.Response, error) {
	reqHeaders := gensupport.SetHeaders(c.s.userAgent(), "", c.header_)
	if c.ifNoneMatch_ != "" {
		reqHeaders.Set("If-None-Match", c.ifNoneMatch_)
	}
	var body io.Reader = nil
	c.urlParams_.Set("alt", alt)
	c.urlParams_.Set("prettyPrint", "false")
	urls := googleapi.ResolveRelative(c.s.BasePath, "v1/{+name}")
	urls += "?" + c.urlParams_.Encode()
	req, err := http.NewRequest("GET", urls, body)
	if err != nil {
		return nil, err
	}
	req.Header = reqHeaders
	googleapi.Expand(req.URL, map[string]string{
		"name": c.name,
	})
	return gensupport.SendRequest(c.ctx_, c.s.client, req)
}

// Do executes the "mybusinessbusinessinformation.locations.get" call.
// Any non-2xx status code is an error. Response headers are in either
// *Location.ServerResponse.Header or (if a response was returned at all) in
// error.(*googleapi.Error).Header. Use googleapi.IsNotModified to check
// whether the returned error was because http.StatusNotModified was returned.
func (c *LocationsGetCall) Do(opts ...googleapi.CallOption) (*Location, error) {
	gensupport.SetOptions(c.urlParams_, opts...)
	res, err := c.doRequest("json")
	if res != nil && res.StatusCode == http.StatusNotModified {
		if res.Body != nil {
			res.Body.Close()
		}
		return nil, gensupport.WrapError(&googleapi.Error{
			Code:   res.StatusCode,
			Header: res.Header,
		})
	}
	if err != nil {
		return nil, err
	}
	defer googleapi.CloseBody(res)
	if err := googleapi.CheckResponse(res); err != nil {
		return nil, gensupport.WrapError(err)
	}
	ret := &Location{
		ServerResponse: googleapi.ServerResponse{
			Header:         res.Header,
			HTTPStatusCode: res.StatusCode,
		},
	}
	target := &ret
	if err := gensupport.DecodeResponse(target, res); err != nil {
		return nil, err
	}
	return ret, nil
}

type LocationsGetAttributesCall struct {
	s            *Service
	nameid       string
	urlParams_   gensupport.URLParams
	ifNoneMatch_ string
	ctx_         context.Context
	header_      http.Header
}

// GetAttributes: Looks up all the attributes set for a given location.
//
//   - name: Google identifier for this location in the form of
//     `locations/{location_id}/attributes`.
func (r *LocationsService) GetAttributes(nameid string) *LocationsGetAttributesCall {
	c := &LocationsGetAttributesCall{s: r.s, urlParams_: make(gensupport.URLParams)}
	c.nameid = nameid
	return c
}

// Fields allows partial responses to be retrieved. See
// https://developers.google.com/gdata/docs/2.0/basics#PartialResponse for more
// details.
func (c *LocationsGetAttributesCall) Fields(s ...googleapi.Field) *LocationsGetAttributesCall {
	c.urlParams_.Set("fields", googleapi.CombineFields(s))
	return c
}

// IfNoneMatch sets an optional parameter which makes the operation fail if the
// object's ETag matches the given value. This is useful for getting updates
// only after the object has changed since the last request.
func (c *LocationsGetAttributesCall) IfNoneMatch(entityTag string) *LocationsGetAttributesCall {
	c.ifNoneMatch_ = entityTag
	return c
}

// Context sets the context to be used in this call's Do method.
func (c *LocationsGetAttributesCall) Context(ctx context.Context) *LocationsGetAttributesCall {
	c.ctx_ = ctx
	return c
}

// Header returns a http.Header that can be modified by the caller to add
// headers to the request.
func (c *LocationsGetAttributesCall) Header() http.Header {
	if c.header_ == nil {
		c.header_ = make(http.Header)
	}
	return c.header_
}

func (c *LocationsGetAttributesCall) doRequest(alt string) (*http.Response, error) {
	reqHeaders := gensupport.SetHeaders(c.s.userAgent(), "", c.header_)
	if c.ifNoneMatch_ != "" {
		reqHeaders.Set("If-None-Match", c.ifNoneMatch_)
	}
	var body io.Reader = nil
	c.urlParams_.Set("alt", alt)
	c.urlParams_.Set("prettyPrint", "false")
	urls := googleapi.ResolveRelative(c.s.BasePath, "v1/{+name}")
	urls += "?" + c.urlParams_.Encode()
	req, err := http.NewRequest("GET", urls, body)
	if err != nil {
		return nil, err
	}
	req.Header = reqHeaders
	googleapi.Expand(req.URL, map[string]string{
		"name": c.nameid,
	})
	return gensupport.SendRequest(c.ctx_, c.s.client, req)
}

// Do executes the "mybusinessbusinessinformation.locations.getAttributes" call.
// Any non-2xx status code is an error. Response headers are in either
// *Attributes.ServerResponse.Header or (if a response was returned at all) in
// error.(*googleapi.Error).Header. Use googleapi.IsNotModified to check
// whether the returned error was because http.StatusNotModified was returned.
func (c *LocationsGetAttributesCall) Do(opts ...googleapi.CallOption) (*Attributes, error) {
	gensupport.SetOptions(c.urlParams_, opts...)
	res, err := c.doRequest("json")
	if res != nil && res.StatusCode == http.StatusNotModified {
		if res.Body != nil {
			res.Body.Close()
		}
		return nil, gensupport.WrapError(&googleapi.Error{
			Code:   res.StatusCode,
			Header: res.Header,
		})
	}
	if err != nil {
		return nil, err
	}
	defer googleapi.CloseBody(res)
	if err := googleapi.CheckResponse(res); err != nil {
		return nil, gensupport.WrapError(err)
	}
	ret := &Attributes{
		ServerResponse: googleapi.ServerResponse{
			Header:         res.Header,
			HTTPStatusCode: res.StatusCode,
		},
	}
	target := &ret
	if err := gensupport.DecodeResponse(target, res); err != nil {
		return nil, err
	}
	return ret, nil
}

type LocationsGetGoogleUpdatedCall struct {
	s            *Service
	name         string
	urlParams_   gensupport.URLParams
	ifNoneMatch_ string
	ctx_         context.Context
	header_      http.Header
}

// GetGoogleUpdated: Gets the Google-updated version of the specified location.
//
// - name: The name of the location to fetch.
func (r *LocationsService) GetGoogleUpdated(name string) *LocationsGetGoogleUpdatedCall {
	c := &LocationsGetGoogleUpdatedCall{s: r.s, urlParams_: make(gensupport.URLParams)}
	c.name = name
	return c
}

// ReadMask sets the optional parameter "readMask": Required. Read mask to
// specify what fields will be returned in the response.
func (c *LocationsGetGoogleUpdatedCall) ReadMask(readMask string) *LocationsGetGoogleUpdatedCall {
	c.urlParams_.Set("readMask", readMask)
	return c
}

// Fields allows partial responses to be retrieved. See
// https://developers.google.com/gdata/docs/2.0/basics#PartialResponse for more
// details.
func (c *LocationsGetGoogleUpdatedCall) Fields(s ...googleapi.Field) *LocationsGetGoogleUpdatedCall {
	c.urlParams_.Set("fields", googleapi.CombineFields(s))
	return c
}

// IfNoneMatch sets an optional parameter which makes the operation fail if the
// object's ETag matches the given value. This is useful for getting updates
// only after the object has changed since the last request.
func (c *LocationsGetGoogleUpdatedCall) IfNoneMatch(entityTag string) *LocationsGetGoogleUpdatedCall {
	c.ifNoneMatch_ = entityTag
	return c
}

// Context sets the context to be used in this call's Do method.
func (c *LocationsGetGoogleUpdatedCall) Context(ctx context.Context) *LocationsGetGoogleUpdatedCall {
	c.ctx_ = ctx
	return c
}

// Header returns a http.Header that can be modified by the caller to add
// headers to the request.
func (c *LocationsGetGoogleUpdatedCall) Header() http.Header {
	if c.header_ == nil {
		c.header_ = make(http.Header)
	}
	return c.header_
}

func (c *LocationsGetGoogleUpdatedCall) doRequest(alt string) (*http.Response, error) {
	reqHeaders := gensupport.SetHeaders(c.s.userAgent(), "", c.header_)
	if c.ifNoneMatch_ != "" {
		reqHeaders.Set("If-None-Match", c.ifNoneMatch_)
	}
	var body io.Reader = nil
	c.urlParams_.Set("alt", alt)
	c.urlParams_.Set("prettyPrint", "false")
	urls := googleapi.ResolveRelative(c.s.BasePath, "v1/{+name}:getGoogleUpdated")
	urls += "?" + c.urlParams_.Encode()
	req, err := http.NewRequest("GET", urls, body)
	if err != nil {
		return nil, err
	}
	req.Header = reqHeaders
	googleapi.Expand(req.URL, map[string]string{
		"name": c.name,
	})
	return gensupport.SendRequest(c.ctx_, c.s.client, req)
}

// Do executes the "mybusinessbusinessinformation.locations.getGoogleUpdated" call.
// Any non-2xx status code is an error. Response headers are in either
// *GoogleUpdatedLocation.ServerResponse.Header or (if a response was returned
// at all) in error.(*googleapi.Error).Header. Use googleapi.IsNotModified to
// check whether the returned error was because http.StatusNotModified was
// returned.
func (c *LocationsGetGoogleUpdatedCall) Do(opts ...googleapi.CallOption) (*GoogleUpdatedLocation, error) {
	gensupport.SetOptions(c.urlParams_, opts...)
	res, err := c.doRequest("json")
	if res != nil && res.StatusCode == http.StatusNotModified {
		if res.Body != nil {
			res.Body.Close()
		}
		return nil, gensupport.WrapError(&googleapi.Error{
			Code:   res.StatusCode,
			Header: res.Header,
		})
	}
	if err != nil {
		return nil, err
	}
	defer googleapi.CloseBody(res)
	if err := googleapi.CheckResponse(res); err != nil {
		return nil, gensupport.WrapError(err)
	}
	ret := &GoogleUpdatedLocation{
		ServerResponse: googleapi.ServerResponse{
			Header:         res.Header,
			HTTPStatusCode: res.StatusCode,
		},
	}
	target := &ret
	if err := gensupport.DecodeResponse(target, res); err != nil {
		return nil, err
	}
	return ret, nil
}

type LocationsPatchCall struct {
	s          *Service
	nameid     string
	location   *Location
	urlParams_ gensupport.URLParams
	ctx_       context.Context
	header_    http.Header
}

// Patch: Updates the specified location.
//
//   - name: Google identifier for this location in the form:
//     `locations/{location_id}`.
func (r *LocationsService) Patch(nameid string, location *Location) *LocationsPatchCall {
	c := &LocationsPatchCall{s: r.s, urlParams_: make(gensupport.URLParams)}
	c.nameid = nameid
	c.location = location
	return c
}

// UpdateMask sets the optional parameter "updateMask": Required. The specific
// fields to update.
func (c *LocationsPatchCall) UpdateMask(updateMask string) *LocationsPatchCall {
	c.urlParams_.Set("updateMask", updateMask)
	return c
}

// ValidateOnly sets the optional parameter "validateOnly": If true, the
// request is validated without actually updating the location. When this field
// is set, we will only return validation errors if there were any. The
// response will be empty if no errors were found.
func (c *LocationsPatchCall) ValidateOnly(validateOnly bool) *LocationsPatchCall {
	c.urlParams_.Set("validateOnly", fmt.Sprint(validateOnly))
	return c
}

// Fields allows partial responses to be retrieved. See
// https://developers.google.com/gdata/docs/2.0/basics#PartialResponse for more
// details.
func (c *LocationsPatchCall) Fields(s ...googleapi.Field) *LocationsPatchCall {
	c.urlParams_.Set("fields", googleapi.CombineFields(s))
	return c
}

// Context sets the context to be used in this call's Do method.
func (c *LocationsPatchCall) Context(ctx context.Context) *LocationsPatchCall {
	c.ctx_ = ctx
	return c
}

// Header returns a http.Header that can be modified by the caller to add
// headers to the request.
func (c *LocationsPatchCall) Header() http.Header {
	if c.header_ == nil {
		c.header_ = make(http.Header)
	}
	return c.header_
}

func (c *LocationsPatchCall) doRequest(alt string) (*http.Response, error) {
	reqHeaders := gensupport.SetHeaders(c.s.userAgent(), "application/json", c.header_)
	var body io.Reader = nil
	body, err := googleapi.WithoutDataWrapper.JSONReader(c.location)
	if err != nil {
		return nil, err
	}
	c.urlParams_.Set("alt", alt)
	c.urlParams_.Set("prettyPrint", "false")
	urls := googleapi.ResolveRelative(c.s.BasePath, "v1/{+name}")
	urls += "?" + c.urlParams_.Encode()
	req, err := http.NewRequest("PATCH", urls, body)
	if err != nil {
		return nil, err
	}
	req.Header = reqHeaders
	googleapi.Expand(req.URL, map[string]string{
		"name": c.nameid,
	})
	return gensupport.SendRequest(c.ctx_, c.s.client, req)
}

// Do executes the "mybusinessbusinessinformation.locations.patch" call.
// Any non-2xx status code is an error. Response headers are in either
// *Location.ServerResponse.Header or (if a response was returned at all) in
// error.(*googleapi.Error).Header. Use googleapi.IsNotModified to check
// whether the returned error was because http.StatusNotModified was returned.
func (c *LocationsPatchCall) Do(opts ...googleapi.CallOption) (*Location, error) {
	gensupport.SetOptions(c.urlParams_, opts...)
	res, err := c.doRequest("json")
	if res != nil && res.StatusCode == http.StatusNotModified {
		if res.Body != nil {
			res.Body.Close()
		}
		return nil, gensupport.WrapError(&googleapi.Error{
			Code:   res.StatusCode,
			Header: res.Header,
		})
	}
	if err != nil {
		return nil, err
	}
	defer googleapi.CloseBody(res)
	if err := googleapi.CheckResponse(res); err != nil {
		return nil, gensupport.WrapError(err)
	}
	ret := &Location{
		ServerResponse: googleapi.ServerResponse{
			Header:         res.Header,
			HTTPStatusCode: res.StatusCode,
		},
	}
	target := &ret
	if err := gensupport.DecodeResponse(target, res); err != nil {
		return nil, err
	}
	return ret, nil
}

type LocationsUpdateAttributesCall struct {
	s          *Service
	nameid     string
	attributes *Attributes
	urlParams_ gensupport.URLParams
	ctx_       context.Context
	header_    http.Header
}

// UpdateAttributes: Update attributes for a given location.
//
//   - name: Google identifier for this location in the form of
//     `locations/{location_id}/attributes`.
func (r *LocationsService) UpdateAttributes(nameid string, attributes *Attributes) *LocationsUpdateAttributesCall {
	c := &LocationsUpdateAttributesCall{s: r.s, urlParams_: make(gensupport.URLParams)}
	c.nameid = nameid
	c.attributes = attributes
	return c
}

// AttributeMask sets the optional parameter "attributeMask": Required.
// Attribute name of attributes that you'd like to update. Represented by
// `attributes/{attribute}`. Updates: All attributes provided in the attributes
// field that you would like to update must be set in the `attribute_mask`.
// Attributes set in the above list but not in the `attribute_mask` will be
// ignored. Deletes: If you'd like to delete certain attributes, they must be
// specified in the `attribute_mask` with no matching entry in the attributes
// list. If you'd like to delete all attributes set on a location, you should
// look up all the applicable attributes for the location and then add them to
// the `attribute_mask` with an empty attributes field.
func (c *LocationsUpdateAttributesCall) AttributeMask(attributeMask string) *LocationsUpdateAttributesCall {
	c.urlParams_.Set("attributeMask", attributeMask)
	return c
}

// Fields allows partial responses to be retrieved. See
// https://developers.google.com/gdata/docs/2.0/basics#PartialResponse for more
// details.
func (c *LocationsUpdateAttributesCall) Fields(s ...googleapi.Field) *LocationsUpdateAttributesCall {
	c.urlParams_.Set("fields", googleapi.CombineFields(s))
	return c
}

// Context sets the context to be used in this call's Do method.
func (c *LocationsUpdateAttributesCall) Context(ctx context.Context) *LocationsUpdateAttributesCall {
	c.ctx_ = ctx
	return c
}

// Header returns a http.Header that can be modified by the caller to add
// headers to the request.
func (c *LocationsUpdateAttributesCall) Header() http.Header {
	if c.header_ == nil {
		c.header_ = make(http.Header)
	}
	return c.header_
}

func (c *LocationsUpdateAttributesCall) doRequest(alt string) (*http.Response, error) {
	reqHeaders := gensupport.SetHeaders(c.s.userAgent(), "application/json", c.header_)
	var body io.Reader = nil
	body, err := googleapi.WithoutDataWrapper.JSONReader(c.attributes)
	if err != nil {
		return nil, err
	}
	c.urlParams_.Set("alt", alt)
	c.urlParams_.Set("prettyPrint", "false")
	urls := googleapi.ResolveRelative(c.s.BasePath, "v1/{+name}")
	urls += "?" + c.urlParams_.Encode()
	req, err := http.NewRequest("PATCH", urls, body)
	if err != nil {
		return nil, err
	}
	req.Header = reqHeaders
	googleapi.Expand(req.URL, map[string]string{
		"name": c.nameid,
	})
	return gensupport.SendRequest(c.ctx_, c.s.client, req)
}

// Do executes the "mybusinessbusinessinformation.locations.updateAttributes" call.
// Any non-2xx status code is an error. Response headers are in either
// *Attributes.ServerResponse.Header or (if a response was returned at all) in
// error.(*googleapi.Error).Header. Use googleapi.IsNotModified to check
// whether the returned error was because http.StatusNotModified was returned.
func (c *LocationsUpdateAttributesCall) Do(opts ...googleapi.CallOption) (*Attributes, error) {
	gensupport.SetOptions(c.urlParams_, opts...)
	res, err := c.doRequest("json")
	if res != nil && res.StatusCode == http.StatusNotModified {
		if res.Body != nil {
			res.Body.Close()
		}
		return nil, gensupport.WrapError(&googleapi.Error{
			Code:   res.StatusCode,
			Header: res.Header,
		})
	}
	if err != nil {
		return nil, err
	}
	defer googleapi.CloseBody(res)
	if err := googleapi.CheckResponse(res); err != nil {
		return nil, gensupport.WrapError(err)
	}
	ret := &Attributes{
		ServerResponse: googleapi.ServerResponse{
			Header:         res.Header,
			HTTPStatusCode: res.StatusCode,
		},
	}
	target := &ret
	if err := gensupport.DecodeResponse(target, res); err != nil {
		return nil, err
	}
	return ret, nil
}

type LocationsAttributesGetGoogleUpdatedCall struct {
	s            *Service
	nameid       string
	urlParams_   gensupport.URLParams
	ifNoneMatch_ string
	ctx_         context.Context
	header_      http.Header
}

// GetGoogleUpdated: Gets the Google-updated version of the specified location.
//
//   - name: Google identifier for this location in the form of
//     `locations/{location_id}/attributes`.
func (r *LocationsAttributesService) GetGoogleUpdated(nameid string) *LocationsAttributesGetGoogleUpdatedCall {
	c := &LocationsAttributesGetGoogleUpdatedCall{s: r.s, urlParams_: make(gensupport.URLParams)}
	c.nameid = nameid
	return c
}

// Fields allows partial responses to be retrieved. See
// https://developers.google.com/gdata/docs/2.0/basics#PartialResponse for more
// details.
func (c *LocationsAttributesGetGoogleUpdatedCall) Fields(s ...googleapi.Field) *LocationsAttributesGetGoogleUpdatedCall {
	c.urlParams_.Set("fields", googleapi.CombineFields(s))
	return c
}

// IfNoneMatch sets an optional parameter which makes the operation fail if the
// object's ETag matches the given value. This is useful for getting updates
// only after the object has changed since the last request.
func (c *LocationsAttributesGetGoogleUpdatedCall) IfNoneMatch(entityTag string) *LocationsAttributesGetGoogleUpdatedCall {
	c.ifNoneMatch_ = entityTag
	return c
}

// Context sets the context to be used in this call's Do method.
func (c *LocationsAttributesGetGoogleUpdatedCall) Context(ctx context.Context) *LocationsAttributesGetGoogleUpdatedCall {
	c.ctx_ = ctx
	return c
}

// Header returns a http.Header that can be modified by the caller to add
// headers to the request.
func (c *LocationsAttributesGetGoogleUpdatedCall) Header() http.Header {
	if c.header_ == nil {
		c.header_ = make(http.Header)
	}
	return c.header_
}

func (c *LocationsAttributesGetGoogleUpdatedCall) doRequest(alt string) (*http.Response, error) {
	reqHeaders := gensupport.SetHeaders(c.s.userAgent(), "", c.header_)
	if c.ifNoneMatch_ != "" {
		reqHeaders.Set("If-None-Match", c.ifNoneMatch_)
	}
	var body io.Reader = nil
	c.urlParams_.Set("alt", alt)
	c.urlParams_.Set("prettyPrint", "false")
	urls := googleapi.ResolveRelative(c.s.BasePath, "v1/{+name}:getGoogleUpdated")
	urls += "?" + c.urlParams_.Encode()
	req, err := http.NewRequest("GET", urls, body)
	if err != nil {
		return nil, err
	}
	req.Header = reqHeaders
	googleapi.Expand(req.URL, map[string]string{
		"name": c.nameid,
	})
	return gensupport.SendRequest(c.ctx_, c.s.client, req)
}

// Do executes the "mybusinessbusinessinformation.locations.attributes.getGoogleUpdated" call.
// Any non-2xx status code is an error. Response headers are in either
// *Attributes.ServerResponse.Header or (if a response was returned at all) in
// error.(*googleapi.Error).Header. Use googleapi.IsNotModified to check
// whether the returned error was because http.StatusNotModified was returned.
func (c *LocationsAttributesGetGoogleUpdatedCall) Do(opts ...googleapi.CallOption) (*Attributes, error) {
	gensupport.SetOptions(c.urlParams_, opts...)
	res, err := c.doRequest("json")
	if res != nil && res.StatusCode == http.StatusNotModified {
		if res.Body != nil {
			res.Body.Close()
		}
		return nil, gensupport.WrapError(&googleapi.Error{
			Code:   res.StatusCode,
			Header: res.Header,
		})
	}
	if err != nil {
		return nil, err
	}
	defer googleapi.CloseBody(res)
	if err := googleapi.CheckResponse(res); err != nil {
		return nil, gensupport.WrapError(err)
	}
	ret := &Attributes{
		ServerResponse: googleapi.ServerResponse{
			Header:         res.Header,
			HTTPStatusCode: res.StatusCode,
		},
	}
	target := &ret
	if err := gensupport.DecodeResponse(target, res); err != nil {
		return nil, err
	}
	return ret, nil
}
