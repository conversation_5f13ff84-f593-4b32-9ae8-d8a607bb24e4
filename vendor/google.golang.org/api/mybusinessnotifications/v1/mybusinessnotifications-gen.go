// Copyright 2024 Google LLC.
// Use of this source code is governed by a BSD-style
// license that can be found in the LICENSE file.

// Code generated file. DO NOT EDIT.

// Package mybusinessnotifications provides access to the My Business Notifications API.
//
// For product documentation, see: https://developers.google.com/my-business/
//
// # Library status
//
// These client libraries are officially supported by Google. However, this
// library is considered complete and is in maintenance mode. This means
// that we will address critical bugs and security issues but will not add
// any new features.
//
// When possible, we recommend using our newer
// [Cloud Client Libraries for Go](https://pkg.go.dev/cloud.google.com/go)
// that are still actively being worked and iterated on.
//
// # Creating a client
//
// Usage example:
//
//	import "google.golang.org/api/mybusinessnotifications/v1"
//	...
//	ctx := context.Background()
//	mybusinessnotificationsService, err := mybusinessnotifications.NewService(ctx)
//
// In this example, Google Application Default Credentials are used for
// authentication. For information on how to create and obtain Application
// Default Credentials, see https://developers.google.com/identity/protocols/application-default-credentials.
//
// # Other authentication options
//
// To use an API key for authentication (note: some APIs do not support API
// keys), use [google.golang.org/api/option.WithAPIKey]:
//
//	mybusinessnotificationsService, err := mybusinessnotifications.NewService(ctx, option.WithAPIKey("AIza..."))
//
// To use an OAuth token (e.g., a user token obtained via a three-legged OAuth
// flow, use [google.golang.org/api/option.WithTokenSource]:
//
//	config := &oauth2.Config{...}
//	// ...
//	token, err := config.Exchange(ctx, ...)
//	mybusinessnotificationsService, err := mybusinessnotifications.NewService(ctx, option.WithTokenSource(config.TokenSource(ctx, token)))
//
// See [google.golang.org/api/option.ClientOption] for details on options.
package mybusinessnotifications // import "google.golang.org/api/mybusinessnotifications/v1"

import (
	"bytes"
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"strconv"
	"strings"

	googleapi "google.golang.org/api/googleapi"
	internal "google.golang.org/api/internal"
	gensupport "google.golang.org/api/internal/gensupport"
	option "google.golang.org/api/option"
	internaloption "google.golang.org/api/option/internaloption"
	htransport "google.golang.org/api/transport/http"
)

// Always reference these packages, just in case the auto-generated code
// below doesn't.
var _ = bytes.NewBuffer
var _ = strconv.Itoa
var _ = fmt.Sprintf
var _ = json.NewDecoder
var _ = io.Copy
var _ = url.Parse
var _ = gensupport.MarshalJSON
var _ = googleapi.Version
var _ = errors.New
var _ = strings.Replace
var _ = context.Canceled
var _ = internaloption.WithDefaultEndpoint
var _ = internal.Version

const apiId = "mybusinessnotifications:v1"
const apiName = "mybusinessnotifications"
const apiVersion = "v1"
const basePath = "https://mybusinessnotifications.googleapis.com/"
const basePathTemplate = "https://mybusinessnotifications.UNIVERSE_DOMAIN/"
const mtlsBasePath = "https://mybusinessnotifications.mtls.googleapis.com/"

// NewService creates a new Service.
func NewService(ctx context.Context, opts ...option.ClientOption) (*Service, error) {
	opts = append(opts, internaloption.WithDefaultEndpoint(basePath))
	opts = append(opts, internaloption.WithDefaultEndpointTemplate(basePathTemplate))
	opts = append(opts, internaloption.WithDefaultMTLSEndpoint(mtlsBasePath))
	opts = append(opts, internaloption.EnableNewAuthLibrary())
	client, endpoint, err := htransport.NewClient(ctx, opts...)
	if err != nil {
		return nil, err
	}
	s, err := New(client)
	if err != nil {
		return nil, err
	}
	if endpoint != "" {
		s.BasePath = endpoint
	}
	return s, nil
}

// New creates a new Service. It uses the provided http.Client for requests.
//
// Deprecated: please use NewService instead.
// To provide a custom HTTP client, use option.WithHTTPClient.
// If you are using google.golang.org/api/googleapis/transport.APIKey, use option.WithAPIKey with NewService instead.
func New(client *http.Client) (*Service, error) {
	if client == nil {
		return nil, errors.New("client is nil")
	}
	s := &Service{client: client, BasePath: basePath}
	s.Accounts = NewAccountsService(s)
	return s, nil
}

type Service struct {
	client    *http.Client
	BasePath  string // API endpoint base URL
	UserAgent string // optional additional User-Agent fragment

	Accounts *AccountsService
}

func (s *Service) userAgent() string {
	if s.UserAgent == "" {
		return googleapi.UserAgent
	}
	return googleapi.UserAgent + " " + s.UserAgent
}

func NewAccountsService(s *Service) *AccountsService {
	rs := &AccountsService{s: s}
	return rs
}

type AccountsService struct {
	s *Service
}

// NotificationSetting: A Google Pub/Sub topic where notifications can be
// published when a location is updated or has a new review. There will be only
// one notification setting resource per-account.
type NotificationSetting struct {
	// Name: Required. The resource name this setting is for. This is of the form
	// `accounts/{account_id}/notificationSetting`.
	Name string `json:"name,omitempty"`
	// NotificationTypes: The types of notifications that will be sent to the
	// Pub/Sub topic. To stop receiving notifications entirely, use
	// NotificationSettings.UpdateNotificationSetting with an empty
	// notification_types or set the pubsub_topic to an empty string.
	//
	// Possible values:
	//   "NOTIFICATION_TYPE_UNSPECIFIED" - No notification type. Will not match any
	// notifications.
	//   "GOOGLE_UPDATE" - The location has Google updates for review. The
	// location_name field on the notification will provide the resource name of
	// the location with Google updates.
	//   "NEW_REVIEW" - A new review has been added to the location. The
	// review_name field on the notification will provide the resource name of the
	// review that was added, and location_name will have the location's resource
	// name.
	//   "UPDATED_REVIEW" - A review on the location has been updated. The
	// review_name field on the notification will provide the resource name of the
	// review that was added, and location_name will have the location's resource
	// name.
	//   "NEW_CUSTOMER_MEDIA" - A new media item has been added to the location by
	// a Google Maps user. The notification will provide the resource name of the
	// new media item.
	//   "NEW_QUESTION" - A new question is added to the location. The notification
	// will provide the resource name of question.
	//   "UPDATED_QUESTION" - A question of the location is updated. The
	// notification will provide the resource name of question.
	//   "NEW_ANSWER" - A new answer is added to the location. The notification
	// will provide the resource name of question and answer.
	//   "UPDATED_ANSWER" - An answer of the location is updated. The notification
	// will provide the resource name of question and answer.
	//   "DUPLICATE_LOCATION" - Indicates whether there is a change in location
	// metadata's duplicate location field.
	//   "LOSS_OF_VOICE_OF_MERCHANT" - Deprecated: Migrate the existing usages of
	// this value to the more expanded "VOICE_OF_MERCHANT_UPDATED".
	//   "VOICE_OF_MERCHANT_UPDATED" - Indicates whether the location has an update
	// in Voice of Merchant (VOM) status. VOM dictates whether the location is in
	// good standing and the merchant has control over the business on Google. Any
	// edits made to the location will propagate to Maps after passing the review
	// phase. Call GetVoiceOfMerchantState rpc for more details.
	NotificationTypes []string `json:"notificationTypes,omitempty"`
	// PubsubTopic: Optional. The Google Pub/Sub topic that will receive
	// notifications when locations managed by this account are updated. If unset,
	// no notifications will be posted. The account
	// <EMAIL> must have at least Publish
	// permissions on the Pub/Sub topic.
	PubsubTopic string `json:"pubsubTopic,omitempty"`

	// ServerResponse contains the HTTP response code and headers from the server.
	googleapi.ServerResponse `json:"-"`
	// ForceSendFields is a list of field names (e.g. "Name") to unconditionally
	// include in API requests. By default, fields with empty or default values are
	// omitted from API requests. See
	// https://pkg.go.dev/google.golang.org/api#hdr-ForceSendFields for more
	// details.
	ForceSendFields []string `json:"-"`
	// NullFields is a list of field names (e.g. "Name") to include in API requests
	// with the JSON null value. By default, fields with empty values are omitted
	// from API requests. See
	// https://pkg.go.dev/google.golang.org/api#hdr-NullFields for more details.
	NullFields []string `json:"-"`
}

func (s NotificationSetting) MarshalJSON() ([]byte, error) {
	type NoMethod NotificationSetting
	return gensupport.MarshalJSON(NoMethod(s), s.ForceSendFields, s.NullFields)
}

type AccountsGetNotificationSettingCall struct {
	s            *Service
	name         string
	urlParams_   gensupport.URLParams
	ifNoneMatch_ string
	ctx_         context.Context
	header_      http.Header
}

// GetNotificationSetting: Returns the pubsub notification settings for the
// account.
//
//   - name: The resource name of the notification setting we are trying to
//     fetch.
func (r *AccountsService) GetNotificationSetting(name string) *AccountsGetNotificationSettingCall {
	c := &AccountsGetNotificationSettingCall{s: r.s, urlParams_: make(gensupport.URLParams)}
	c.name = name
	return c
}

// Fields allows partial responses to be retrieved. See
// https://developers.google.com/gdata/docs/2.0/basics#PartialResponse for more
// details.
func (c *AccountsGetNotificationSettingCall) Fields(s ...googleapi.Field) *AccountsGetNotificationSettingCall {
	c.urlParams_.Set("fields", googleapi.CombineFields(s))
	return c
}

// IfNoneMatch sets an optional parameter which makes the operation fail if the
// object's ETag matches the given value. This is useful for getting updates
// only after the object has changed since the last request.
func (c *AccountsGetNotificationSettingCall) IfNoneMatch(entityTag string) *AccountsGetNotificationSettingCall {
	c.ifNoneMatch_ = entityTag
	return c
}

// Context sets the context to be used in this call's Do method.
func (c *AccountsGetNotificationSettingCall) Context(ctx context.Context) *AccountsGetNotificationSettingCall {
	c.ctx_ = ctx
	return c
}

// Header returns a http.Header that can be modified by the caller to add
// headers to the request.
func (c *AccountsGetNotificationSettingCall) Header() http.Header {
	if c.header_ == nil {
		c.header_ = make(http.Header)
	}
	return c.header_
}

func (c *AccountsGetNotificationSettingCall) doRequest(alt string) (*http.Response, error) {
	reqHeaders := gensupport.SetHeaders(c.s.userAgent(), "", c.header_)
	if c.ifNoneMatch_ != "" {
		reqHeaders.Set("If-None-Match", c.ifNoneMatch_)
	}
	var body io.Reader = nil
	c.urlParams_.Set("alt", alt)
	c.urlParams_.Set("prettyPrint", "false")
	urls := googleapi.ResolveRelative(c.s.BasePath, "v1/{+name}")
	urls += "?" + c.urlParams_.Encode()
	req, err := http.NewRequest("GET", urls, body)
	if err != nil {
		return nil, err
	}
	req.Header = reqHeaders
	googleapi.Expand(req.URL, map[string]string{
		"name": c.name,
	})
	return gensupport.SendRequest(c.ctx_, c.s.client, req)
}

// Do executes the "mybusinessnotifications.accounts.getNotificationSetting" call.
// Any non-2xx status code is an error. Response headers are in either
// *NotificationSetting.ServerResponse.Header or (if a response was returned at
// all) in error.(*googleapi.Error).Header. Use googleapi.IsNotModified to
// check whether the returned error was because http.StatusNotModified was
// returned.
func (c *AccountsGetNotificationSettingCall) Do(opts ...googleapi.CallOption) (*NotificationSetting, error) {
	gensupport.SetOptions(c.urlParams_, opts...)
	res, err := c.doRequest("json")
	if res != nil && res.StatusCode == http.StatusNotModified {
		if res.Body != nil {
			res.Body.Close()
		}
		return nil, gensupport.WrapError(&googleapi.Error{
			Code:   res.StatusCode,
			Header: res.Header,
		})
	}
	if err != nil {
		return nil, err
	}
	defer googleapi.CloseBody(res)
	if err := googleapi.CheckResponse(res); err != nil {
		return nil, gensupport.WrapError(err)
	}
	ret := &NotificationSetting{
		ServerResponse: googleapi.ServerResponse{
			Header:         res.Header,
			HTTPStatusCode: res.StatusCode,
		},
	}
	target := &ret
	if err := gensupport.DecodeResponse(target, res); err != nil {
		return nil, err
	}
	return ret, nil
}

type AccountsUpdateNotificationSettingCall struct {
	s                   *Service
	name                string
	notificationsetting *NotificationSetting
	urlParams_          gensupport.URLParams
	ctx_                context.Context
	header_             http.Header
}

// UpdateNotificationSetting: Sets the pubsub notification setting for the
// account informing Google which topic to send pubsub notifications for. Use
// the notification_types field within notification_setting to manipulate the
// events an account wants to subscribe to. An account will only have one
// notification setting resource, and only one pubsub topic can be set. To
// delete the setting, update with an empty notification_types
//
//   - name: The resource name this setting is for. This is of the form
//     `accounts/{account_id}/notificationSetting`.
func (r *AccountsService) UpdateNotificationSetting(name string, notificationsetting *NotificationSetting) *AccountsUpdateNotificationSettingCall {
	c := &AccountsUpdateNotificationSettingCall{s: r.s, urlParams_: make(gensupport.URLParams)}
	c.name = name
	c.notificationsetting = notificationsetting
	return c
}

// UpdateMask sets the optional parameter "updateMask": Required. The specific
// fields that should be updated. The only editable field is
// notification_setting.
func (c *AccountsUpdateNotificationSettingCall) UpdateMask(updateMask string) *AccountsUpdateNotificationSettingCall {
	c.urlParams_.Set("updateMask", updateMask)
	return c
}

// Fields allows partial responses to be retrieved. See
// https://developers.google.com/gdata/docs/2.0/basics#PartialResponse for more
// details.
func (c *AccountsUpdateNotificationSettingCall) Fields(s ...googleapi.Field) *AccountsUpdateNotificationSettingCall {
	c.urlParams_.Set("fields", googleapi.CombineFields(s))
	return c
}

// Context sets the context to be used in this call's Do method.
func (c *AccountsUpdateNotificationSettingCall) Context(ctx context.Context) *AccountsUpdateNotificationSettingCall {
	c.ctx_ = ctx
	return c
}

// Header returns a http.Header that can be modified by the caller to add
// headers to the request.
func (c *AccountsUpdateNotificationSettingCall) Header() http.Header {
	if c.header_ == nil {
		c.header_ = make(http.Header)
	}
	return c.header_
}

func (c *AccountsUpdateNotificationSettingCall) doRequest(alt string) (*http.Response, error) {
	reqHeaders := gensupport.SetHeaders(c.s.userAgent(), "application/json", c.header_)
	var body io.Reader = nil
	body, err := googleapi.WithoutDataWrapper.JSONReader(c.notificationsetting)
	if err != nil {
		return nil, err
	}
	c.urlParams_.Set("alt", alt)
	c.urlParams_.Set("prettyPrint", "false")
	urls := googleapi.ResolveRelative(c.s.BasePath, "v1/{+name}")
	urls += "?" + c.urlParams_.Encode()
	req, err := http.NewRequest("PATCH", urls, body)
	if err != nil {
		return nil, err
	}
	req.Header = reqHeaders
	googleapi.Expand(req.URL, map[string]string{
		"name": c.name,
	})
	return gensupport.SendRequest(c.ctx_, c.s.client, req)
}

// Do executes the "mybusinessnotifications.accounts.updateNotificationSetting" call.
// Any non-2xx status code is an error. Response headers are in either
// *NotificationSetting.ServerResponse.Header or (if a response was returned at
// all) in error.(*googleapi.Error).Header. Use googleapi.IsNotModified to
// check whether the returned error was because http.StatusNotModified was
// returned.
func (c *AccountsUpdateNotificationSettingCall) Do(opts ...googleapi.CallOption) (*NotificationSetting, error) {
	gensupport.SetOptions(c.urlParams_, opts...)
	res, err := c.doRequest("json")
	if res != nil && res.StatusCode == http.StatusNotModified {
		if res.Body != nil {
			res.Body.Close()
		}
		return nil, gensupport.WrapError(&googleapi.Error{
			Code:   res.StatusCode,
			Header: res.Header,
		})
	}
	if err != nil {
		return nil, err
	}
	defer googleapi.CloseBody(res)
	if err := googleapi.CheckResponse(res); err != nil {
		return nil, gensupport.WrapError(err)
	}
	ret := &NotificationSetting{
		ServerResponse: googleapi.ServerResponse{
			Header:         res.Header,
			HTTPStatusCode: res.StatusCode,
		},
	}
	target := &ret
	if err := gensupport.DecodeResponse(target, res); err != nil {
		return nil, err
	}
	return ret, nil
}
