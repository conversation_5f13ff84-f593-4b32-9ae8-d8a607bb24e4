// Copyright 2024 Google LLC.
// Use of this source code is governed by a BSD-style
// license that can be found in the LICENSE file.

// Code generated file. DO NOT EDIT.

// Package cloudtasks provides access to the Cloud Tasks API.
//
// This package is DEPRECATED. Use package cloud.google.com/go/cloudtasks/apiv2beta2 instead.
//
// For product documentation, see: https://cloud.google.com/tasks/
//
// # Library status
//
// These client libraries are officially supported by Google. However, this
// library is considered complete and is in maintenance mode. This means
// that we will address critical bugs and security issues but will not add
// any new features.
//
// When possible, we recommend using our newer
// [Cloud Client Libraries for Go](https://pkg.go.dev/cloud.google.com/go)
// that are still actively being worked and iterated on.
//
// # Creating a client
//
// Usage example:
//
//	import "google.golang.org/api/cloudtasks/v2beta2"
//	...
//	ctx := context.Background()
//	cloudtasksService, err := cloudtasks.NewService(ctx)
//
// In this example, Google Application Default Credentials are used for
// authentication. For information on how to create and obtain Application
// Default Credentials, see https://developers.google.com/identity/protocols/application-default-credentials.
//
// # Other authentication options
//
// To use an API key for authentication (note: some APIs do not support API
// keys), use [google.golang.org/api/option.WithAPIKey]:
//
//	cloudtasksService, err := cloudtasks.NewService(ctx, option.WithAPIKey("AIza..."))
//
// To use an OAuth token (e.g., a user token obtained via a three-legged OAuth
// flow, use [google.golang.org/api/option.WithTokenSource]:
//
//	config := &oauth2.Config{...}
//	// ...
//	token, err := config.Exchange(ctx, ...)
//	cloudtasksService, err := cloudtasks.NewService(ctx, option.WithTokenSource(config.TokenSource(ctx, token)))
//
// See [google.golang.org/api/option.ClientOption] for details on options.
package cloudtasks // import "google.golang.org/api/cloudtasks/v2beta2"

import (
	"bytes"
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"strconv"
	"strings"

	googleapi "google.golang.org/api/googleapi"
	internal "google.golang.org/api/internal"
	gensupport "google.golang.org/api/internal/gensupport"
	option "google.golang.org/api/option"
	internaloption "google.golang.org/api/option/internaloption"
	htransport "google.golang.org/api/transport/http"
)

// Always reference these packages, just in case the auto-generated code
// below doesn't.
var _ = bytes.NewBuffer
var _ = strconv.Itoa
var _ = fmt.Sprintf
var _ = json.NewDecoder
var _ = io.Copy
var _ = url.Parse
var _ = gensupport.MarshalJSON
var _ = googleapi.Version
var _ = errors.New
var _ = strings.Replace
var _ = context.Canceled
var _ = internaloption.WithDefaultEndpoint
var _ = internal.Version

const apiId = "cloudtasks:v2beta2"
const apiName = "cloudtasks"
const apiVersion = "v2beta2"
const basePath = "https://cloudtasks.googleapis.com/"
const basePathTemplate = "https://cloudtasks.UNIVERSE_DOMAIN/"
const mtlsBasePath = "https://cloudtasks.mtls.googleapis.com/"

// OAuth2 scopes used by this API.
const (
	// See, edit, configure, and delete your Google Cloud data and see the email
	// address for your Google Account.
	CloudPlatformScope = "https://www.googleapis.com/auth/cloud-platform"
)

// NewService creates a new Service.
func NewService(ctx context.Context, opts ...option.ClientOption) (*Service, error) {
	scopesOption := internaloption.WithDefaultScopes(
		"https://www.googleapis.com/auth/cloud-platform",
	)
	// NOTE: prepend, so we don't override user-specified scopes.
	opts = append([]option.ClientOption{scopesOption}, opts...)
	opts = append(opts, internaloption.WithDefaultEndpoint(basePath))
	opts = append(opts, internaloption.WithDefaultEndpointTemplate(basePathTemplate))
	opts = append(opts, internaloption.WithDefaultMTLSEndpoint(mtlsBasePath))
	opts = append(opts, internaloption.EnableNewAuthLibrary())
	client, endpoint, err := htransport.NewClient(ctx, opts...)
	if err != nil {
		return nil, err
	}
	s, err := New(client)
	if err != nil {
		return nil, err
	}
	if endpoint != "" {
		s.BasePath = endpoint
	}
	return s, nil
}

// New creates a new Service. It uses the provided http.Client for requests.
//
// Deprecated: please use NewService instead.
// To provide a custom HTTP client, use option.WithHTTPClient.
// If you are using google.golang.org/api/googleapis/transport.APIKey, use option.WithAPIKey with NewService instead.
func New(client *http.Client) (*Service, error) {
	if client == nil {
		return nil, errors.New("client is nil")
	}
	s := &Service{client: client, BasePath: basePath}
	s.Api = NewApiService(s)
	s.Projects = NewProjectsService(s)
	return s, nil
}

type Service struct {
	client    *http.Client
	BasePath  string // API endpoint base URL
	UserAgent string // optional additional User-Agent fragment

	Api *ApiService

	Projects *ProjectsService
}

func (s *Service) userAgent() string {
	if s.UserAgent == "" {
		return googleapi.UserAgent
	}
	return googleapi.UserAgent + " " + s.UserAgent
}

func NewApiService(s *Service) *ApiService {
	rs := &ApiService{s: s}
	rs.Queue = NewApiQueueService(s)
	return rs
}

type ApiService struct {
	s *Service

	Queue *ApiQueueService
}

func NewApiQueueService(s *Service) *ApiQueueService {
	rs := &ApiQueueService{s: s}
	return rs
}

type ApiQueueService struct {
	s *Service
}

func NewProjectsService(s *Service) *ProjectsService {
	rs := &ProjectsService{s: s}
	rs.Locations = NewProjectsLocationsService(s)
	return rs
}

type ProjectsService struct {
	s *Service

	Locations *ProjectsLocationsService
}

func NewProjectsLocationsService(s *Service) *ProjectsLocationsService {
	rs := &ProjectsLocationsService{s: s}
	rs.Queues = NewProjectsLocationsQueuesService(s)
	return rs
}

type ProjectsLocationsService struct {
	s *Service

	Queues *ProjectsLocationsQueuesService
}

func NewProjectsLocationsQueuesService(s *Service) *ProjectsLocationsQueuesService {
	rs := &ProjectsLocationsQueuesService{s: s}
	rs.Tasks = NewProjectsLocationsQueuesTasksService(s)
	return rs
}

type ProjectsLocationsQueuesService struct {
	s *Service

	Tasks *ProjectsLocationsQueuesTasksService
}

func NewProjectsLocationsQueuesTasksService(s *Service) *ProjectsLocationsQueuesTasksService {
	rs := &ProjectsLocationsQueuesTasksService{s: s}
	return rs
}

type ProjectsLocationsQueuesTasksService struct {
	s *Service
}

// AcknowledgeTaskRequest: Request message for acknowledging a task using
// AcknowledgeTask.
type AcknowledgeTaskRequest struct {
	// ScheduleTime: Required. The task's current schedule time, available in the
	// schedule_time returned by LeaseTasks response or RenewLease response. This
	// restriction is to ensure that your worker currently holds the lease.
	ScheduleTime string `json:"scheduleTime,omitempty"`
	// ForceSendFields is a list of field names (e.g. "ScheduleTime") to
	// unconditionally include in API requests. By default, fields with empty or
	// default values are omitted from API requests. See
	// https://pkg.go.dev/google.golang.org/api#hdr-ForceSendFields for more
	// details.
	ForceSendFields []string `json:"-"`
	// NullFields is a list of field names (e.g. "ScheduleTime") to include in API
	// requests with the JSON null value. By default, fields with empty values are
	// omitted from API requests. See
	// https://pkg.go.dev/google.golang.org/api#hdr-NullFields for more details.
	NullFields []string `json:"-"`
}

func (s AcknowledgeTaskRequest) MarshalJSON() ([]byte, error) {
	type NoMethod AcknowledgeTaskRequest
	return gensupport.MarshalJSON(NoMethod(s), s.ForceSendFields, s.NullFields)
}

// AppEngineHttpRequest: App Engine HTTP request. The message defines the HTTP
// request that is sent to an App Engine app when the task is dispatched. This
// proto can only be used for tasks in a queue which has app_engine_http_target
// set. Using AppEngineHttpRequest requires `appengine.applications.get`
// (https://cloud.google.com/appengine/docs/admin-api/access-control) Google
// IAM permission for the project and the following scope:
// `https://www.googleapis.com/auth/cloud-platform` The task will be delivered
// to the App Engine app which belongs to the same project as the queue. For
// more information, see How Requests are Routed
// (https://cloud.google.com/appengine/docs/standard/python/how-requests-are-routed)
// and how routing is affected by dispatch files
// (https://cloud.google.com/appengine/docs/python/config/dispatchref). Traffic
// is encrypted during transport and never leaves Google datacenters. Because
// this traffic is carried over a communication mechanism internal to Google,
// you cannot explicitly set the protocol (for example, HTTP or HTTPS). The
// request to the handler, however, will appear to have used the HTTP protocol.
// The AppEngineRouting used to construct the URL that the task is delivered to
// can be set at the queue-level or task-level: * If set,
// app_engine_routing_override is used for all tasks in the queue, no matter
// what the setting is for the task-level app_engine_routing. The `url` that
// the task will be sent to is: * `url =` host `+` relative_url Tasks can be
// dispatched to secure app handlers, unsecure app handlers, and URIs
// restricted with `login: admin`
// (https://cloud.google.com/appengine/docs/standard/python/config/appref).
// Because tasks are not run as any user, they cannot be dispatched to URIs
// restricted with `login: required`
// (https://cloud.google.com/appengine/docs/standard/python/config/appref) Task
// dispatches also do not follow redirects. The task attempt has succeeded if
// the app's request handler returns an HTTP response code in the range [`200`
// - `299`]. The task attempt has failed if the app's handler returns a non-2xx
// response code or Cloud Tasks does not receive response before the deadline.
// Failed tasks will be retried according to the retry configuration. `503`
// (Service Unavailable) is considered an App Engine system error instead of an
// application error and will cause Cloud Tasks' traffic congestion control to
// temporarily throttle the queue's dispatches. Unlike other types of task
// targets, a `429` (Too Many Requests) response from an app handler does not
// cause traffic congestion control to throttle the queue.
type AppEngineHttpRequest struct {
	// AppEngineRouting: Task-level setting for App Engine routing. If set,
	// app_engine_routing_override is used for all tasks in the queue, no matter
	// what the setting is for the task-level app_engine_routing.
	AppEngineRouting *AppEngineRouting `json:"appEngineRouting,omitempty"`
	// Headers: HTTP request headers. This map contains the header field names and
	// values. Headers can be set when the task is created. Repeated headers are
	// not supported but a header value can contain commas. Cloud Tasks sets some
	// headers to default values: * `User-Agent`: By default, this header is
	// "AppEngine-Google; (+http://code.google.com/appengine)". This header can
	// be modified, but Cloud Tasks will append "AppEngine-Google;
	// (+http://code.google.com/appengine)" to the modified `User-Agent`. If the
	// task has a payload, Cloud Tasks sets the following headers: *
	// `Content-Type`: By default, the `Content-Type` header is set to
	// "application/octet-stream". The default can be overridden by explicitly
	// setting `Content-Type` to a particular media type when the task is created.
	// For example, `Content-Type` can be set to "application/json". *
	// `Content-Length`: This is computed by Cloud Tasks. This value is output
	// only. It cannot be changed. The headers below cannot be set or overridden: *
	// `Host` * `X-Google-*` * `X-AppEngine-*` In addition, Cloud Tasks sets some
	// headers when the task is dispatched, such as headers containing information
	// about the task; see request headers
	// (https://cloud.google.com/appengine/docs/python/taskqueue/push/creating-handlers#reading_request_headers).
	// These headers are set only when the task is dispatched, so they are not
	// visible when the task is returned in a Cloud Tasks response. Although there
	// is no specific limit for the maximum number of headers or the size, there is
	// a limit on the maximum size of the Task. For more information, see the
	// CreateTask documentation.
	Headers map[string]string `json:"headers,omitempty"`
	// HttpMethod: The HTTP method to use for the request. The default is POST. The
	// app's request handler for the task's target URL must be able to handle HTTP
	// requests with this http_method, otherwise the task attempt fails with error
	// code 405 (Method Not Allowed). See Writing a push task request handler
	// (https://cloud.google.com/appengine/docs/java/taskqueue/push/creating-handlers#writing_a_push_task_request_handler)
	// and the App Engine documentation for your runtime on How Requests are
	// Handled
	// (https://cloud.google.com/appengine/docs/standard/python3/how-requests-are-handled).
	//
	// Possible values:
	//   "HTTP_METHOD_UNSPECIFIED" - HTTP method unspecified
	//   "POST" - HTTP POST
	//   "GET" - HTTP GET
	//   "HEAD" - HTTP HEAD
	//   "PUT" - HTTP PUT
	//   "DELETE" - HTTP DELETE
	//   "PATCH" - HTTP PATCH
	//   "OPTIONS" - HTTP OPTIONS
	HttpMethod string `json:"httpMethod,omitempty"`
	// Payload: Payload. The payload will be sent as the HTTP message body. A
	// message body, and thus a payload, is allowed only if the HTTP method is POST
	// or PUT. It is an error to set a data payload on a task with an incompatible
	// HttpMethod.
	Payload string `json:"payload,omitempty"`
	// RelativeUrl: The relative URL. The relative URL must begin with "/" and must
	// be a valid HTTP relative URL. It can contain a path and query string
	// arguments. If the relative URL is empty, then the root path "/" will be
	// used. No spaces are allowed, and the maximum length allowed is 2083
	// characters.
	RelativeUrl string `json:"relativeUrl,omitempty"`
	// ForceSendFields is a list of field names (e.g. "AppEngineRouting") to
	// unconditionally include in API requests. By default, fields with empty or
	// default values are omitted from API requests. See
	// https://pkg.go.dev/google.golang.org/api#hdr-ForceSendFields for more
	// details.
	ForceSendFields []string `json:"-"`
	// NullFields is a list of field names (e.g. "AppEngineRouting") to include in
	// API requests with the JSON null value. By default, fields with empty values
	// are omitted from API requests. See
	// https://pkg.go.dev/google.golang.org/api#hdr-NullFields for more details.
	NullFields []string `json:"-"`
}

func (s AppEngineHttpRequest) MarshalJSON() ([]byte, error) {
	type NoMethod AppEngineHttpRequest
	return gensupport.MarshalJSON(NoMethod(s), s.ForceSendFields, s.NullFields)
}

// AppEngineHttpTarget: App Engine HTTP target. The task will be delivered to
// the App Engine application hostname specified by its AppEngineHttpTarget and
// AppEngineHttpRequest. The documentation for AppEngineHttpRequest explains
// how the task's host URL is constructed. Using AppEngineHttpTarget requires
// `appengine.applications.get`
// (https://cloud.google.com/appengine/docs/admin-api/access-control) Google
// IAM permission for the project and the following scope:
// `https://www.googleapis.com/auth/cloud-platform`
type AppEngineHttpTarget struct {
	// AppEngineRoutingOverride: Overrides for the task-level app_engine_routing.
	// If set, `app_engine_routing_override` is used for all tasks in the queue, no
	// matter what the setting is for the task-level app_engine_routing.
	AppEngineRoutingOverride *AppEngineRouting `json:"appEngineRoutingOverride,omitempty"`
	// ForceSendFields is a list of field names (e.g. "AppEngineRoutingOverride")
	// to unconditionally include in API requests. By default, fields with empty or
	// default values are omitted from API requests. See
	// https://pkg.go.dev/google.golang.org/api#hdr-ForceSendFields for more
	// details.
	ForceSendFields []string `json:"-"`
	// NullFields is a list of field names (e.g. "AppEngineRoutingOverride") to
	// include in API requests with the JSON null value. By default, fields with
	// empty values are omitted from API requests. See
	// https://pkg.go.dev/google.golang.org/api#hdr-NullFields for more details.
	NullFields []string `json:"-"`
}

func (s AppEngineHttpTarget) MarshalJSON() ([]byte, error) {
	type NoMethod AppEngineHttpTarget
	return gensupport.MarshalJSON(NoMethod(s), s.ForceSendFields, s.NullFields)
}

// AppEngineRouting: App Engine Routing. Defines routing characteristics
// specific to App Engine - service, version, and instance. For more
// information about services, versions, and instances see An Overview of App
// Engine
// (https://cloud.google.com/appengine/docs/python/an-overview-of-app-engine),
// Microservices Architecture on Google App Engine
// (https://cloud.google.com/appengine/docs/python/microservices-on-app-engine),
// App Engine Standard request routing
// (https://cloud.google.com/appengine/docs/standard/python/how-requests-are-routed),
// and App Engine Flex request routing
// (https://cloud.google.com/appengine/docs/flexible/python/how-requests-are-routed).
type AppEngineRouting struct {
	// Host: Output only. The host that the task is sent to. For more information,
	// see How Requests are Routed
	// (https://cloud.google.com/appengine/docs/standard/python/how-requests-are-routed).
	// The host is constructed as: * `host = [application_domain_name]` `|
	// [service] + '.' + [application_domain_name]` `| [version] + '.' +
	// [application_domain_name]` `| [version_dot_service]+ '.' +
	// [application_domain_name]` `| [instance] + '.' + [application_domain_name]`
	// `| [instance_dot_service] + '.' + [application_domain_name]` `|
	// [instance_dot_version] + '.' + [application_domain_name]` `|
	// [instance_dot_version_dot_service] + '.' + [application_domain_name]` *
	// `application_domain_name` = The domain name of the app, for example
	// .appspot.com, which is associated with the queue's project ID. Some tasks
	// which were created using the App Engine SDK use a custom domain name. *
	// `service =` service * `version =` version * `version_dot_service =` version
	// `+ '.' +` service * `instance =` instance * `instance_dot_service =`
	// instance `+ '.' +` service * `instance_dot_version =` instance `+ '.' +`
	// version * `instance_dot_version_dot_service =` instance `+ '.' +` version `+
	// '.' +` service If service is empty, then the task will be sent to the
	// service which is the default service when the task is attempted. If version
	// is empty, then the task will be sent to the version which is the default
	// version when the task is attempted. If instance is empty, then the task will
	// be sent to an instance which is available when the task is attempted. If
	// service, version, or instance is invalid, then the task will be sent to the
	// default version of the default service when the task is attempted.
	Host string `json:"host,omitempty"`
	// Instance: App instance. By default, the task is sent to an instance which is
	// available when the task is attempted. Requests can only be sent to a
	// specific instance if manual scaling is used in App Engine Standard
	// (https://cloud.google.com/appengine/docs/python/an-overview-of-app-engine?hl=en_US#scaling_types_and_instance_classes).
	// App Engine Flex does not support instances. For more information, see App
	// Engine Standard request routing
	// (https://cloud.google.com/appengine/docs/standard/python/how-requests-are-routed)
	// and App Engine Flex request routing
	// (https://cloud.google.com/appengine/docs/flexible/python/how-requests-are-routed).
	Instance string `json:"instance,omitempty"`
	// Service: App service. By default, the task is sent to the service which is
	// the default service when the task is attempted. For some queues or tasks
	// which were created using the App Engine Task Queue API, host is not parsable
	// into service, version, and instance. For example, some tasks which were
	// created using the App Engine SDK use a custom domain name; custom domains
	// are not parsed by Cloud Tasks. If host is not parsable, then service,
	// version, and instance are the empty string.
	Service string `json:"service,omitempty"`
	// Version: App version. By default, the task is sent to the version which is
	// the default version when the task is attempted. For some queues or tasks
	// which were created using the App Engine Task Queue API, host is not parsable
	// into service, version, and instance. For example, some tasks which were
	// created using the App Engine SDK use a custom domain name; custom domains
	// are not parsed by Cloud Tasks. If host is not parsable, then service,
	// version, and instance are the empty string.
	Version string `json:"version,omitempty"`
	// ForceSendFields is a list of field names (e.g. "Host") to unconditionally
	// include in API requests. By default, fields with empty or default values are
	// omitted from API requests. See
	// https://pkg.go.dev/google.golang.org/api#hdr-ForceSendFields for more
	// details.
	ForceSendFields []string `json:"-"`
	// NullFields is a list of field names (e.g. "Host") to include in API requests
	// with the JSON null value. By default, fields with empty values are omitted
	// from API requests. See
	// https://pkg.go.dev/google.golang.org/api#hdr-NullFields for more details.
	NullFields []string `json:"-"`
}

func (s AppEngineRouting) MarshalJSON() ([]byte, error) {
	type NoMethod AppEngineRouting
	return gensupport.MarshalJSON(NoMethod(s), s.ForceSendFields, s.NullFields)
}

// AttemptStatus: The status of a task attempt.
type AttemptStatus struct {
	// DispatchTime: Output only. The time that this attempt was dispatched.
	// `dispatch_time` will be truncated to the nearest microsecond.
	DispatchTime string `json:"dispatchTime,omitempty"`
	// ResponseStatus: Output only. The response from the target for this attempt.
	// If the task has not been attempted or the task is currently running then the
	// response status is unset.
	ResponseStatus *Status `json:"responseStatus,omitempty"`
	// ResponseTime: Output only. The time that this attempt response was received.
	// `response_time` will be truncated to the nearest microsecond.
	ResponseTime string `json:"responseTime,omitempty"`
	// ScheduleTime: Output only. The time that this attempt was scheduled.
	// `schedule_time` will be truncated to the nearest microsecond.
	ScheduleTime string `json:"scheduleTime,omitempty"`
	// ForceSendFields is a list of field names (e.g. "DispatchTime") to
	// unconditionally include in API requests. By default, fields with empty or
	// default values are omitted from API requests. See
	// https://pkg.go.dev/google.golang.org/api#hdr-ForceSendFields for more
	// details.
	ForceSendFields []string `json:"-"`
	// NullFields is a list of field names (e.g. "DispatchTime") to include in API
	// requests with the JSON null value. By default, fields with empty values are
	// omitted from API requests. See
	// https://pkg.go.dev/google.golang.org/api#hdr-NullFields for more details.
	NullFields []string `json:"-"`
}

func (s AttemptStatus) MarshalJSON() ([]byte, error) {
	type NoMethod AttemptStatus
	return gensupport.MarshalJSON(NoMethod(s), s.ForceSendFields, s.NullFields)
}

// Binding: Associates `members`, or principals, with a `role`.
type Binding struct {
	// Condition: The condition that is associated with this binding. If the
	// condition evaluates to `true`, then this binding applies to the current
	// request. If the condition evaluates to `false`, then this binding does not
	// apply to the current request. However, a different role binding might grant
	// the same role to one or more of the principals in this binding. To learn
	// which resources support conditions in their IAM policies, see the IAM
	// documentation
	// (https://cloud.google.com/iam/help/conditions/resource-policies).
	Condition *Expr `json:"condition,omitempty"`
	// Members: Specifies the principals requesting access for a Google Cloud
	// resource. `members` can have the following values: * `allUsers`: A special
	// identifier that represents anyone who is on the internet; with or without a
	// Google account. * `allAuthenticatedUsers`: A special identifier that
	// represents anyone who is authenticated with a Google account or a service
	// account. Does not include identities that come from external identity
	// providers (IdPs) through identity federation. * `user:{emailid}`: An email
	// address that represents a specific Google account. For example,
	// `<EMAIL>` . * `serviceAccount:{emailid}`: An email address that
	// represents a Google service account. For example,
	// `<EMAIL>`. *
	// `serviceAccount:{projectid}.svc.id.goog[{namespace}/{kubernetes-sa}]`: An
	// identifier for a Kubernetes service account
	// (https://cloud.google.com/kubernetes-engine/docs/how-to/kubernetes-service-accounts).
	// For example, `my-project.svc.id.goog[my-namespace/my-kubernetes-sa]`. *
	// `group:{emailid}`: An email address that represents a Google group. For
	// example, `<EMAIL>`. * `domain:{domain}`: The G Suite domain
	// (primary) that represents all the users of that domain. For example,
	// `google.com` or `example.com`. *
	// `principal://iam.googleapis.com/locations/global/workforcePools/{pool_id}/sub
	// ject/{subject_attribute_value}`: A single identity in a workforce identity
	// pool. *
	// `principalSet://iam.googleapis.com/locations/global/workforcePools/{pool_id}/
	// group/{group_id}`: All workforce identities in a group. *
	// `principalSet://iam.googleapis.com/locations/global/workforcePools/{pool_id}/
	// attribute.{attribute_name}/{attribute_value}`: All workforce identities with
	// a specific attribute value. *
	// `principalSet://iam.googleapis.com/locations/global/workforcePools/{pool_id}/
	// *`: All identities in a workforce identity pool. *
	// `principal://iam.googleapis.com/projects/{project_number}/locations/global/wo
	// rkloadIdentityPools/{pool_id}/subject/{subject_attribute_value}`: A single
	// identity in a workload identity pool. *
	// `principalSet://iam.googleapis.com/projects/{project_number}/locations/global
	// /workloadIdentityPools/{pool_id}/group/{group_id}`: A workload identity pool
	// group. *
	// `principalSet://iam.googleapis.com/projects/{project_number}/locations/global
	// /workloadIdentityPools/{pool_id}/attribute.{attribute_name}/{attribute_value}
	// `: All identities in a workload identity pool with a certain attribute. *
	// `principalSet://iam.googleapis.com/projects/{project_number}/locations/global
	// /workloadIdentityPools/{pool_id}/*`: All identities in a workload identity
	// pool. * `deleted:user:{emailid}?uid={uniqueid}`: An email address (plus
	// unique identifier) representing a user that has been recently deleted. For
	// example, `<EMAIL>?uid=123456789012345678901`. If the user is
	// recovered, this value reverts to `user:{emailid}` and the recovered user
	// retains the role in the binding. *
	// `deleted:serviceAccount:{emailid}?uid={uniqueid}`: An email address (plus
	// unique identifier) representing a service account that has been recently
	// deleted. For example,
	// `<EMAIL>?uid=123456789012345678901`. If the
	// service account is undeleted, this value reverts to
	// `serviceAccount:{emailid}` and the undeleted service account retains the
	// role in the binding. * `deleted:group:{emailid}?uid={uniqueid}`: An email
	// address (plus unique identifier) representing a Google group that has been
	// recently deleted. For example,
	// `<EMAIL>?uid=123456789012345678901`. If the group is recovered,
	// this value reverts to `group:{emailid}` and the recovered group retains the
	// role in the binding. *
	// `deleted:principal://iam.googleapis.com/locations/global/workforcePools/{pool
	// _id}/subject/{subject_attribute_value}`: Deleted single identity in a
	// workforce identity pool. For example,
	// `deleted:principal://iam.googleapis.com/locations/global/workforcePools/my-po
	// ol-id/subject/my-subject-attribute-value`.
	Members []string `json:"members,omitempty"`
	// Role: Role that is assigned to the list of `members`, or principals. For
	// example, `roles/viewer`, `roles/editor`, or `roles/owner`. For an overview
	// of the IAM roles and permissions, see the IAM documentation
	// (https://cloud.google.com/iam/docs/roles-overview). For a list of the
	// available pre-defined roles, see here
	// (https://cloud.google.com/iam/docs/understanding-roles).
	Role string `json:"role,omitempty"`
	// ForceSendFields is a list of field names (e.g. "Condition") to
	// unconditionally include in API requests. By default, fields with empty or
	// default values are omitted from API requests. See
	// https://pkg.go.dev/google.golang.org/api#hdr-ForceSendFields for more
	// details.
	ForceSendFields []string `json:"-"`
	// NullFields is a list of field names (e.g. "Condition") to include in API
	// requests with the JSON null value. By default, fields with empty values are
	// omitted from API requests. See
	// https://pkg.go.dev/google.golang.org/api#hdr-NullFields for more details.
	NullFields []string `json:"-"`
}

func (s Binding) MarshalJSON() ([]byte, error) {
	type NoMethod Binding
	return gensupport.MarshalJSON(NoMethod(s), s.ForceSendFields, s.NullFields)
}

// BufferTaskRequest: Request message for BufferTask.
type BufferTaskRequest struct {
	// Body: Optional. Body of the HTTP request. The body can take any generic
	// value. The value is written to the HttpRequest of the [Task].
	Body *HttpBody `json:"body,omitempty"`
	// ForceSendFields is a list of field names (e.g. "Body") to unconditionally
	// include in API requests. By default, fields with empty or default values are
	// omitted from API requests. See
	// https://pkg.go.dev/google.golang.org/api#hdr-ForceSendFields for more
	// details.
	ForceSendFields []string `json:"-"`
	// NullFields is a list of field names (e.g. "Body") to include in API requests
	// with the JSON null value. By default, fields with empty values are omitted
	// from API requests. See
	// https://pkg.go.dev/google.golang.org/api#hdr-NullFields for more details.
	NullFields []string `json:"-"`
}

func (s BufferTaskRequest) MarshalJSON() ([]byte, error) {
	type NoMethod BufferTaskRequest
	return gensupport.MarshalJSON(NoMethod(s), s.ForceSendFields, s.NullFields)
}

// BufferTaskResponse: Response message for BufferTask.
type BufferTaskResponse struct {
	// Task: The created task.
	Task *Task `json:"task,omitempty"`

	// ServerResponse contains the HTTP response code and headers from the server.
	googleapi.ServerResponse `json:"-"`
	// ForceSendFields is a list of field names (e.g. "Task") to unconditionally
	// include in API requests. By default, fields with empty or default values are
	// omitted from API requests. See
	// https://pkg.go.dev/google.golang.org/api#hdr-ForceSendFields for more
	// details.
	ForceSendFields []string `json:"-"`
	// NullFields is a list of field names (e.g. "Task") to include in API requests
	// with the JSON null value. By default, fields with empty values are omitted
	// from API requests. See
	// https://pkg.go.dev/google.golang.org/api#hdr-NullFields for more details.
	NullFields []string `json:"-"`
}

func (s BufferTaskResponse) MarshalJSON() ([]byte, error) {
	type NoMethod BufferTaskResponse
	return gensupport.MarshalJSON(NoMethod(s), s.ForceSendFields, s.NullFields)
}

// CancelLeaseRequest: Request message for canceling a lease using CancelLease.
type CancelLeaseRequest struct {
	// ResponseView: The response_view specifies which subset of the Task will be
	// returned. By default response_view is BASIC; not all information is
	// retrieved by default because some data, such as payloads, might be desirable
	// to return only when needed because of its large size or because of the
	// sensitivity of data that it contains. Authorization for FULL requires
	// `cloudtasks.tasks.fullView` Google IAM (https://cloud.google.com/iam/)
	// permission on the Task resource.
	//
	// Possible values:
	//   "VIEW_UNSPECIFIED" - Unspecified. Defaults to BASIC.
	//   "BASIC" - The basic view omits fields which can be large or can contain
	// sensitive data. This view does not include the (payload in
	// AppEngineHttpRequest and payload in PullMessage). These payloads are
	// desirable to return only when needed, because they can be large and because
	// of the sensitivity of the data that you choose to store in it.
	//   "FULL" - All information is returned. Authorization for FULL requires
	// `cloudtasks.tasks.fullView` [Google IAM](https://cloud.google.com/iam/)
	// permission on the Queue resource.
	ResponseView string `json:"responseView,omitempty"`
	// ScheduleTime: Required. The task's current schedule time, available in the
	// schedule_time returned by LeaseTasks response or RenewLease response. This
	// restriction is to ensure that your worker currently holds the lease.
	ScheduleTime string `json:"scheduleTime,omitempty"`
	// ForceSendFields is a list of field names (e.g. "ResponseView") to
	// unconditionally include in API requests. By default, fields with empty or
	// default values are omitted from API requests. See
	// https://pkg.go.dev/google.golang.org/api#hdr-ForceSendFields for more
	// details.
	ForceSendFields []string `json:"-"`
	// NullFields is a list of field names (e.g. "ResponseView") to include in API
	// requests with the JSON null value. By default, fields with empty values are
	// omitted from API requests. See
	// https://pkg.go.dev/google.golang.org/api#hdr-NullFields for more details.
	NullFields []string `json:"-"`
}

func (s CancelLeaseRequest) MarshalJSON() ([]byte, error) {
	type NoMethod CancelLeaseRequest
	return gensupport.MarshalJSON(NoMethod(s), s.ForceSendFields, s.NullFields)
}

// CmekConfig: Describes the customer-managed encryption key (CMEK)
// configuration associated with a project and location.
type CmekConfig struct {
	// KmsKey: Resource name of the Cloud KMS key, of the form
	// `projects/PROJECT_ID/locations/LOCATION_ID/keyRings/KEY_RING_ID/cryptoKeys/KE
	// Y_ID`, that will be used to encrypt the Queues & Tasks in the region.
	// Setting this as blank will turn off CMEK encryption.
	KmsKey string `json:"kmsKey,omitempty"`
	// Name: Output only. The config resource name which includes the project and
	// location and must end in 'cmekConfig', in the format
	// projects/PROJECT_ID/locations/LOCATION_ID/cmekConfig`
	Name string `json:"name,omitempty"`

	// ServerResponse contains the HTTP response code and headers from the server.
	googleapi.ServerResponse `json:"-"`
	// ForceSendFields is a list of field names (e.g. "KmsKey") to unconditionally
	// include in API requests. By default, fields with empty or default values are
	// omitted from API requests. See
	// https://pkg.go.dev/google.golang.org/api#hdr-ForceSendFields for more
	// details.
	ForceSendFields []string `json:"-"`
	// NullFields is a list of field names (e.g. "KmsKey") to include in API
	// requests with the JSON null value. By default, fields with empty values are
	// omitted from API requests. See
	// https://pkg.go.dev/google.golang.org/api#hdr-NullFields for more details.
	NullFields []string `json:"-"`
}

func (s CmekConfig) MarshalJSON() ([]byte, error) {
	type NoMethod CmekConfig
	return gensupport.MarshalJSON(NoMethod(s), s.ForceSendFields, s.NullFields)
}

// CreateTaskRequest: Request message for CreateTask.
type CreateTaskRequest struct {
	// ResponseView: The response_view specifies which subset of the Task will be
	// returned. By default response_view is BASIC; not all information is
	// retrieved by default because some data, such as payloads, might be desirable
	// to return only when needed because of its large size or because of the
	// sensitivity of data that it contains. Authorization for FULL requires
	// `cloudtasks.tasks.fullView` Google IAM (https://cloud.google.com/iam/)
	// permission on the Task resource.
	//
	// Possible values:
	//   "VIEW_UNSPECIFIED" - Unspecified. Defaults to BASIC.
	//   "BASIC" - The basic view omits fields which can be large or can contain
	// sensitive data. This view does not include the (payload in
	// AppEngineHttpRequest and payload in PullMessage). These payloads are
	// desirable to return only when needed, because they can be large and because
	// of the sensitivity of the data that you choose to store in it.
	//   "FULL" - All information is returned. Authorization for FULL requires
	// `cloudtasks.tasks.fullView` [Google IAM](https://cloud.google.com/iam/)
	// permission on the Queue resource.
	ResponseView string `json:"responseView,omitempty"`
	// Task: Required. The task to add. Task names have the following format:
	// `projects/PROJECT_ID/locations/LOCATION_ID/queues/QUEUE_ID/tasks/TASK_ID`.
	// The user can optionally specify a task name. If a name is not specified then
	// the system will generate a random unique task id, which will be set in the
	// task returned in the response. If schedule_time is not set or is in the past
	// then Cloud Tasks will set it to the current time. Task De-duplication:
	// Explicitly specifying a task ID enables task de-duplication. If a task's ID
	// is identical to that of an existing task or a task that was deleted or
	// completed recently then the call will fail with ALREADY_EXISTS. The IDs of
	// deleted tasks are not immediately available for reuse. It can take up to 4
	// hours (or 9 days if the task's queue was created using a queue.yaml or
	// queue.xml) for the task ID to be released and made available again. Because
	// there is an extra lookup cost to identify duplicate task names, these
	// CreateTask calls have significantly increased latency. Using hashed strings
	// for the task id or for the prefix of the task id is recommended. Choosing
	// task ids that are sequential or have sequential prefixes, for example using
	// a timestamp, causes an increase in latency and error rates in all task
	// commands. The infrastructure relies on an approximately uniform distribution
	// of task ids to store and serve tasks efficiently.
	Task *Task `json:"task,omitempty"`
	// ForceSendFields is a list of field names (e.g. "ResponseView") to
	// unconditionally include in API requests. By default, fields with empty or
	// default values are omitted from API requests. See
	// https://pkg.go.dev/google.golang.org/api#hdr-ForceSendFields for more
	// details.
	ForceSendFields []string `json:"-"`
	// NullFields is a list of field names (e.g. "ResponseView") to include in API
	// requests with the JSON null value. By default, fields with empty values are
	// omitted from API requests. See
	// https://pkg.go.dev/google.golang.org/api#hdr-NullFields for more details.
	NullFields []string `json:"-"`
}

func (s CreateTaskRequest) MarshalJSON() ([]byte, error) {
	type NoMethod CreateTaskRequest
	return gensupport.MarshalJSON(NoMethod(s), s.ForceSendFields, s.NullFields)
}

// Empty: A generic empty message that you can re-use to avoid defining
// duplicated empty messages in your APIs. A typical example is to use it as
// the request or the response type of an API method. For instance: service Foo
// { rpc Bar(google.protobuf.Empty) returns (google.protobuf.Empty); }
type Empty struct {
	// ServerResponse contains the HTTP response code and headers from the server.
	googleapi.ServerResponse `json:"-"`
}

// Expr: Represents a textual expression in the Common Expression Language
// (CEL) syntax. CEL is a C-like expression language. The syntax and semantics
// of CEL are documented at https://github.com/google/cel-spec. Example
// (Comparison): title: "Summary size limit" description: "Determines if a
// summary is less than 100 chars" expression: "document.summary.size() < 100"
// Example (Equality): title: "Requestor is owner" description: "Determines if
// requestor is the document owner" expression: "document.owner ==
// request.auth.claims.email" Example (Logic): title: "Public documents"
// description: "Determine whether the document should be publicly visible"
// expression: "document.type != 'private' && document.type != 'internal'"
// Example (Data Manipulation): title: "Notification string" description:
// "Create a notification string with a timestamp." expression: "'New message
// received at ' + string(document.create_time)" The exact variables and
// functions that may be referenced within an expression are determined by the
// service that evaluates it. See the service documentation for additional
// information.
type Expr struct {
	// Description: Optional. Description of the expression. This is a longer text
	// which describes the expression, e.g. when hovered over it in a UI.
	Description string `json:"description,omitempty"`
	// Expression: Textual representation of an expression in Common Expression
	// Language syntax.
	Expression string `json:"expression,omitempty"`
	// Location: Optional. String indicating the location of the expression for
	// error reporting, e.g. a file name and a position in the file.
	Location string `json:"location,omitempty"`
	// Title: Optional. Title for the expression, i.e. a short string describing
	// its purpose. This can be used e.g. in UIs which allow to enter the
	// expression.
	Title string `json:"title,omitempty"`
	// ForceSendFields is a list of field names (e.g. "Description") to
	// unconditionally include in API requests. By default, fields with empty or
	// default values are omitted from API requests. See
	// https://pkg.go.dev/google.golang.org/api#hdr-ForceSendFields for more
	// details.
	ForceSendFields []string `json:"-"`
	// NullFields is a list of field names (e.g. "Description") to include in API
	// requests with the JSON null value. By default, fields with empty values are
	// omitted from API requests. See
	// https://pkg.go.dev/google.golang.org/api#hdr-NullFields for more details.
	NullFields []string `json:"-"`
}

func (s Expr) MarshalJSON() ([]byte, error) {
	type NoMethod Expr
	return gensupport.MarshalJSON(NoMethod(s), s.ForceSendFields, s.NullFields)
}

// GetIamPolicyRequest: Request message for `GetIamPolicy` method.
type GetIamPolicyRequest struct {
	// Options: OPTIONAL: A `GetPolicyOptions` object for specifying options to
	// `GetIamPolicy`.
	Options *GetPolicyOptions `json:"options,omitempty"`
	// ForceSendFields is a list of field names (e.g. "Options") to unconditionally
	// include in API requests. By default, fields with empty or default values are
	// omitted from API requests. See
	// https://pkg.go.dev/google.golang.org/api#hdr-ForceSendFields for more
	// details.
	ForceSendFields []string `json:"-"`
	// NullFields is a list of field names (e.g. "Options") to include in API
	// requests with the JSON null value. By default, fields with empty values are
	// omitted from API requests. See
	// https://pkg.go.dev/google.golang.org/api#hdr-NullFields for more details.
	NullFields []string `json:"-"`
}

func (s GetIamPolicyRequest) MarshalJSON() ([]byte, error) {
	type NoMethod GetIamPolicyRequest
	return gensupport.MarshalJSON(NoMethod(s), s.ForceSendFields, s.NullFields)
}

// GetPolicyOptions: Encapsulates settings provided to GetIamPolicy.
type GetPolicyOptions struct {
	// RequestedPolicyVersion: Optional. The maximum policy version that will be
	// used to format the policy. Valid values are 0, 1, and 3. Requests specifying
	// an invalid value will be rejected. Requests for policies with any
	// conditional role bindings must specify version 3. Policies with no
	// conditional role bindings may specify any valid value or leave the field
	// unset. The policy in the response might use the policy version that you
	// specified, or it might use a lower policy version. For example, if you
	// specify version 3, but the policy has no conditional role bindings, the
	// response uses version 1. To learn which resources support conditions in
	// their IAM policies, see the IAM documentation
	// (https://cloud.google.com/iam/help/conditions/resource-policies).
	RequestedPolicyVersion int64 `json:"requestedPolicyVersion,omitempty"`
	// ForceSendFields is a list of field names (e.g. "RequestedPolicyVersion") to
	// unconditionally include in API requests. By default, fields with empty or
	// default values are omitted from API requests. See
	// https://pkg.go.dev/google.golang.org/api#hdr-ForceSendFields for more
	// details.
	ForceSendFields []string `json:"-"`
	// NullFields is a list of field names (e.g. "RequestedPolicyVersion") to
	// include in API requests with the JSON null value. By default, fields with
	// empty values are omitted from API requests. See
	// https://pkg.go.dev/google.golang.org/api#hdr-NullFields for more details.
	NullFields []string `json:"-"`
}

func (s GetPolicyOptions) MarshalJSON() ([]byte, error) {
	type NoMethod GetPolicyOptions
	return gensupport.MarshalJSON(NoMethod(s), s.ForceSendFields, s.NullFields)
}

// Header: Defines a header message. A header can have a key and a value.
type Header struct {
	// Key: The key of the header.
	Key string `json:"key,omitempty"`
	// Value: The value of the header.
	Value string `json:"value,omitempty"`
	// ForceSendFields is a list of field names (e.g. "Key") to unconditionally
	// include in API requests. By default, fields with empty or default values are
	// omitted from API requests. See
	// https://pkg.go.dev/google.golang.org/api#hdr-ForceSendFields for more
	// details.
	ForceSendFields []string `json:"-"`
	// NullFields is a list of field names (e.g. "Key") to include in API requests
	// with the JSON null value. By default, fields with empty values are omitted
	// from API requests. See
	// https://pkg.go.dev/google.golang.org/api#hdr-NullFields for more details.
	NullFields []string `json:"-"`
}

func (s Header) MarshalJSON() ([]byte, error) {
	type NoMethod Header
	return gensupport.MarshalJSON(NoMethod(s), s.ForceSendFields, s.NullFields)
}

// HeaderOverride: Wraps the Header object.
type HeaderOverride struct {
	// Header: Header embodying a key and a value. Do not put business sensitive or
	// personally identifying data in the HTTP Header Override Configuration or
	// other similar fields in accordance with Section 12 (Resource Fields) of the
	// Service Specific Terms (https://cloud.google.com/terms/service-terms).
	Header *Header `json:"header,omitempty"`
	// ForceSendFields is a list of field names (e.g. "Header") to unconditionally
	// include in API requests. By default, fields with empty or default values are
	// omitted from API requests. See
	// https://pkg.go.dev/google.golang.org/api#hdr-ForceSendFields for more
	// details.
	ForceSendFields []string `json:"-"`
	// NullFields is a list of field names (e.g. "Header") to include in API
	// requests with the JSON null value. By default, fields with empty values are
	// omitted from API requests. See
	// https://pkg.go.dev/google.golang.org/api#hdr-NullFields for more details.
	NullFields []string `json:"-"`
}

func (s HeaderOverride) MarshalJSON() ([]byte, error) {
	type NoMethod HeaderOverride
	return gensupport.MarshalJSON(NoMethod(s), s.ForceSendFields, s.NullFields)
}

// HttpBody: Message that represents an arbitrary HTTP body. It should only be
// used for payload formats that can't be represented as JSON, such as raw
// binary or an HTML page. This message can be used both in streaming and
// non-streaming API methods in the request as well as the response. It can be
// used as a top-level request field, which is convenient if one wants to
// extract parameters from either the URL or HTTP template into the request
// fields and also want access to the raw HTTP body. Example: message
// GetResourceRequest { // A unique request id. string request_id = 1; // The
// raw HTTP body is bound to this field. google.api.HttpBody http_body = 2; }
// service ResourceService { rpc GetResource(GetResourceRequest) returns
// (google.api.HttpBody); rpc UpdateResource(google.api.HttpBody) returns
// (google.protobuf.Empty); } Example with streaming methods: service
// CaldavService { rpc GetCalendar(stream google.api.HttpBody) returns (stream
// google.api.HttpBody); rpc UpdateCalendar(stream google.api.HttpBody) returns
// (stream google.api.HttpBody); } Use of this type only changes how the
// request and response bodies are handled, all other features will continue to
// work unchanged.
type HttpBody struct {
	// ContentType: The HTTP Content-Type header value specifying the content type
	// of the body.
	ContentType string `json:"contentType,omitempty"`
	// Data: The HTTP request/response body as raw binary.
	Data string `json:"data,omitempty"`
	// Extensions: Application specific response metadata. Must be set in the first
	// response for streaming APIs.
	Extensions []googleapi.RawMessage `json:"extensions,omitempty"`
	// ForceSendFields is a list of field names (e.g. "ContentType") to
	// unconditionally include in API requests. By default, fields with empty or
	// default values are omitted from API requests. See
	// https://pkg.go.dev/google.golang.org/api#hdr-ForceSendFields for more
	// details.
	ForceSendFields []string `json:"-"`
	// NullFields is a list of field names (e.g. "ContentType") to include in API
	// requests with the JSON null value. By default, fields with empty values are
	// omitted from API requests. See
	// https://pkg.go.dev/google.golang.org/api#hdr-NullFields for more details.
	NullFields []string `json:"-"`
}

func (s HttpBody) MarshalJSON() ([]byte, error) {
	type NoMethod HttpBody
	return gensupport.MarshalJSON(NoMethod(s), s.ForceSendFields, s.NullFields)
}

// HttpRequest: HTTP request. The task will be pushed to the worker as an HTTP
// request. An HTTP request embodies a url, an http method, headers, body and
// authorization for the http task.
type HttpRequest struct {
	// Body: HTTP request body. A request body is allowed only if the HTTP method
	// is POST, PUT, or PATCH. It is an error to set body on a task with an
	// incompatible HttpMethod.
	Body string `json:"body,omitempty"`
	// Headers: HTTP request headers. This map contains the header field names and
	// values. Headers can be set when running the task is created or task is
	// created. These headers represent a subset of the headers that will accompany
	// the task's HTTP request. Some HTTP request headers will be ignored or
	// replaced. A partial list of headers that will be ignored or replaced is: *
	// Any header that is prefixed with "X-CloudTasks-" will be treated as service
	// header. Service headers define properties of the task and are predefined in
	// CloudTask. * Host: This will be computed by Cloud Tasks and derived from
	// HttpRequest.url. * Content-Length: This will be computed by Cloud Tasks. *
	// User-Agent: This will be set to "Google-Cloud-Tasks". * `X-Google-*`:
	// Google use only. * `X-AppEngine-*`: Google use only. `Content-Type` won't be
	// set by Cloud Tasks. You can explicitly set `Content-Type` to a media type
	// when the task is created. For example, `Content-Type` can be set to
	// "application/octet-stream" or "application/json". Headers which can have
	// multiple values (according to RFC2616) can be specified using
	// comma-separated values. The size of the headers must be less than 80KB.
	Headers map[string]string `json:"headers,omitempty"`
	// HttpMethod: The HTTP method to use for the request. The default is POST.
	//
	// Possible values:
	//   "HTTP_METHOD_UNSPECIFIED" - HTTP method unspecified
	//   "POST" - HTTP POST
	//   "GET" - HTTP GET
	//   "HEAD" - HTTP HEAD
	//   "PUT" - HTTP PUT
	//   "DELETE" - HTTP DELETE
	//   "PATCH" - HTTP PATCH
	//   "OPTIONS" - HTTP OPTIONS
	HttpMethod string `json:"httpMethod,omitempty"`
	// OauthToken: If specified, an OAuth token
	// (https://developers.google.com/identity/protocols/OAuth2) will be generated
	// and attached as an `Authorization` header in the HTTP request. This type of
	// authorization should generally only be used when calling Google APIs hosted
	// on *.googleapis.com.
	OauthToken *OAuthToken `json:"oauthToken,omitempty"`
	// OidcToken: If specified, an OIDC
	// (https://developers.google.com/identity/protocols/OpenIDConnect) token will
	// be generated and attached as an `Authorization` header in the HTTP request.
	// This type of authorization can be used for many scenarios, including calling
	// Cloud Run, or endpoints where you intend to validate the token yourself.
	OidcToken *OidcToken `json:"oidcToken,omitempty"`
	// Url: Required. The full url path that the request will be sent to. This
	// string must begin with either "http://" or "https://". Some examples are:
	// `http://acme.com` and `https://acme.com/sales:8080`. Cloud Tasks will encode
	// some characters for safety and compatibility. The maximum allowed URL length
	// is 2083 characters after encoding. The `Location` header response from a
	// redirect response [`300` - `399`] may be followed. The redirect is not
	// counted as a separate attempt.
	Url string `json:"url,omitempty"`
	// ForceSendFields is a list of field names (e.g. "Body") to unconditionally
	// include in API requests. By default, fields with empty or default values are
	// omitted from API requests. See
	// https://pkg.go.dev/google.golang.org/api#hdr-ForceSendFields for more
	// details.
	ForceSendFields []string `json:"-"`
	// NullFields is a list of field names (e.g. "Body") to include in API requests
	// with the JSON null value. By default, fields with empty values are omitted
	// from API requests. See
	// https://pkg.go.dev/google.golang.org/api#hdr-NullFields for more details.
	NullFields []string `json:"-"`
}

func (s HttpRequest) MarshalJSON() ([]byte, error) {
	type NoMethod HttpRequest
	return gensupport.MarshalJSON(NoMethod(s), s.ForceSendFields, s.NullFields)
}

// HttpTarget: HTTP target. When specified as a Queue, all the tasks with
// [HttpRequest] will be overridden according to the target.
type HttpTarget struct {
	// HeaderOverrides: HTTP target headers. This map contains the header field
	// names and values. Headers will be set when running the task is created
	// and/or task is created. These headers represent a subset of the headers that
	// will accompany the task's HTTP request. Some HTTP request headers will be
	// ignored or replaced. A partial list of headers that will be ignored or
	// replaced is: * Any header that is prefixed with "X-CloudTasks-" will be
	// treated as service header. Service headers define properties of the task and
	// are predefined in CloudTask. * Host: This will be computed by Cloud Tasks
	// and derived from HttpRequest.url. * Content-Length: This will be computed by
	// Cloud Tasks. * User-Agent: This will be set to "Google-CloudTasks". *
	// `X-Google-*`: Google use only. * `X-AppEngine-*`: Google use only.
	// `Content-Type` won't be set by Cloud Tasks. You can explicitly set
	// `Content-Type` to a media type when the task is created. For example,
	// `Content-Type` can be set to "application/octet-stream" or
	// "application/json". Headers which can have multiple values (according to
	// RFC2616) can be specified using comma-separated values. The size of the
	// headers must be less than 80KB. Queue-level headers to override headers of
	// all the tasks in the queue. Do not put business sensitive or personally
	// identifying data in the HTTP Header Override Configuration or other similar
	// fields in accordance with Section 12 (Resource Fields) of the Service
	// Specific Terms (https://cloud.google.com/terms/service-terms).
	HeaderOverrides []*HeaderOverride `json:"headerOverrides,omitempty"`
	// HttpMethod: The HTTP method to use for the request. When specified, it
	// overrides HttpRequest for the task. Note that if the value is set to
	// HttpMethod the HttpRequest of the task will be ignored at execution time.
	//
	// Possible values:
	//   "HTTP_METHOD_UNSPECIFIED" - HTTP method unspecified
	//   "POST" - HTTP POST
	//   "GET" - HTTP GET
	//   "HEAD" - HTTP HEAD
	//   "PUT" - HTTP PUT
	//   "DELETE" - HTTP DELETE
	//   "PATCH" - HTTP PATCH
	//   "OPTIONS" - HTTP OPTIONS
	HttpMethod string `json:"httpMethod,omitempty"`
	// OauthToken: If specified, an OAuth token
	// (https://developers.google.com/identity/protocols/OAuth2) is generated and
	// attached as an `Authorization` header in the HTTP request. This type of
	// authorization should generally be used only when calling Google APIs hosted
	// on *.googleapis.com. Note that both the service account email and the scope
	// MUST be specified when using the queue-level authorization override.
	OauthToken *OAuthToken `json:"oauthToken,omitempty"`
	// OidcToken: If specified, an OIDC
	// (https://developers.google.com/identity/protocols/OpenIDConnect) token is
	// generated and attached as an `Authorization` header in the HTTP request.
	// This type of authorization can be used for many scenarios, including calling
	// Cloud Run, or endpoints where you intend to validate the token yourself.
	// Note that both the service account email and the audience MUST be specified
	// when using the queue-level authorization override.
	OidcToken *OidcToken `json:"oidcToken,omitempty"`
	// UriOverride: Uri override. When specified, overrides the execution Uri for
	// all the tasks in the queue.
	UriOverride *UriOverride `json:"uriOverride,omitempty"`
	// ForceSendFields is a list of field names (e.g. "HeaderOverrides") to
	// unconditionally include in API requests. By default, fields with empty or
	// default values are omitted from API requests. See
	// https://pkg.go.dev/google.golang.org/api#hdr-ForceSendFields for more
	// details.
	ForceSendFields []string `json:"-"`
	// NullFields is a list of field names (e.g. "HeaderOverrides") to include in
	// API requests with the JSON null value. By default, fields with empty values
	// are omitted from API requests. See
	// https://pkg.go.dev/google.golang.org/api#hdr-NullFields for more details.
	NullFields []string `json:"-"`
}

func (s HttpTarget) MarshalJSON() ([]byte, error) {
	type NoMethod HttpTarget
	return gensupport.MarshalJSON(NoMethod(s), s.ForceSendFields, s.NullFields)
}

// LeaseTasksRequest: Request message for leasing tasks using LeaseTasks.
type LeaseTasksRequest struct {
	// Filter: `filter` can be used to specify a subset of tasks to lease. When
	// `filter` is set to `tag=` then the response will contain only tasks whose
	// tag is equal to ``. `` must be less than 500 characters. When `filter` is
	// set to `tag_function=oldest_tag()`, only tasks which have the same tag as
	// the task with the oldest schedule_time will be returned. Grammar Syntax: *
	// `filter = "tag=" tag | "tag_function=" function` * `tag = string` *
	// `function = "oldest_tag()" The `oldest_tag()` function returns tasks which
	// have the same tag as the oldest task (ordered by schedule time). SDK
	// compatibility: Although the SDK allows tags to be either string or bytes
	// (https://cloud.google.com/appengine/docs/standard/java/javadoc/com/google/appengine/api/taskqueue/TaskOptions.html#tag-byte:A-),
	// only UTF-8 encoded tags can be used in Cloud Tasks. Tag which aren't UTF-8
	// encoded can't be used in the filter and the task's tag will be displayed as
	// empty in Cloud Tasks.
	Filter string `json:"filter,omitempty"`
	// LeaseDuration: Required. The duration of the lease. Each task returned in
	// the response will have its schedule_time set to the current time plus the
	// `lease_duration`. The task is leased until its schedule_time; thus, the task
	// will not be returned to another LeaseTasks call before its schedule_time.
	// After the worker has successfully finished the work associated with the
	// task, the worker must call via AcknowledgeTask before the schedule_time.
	// Otherwise the task will be returned to a later LeaseTasks call so that
	// another worker can retry it. The maximum lease duration is 1 week.
	// `lease_duration` will be truncated to the nearest second.
	LeaseDuration string `json:"leaseDuration,omitempty"`
	// MaxTasks: The maximum number of tasks to lease. The system will make a best
	// effort to return as close to as `max_tasks` as possible. The largest that
	// `max_tasks` can be is 1000. The maximum total size of a lease tasks response
	// is 32 MB. If the sum of all task sizes requested reaches this limit, fewer
	// tasks than requested are returned.
	MaxTasks int64 `json:"maxTasks,omitempty"`
	// ResponseView: The response_view specifies which subset of the Task will be
	// returned. By default response_view is BASIC; not all information is
	// retrieved by default because some data, such as payloads, might be desirable
	// to return only when needed because of its large size or because of the
	// sensitivity of data that it contains. Authorization for FULL requires
	// `cloudtasks.tasks.fullView` Google IAM (https://cloud.google.com/iam/)
	// permission on the Task resource.
	//
	// Possible values:
	//   "VIEW_UNSPECIFIED" - Unspecified. Defaults to BASIC.
	//   "BASIC" - The basic view omits fields which can be large or can contain
	// sensitive data. This view does not include the (payload in
	// AppEngineHttpRequest and payload in PullMessage). These payloads are
	// desirable to return only when needed, because they can be large and because
	// of the sensitivity of the data that you choose to store in it.
	//   "FULL" - All information is returned. Authorization for FULL requires
	// `cloudtasks.tasks.fullView` [Google IAM](https://cloud.google.com/iam/)
	// permission on the Queue resource.
	ResponseView string `json:"responseView,omitempty"`
	// ForceSendFields is a list of field names (e.g. "Filter") to unconditionally
	// include in API requests. By default, fields with empty or default values are
	// omitted from API requests. See
	// https://pkg.go.dev/google.golang.org/api#hdr-ForceSendFields for more
	// details.
	ForceSendFields []string `json:"-"`
	// NullFields is a list of field names (e.g. "Filter") to include in API
	// requests with the JSON null value. By default, fields with empty values are
	// omitted from API requests. See
	// https://pkg.go.dev/google.golang.org/api#hdr-NullFields for more details.
	NullFields []string `json:"-"`
}

func (s LeaseTasksRequest) MarshalJSON() ([]byte, error) {
	type NoMethod LeaseTasksRequest
	return gensupport.MarshalJSON(NoMethod(s), s.ForceSendFields, s.NullFields)
}

// LeaseTasksResponse: Response message for leasing tasks using LeaseTasks.
type LeaseTasksResponse struct {
	// Tasks: The leased tasks.
	Tasks []*Task `json:"tasks,omitempty"`

	// ServerResponse contains the HTTP response code and headers from the server.
	googleapi.ServerResponse `json:"-"`
	// ForceSendFields is a list of field names (e.g. "Tasks") to unconditionally
	// include in API requests. By default, fields with empty or default values are
	// omitted from API requests. See
	// https://pkg.go.dev/google.golang.org/api#hdr-ForceSendFields for more
	// details.
	ForceSendFields []string `json:"-"`
	// NullFields is a list of field names (e.g. "Tasks") to include in API
	// requests with the JSON null value. By default, fields with empty values are
	// omitted from API requests. See
	// https://pkg.go.dev/google.golang.org/api#hdr-NullFields for more details.
	NullFields []string `json:"-"`
}

func (s LeaseTasksResponse) MarshalJSON() ([]byte, error) {
	type NoMethod LeaseTasksResponse
	return gensupport.MarshalJSON(NoMethod(s), s.ForceSendFields, s.NullFields)
}

// ListLocationsResponse: The response message for Locations.ListLocations.
type ListLocationsResponse struct {
	// Locations: A list of locations that matches the specified filter in the
	// request.
	Locations []*Location `json:"locations,omitempty"`
	// NextPageToken: The standard List next-page token.
	NextPageToken string `json:"nextPageToken,omitempty"`

	// ServerResponse contains the HTTP response code and headers from the server.
	googleapi.ServerResponse `json:"-"`
	// ForceSendFields is a list of field names (e.g. "Locations") to
	// unconditionally include in API requests. By default, fields with empty or
	// default values are omitted from API requests. See
	// https://pkg.go.dev/google.golang.org/api#hdr-ForceSendFields for more
	// details.
	ForceSendFields []string `json:"-"`
	// NullFields is a list of field names (e.g. "Locations") to include in API
	// requests with the JSON null value. By default, fields with empty values are
	// omitted from API requests. See
	// https://pkg.go.dev/google.golang.org/api#hdr-NullFields for more details.
	NullFields []string `json:"-"`
}

func (s ListLocationsResponse) MarshalJSON() ([]byte, error) {
	type NoMethod ListLocationsResponse
	return gensupport.MarshalJSON(NoMethod(s), s.ForceSendFields, s.NullFields)
}

// ListQueuesResponse: Response message for ListQueues.
type ListQueuesResponse struct {
	// NextPageToken: A token to retrieve next page of results. To return the next
	// page of results, call ListQueues with this value as the page_token. If the
	// next_page_token is empty, there are no more results. The page token is valid
	// for only 2 hours.
	NextPageToken string `json:"nextPageToken,omitempty"`
	// Queues: The list of queues.
	Queues []*Queue `json:"queues,omitempty"`

	// ServerResponse contains the HTTP response code and headers from the server.
	googleapi.ServerResponse `json:"-"`
	// ForceSendFields is a list of field names (e.g. "NextPageToken") to
	// unconditionally include in API requests. By default, fields with empty or
	// default values are omitted from API requests. See
	// https://pkg.go.dev/google.golang.org/api#hdr-ForceSendFields for more
	// details.
	ForceSendFields []string `json:"-"`
	// NullFields is a list of field names (e.g. "NextPageToken") to include in API
	// requests with the JSON null value. By default, fields with empty values are
	// omitted from API requests. See
	// https://pkg.go.dev/google.golang.org/api#hdr-NullFields for more details.
	NullFields []string `json:"-"`
}

func (s ListQueuesResponse) MarshalJSON() ([]byte, error) {
	type NoMethod ListQueuesResponse
	return gensupport.MarshalJSON(NoMethod(s), s.ForceSendFields, s.NullFields)
}

// ListTasksResponse: Response message for listing tasks using ListTasks.
type ListTasksResponse struct {
	// NextPageToken: A token to retrieve next page of results. To return the next
	// page of results, call ListTasks with this value as the page_token. If the
	// next_page_token is empty, there are no more results.
	NextPageToken string `json:"nextPageToken,omitempty"`
	// Tasks: The list of tasks.
	Tasks []*Task `json:"tasks,omitempty"`

	// ServerResponse contains the HTTP response code and headers from the server.
	googleapi.ServerResponse `json:"-"`
	// ForceSendFields is a list of field names (e.g. "NextPageToken") to
	// unconditionally include in API requests. By default, fields with empty or
	// default values are omitted from API requests. See
	// https://pkg.go.dev/google.golang.org/api#hdr-ForceSendFields for more
	// details.
	ForceSendFields []string `json:"-"`
	// NullFields is a list of field names (e.g. "NextPageToken") to include in API
	// requests with the JSON null value. By default, fields with empty values are
	// omitted from API requests. See
	// https://pkg.go.dev/google.golang.org/api#hdr-NullFields for more details.
	NullFields []string `json:"-"`
}

func (s ListTasksResponse) MarshalJSON() ([]byte, error) {
	type NoMethod ListTasksResponse
	return gensupport.MarshalJSON(NoMethod(s), s.ForceSendFields, s.NullFields)
}

// Location: A resource that represents a Google Cloud location.
type Location struct {
	// DisplayName: The friendly name for this location, typically a nearby city
	// name. For example, "Tokyo".
	DisplayName string `json:"displayName,omitempty"`
	// Labels: Cross-service attributes for the location. For example
	// {"cloud.googleapis.com/region": "us-east1"}
	Labels map[string]string `json:"labels,omitempty"`
	// LocationId: The canonical id for this location. For example: "us-east1".
	LocationId string `json:"locationId,omitempty"`
	// Metadata: Service-specific metadata. For example the available capacity at
	// the given location.
	Metadata googleapi.RawMessage `json:"metadata,omitempty"`
	// Name: Resource name for the location, which may vary between
	// implementations. For example:
	// "projects/example-project/locations/us-east1"
	Name string `json:"name,omitempty"`

	// ServerResponse contains the HTTP response code and headers from the server.
	googleapi.ServerResponse `json:"-"`
	// ForceSendFields is a list of field names (e.g. "DisplayName") to
	// unconditionally include in API requests. By default, fields with empty or
	// default values are omitted from API requests. See
	// https://pkg.go.dev/google.golang.org/api#hdr-ForceSendFields for more
	// details.
	ForceSendFields []string `json:"-"`
	// NullFields is a list of field names (e.g. "DisplayName") to include in API
	// requests with the JSON null value. By default, fields with empty values are
	// omitted from API requests. See
	// https://pkg.go.dev/google.golang.org/api#hdr-NullFields for more details.
	NullFields []string `json:"-"`
}

func (s Location) MarshalJSON() ([]byte, error) {
	type NoMethod Location
	return gensupport.MarshalJSON(NoMethod(s), s.ForceSendFields, s.NullFields)
}

// OAuthToken: Contains information needed for generating an OAuth token
// (https://developers.google.com/identity/protocols/OAuth2). This type of
// authorization should generally only be used when calling Google APIs hosted
// on *.googleapis.com.
type OAuthToken struct {
	// Scope: OAuth scope to be used for generating OAuth access token. If not
	// specified, "https://www.googleapis.com/auth/cloud-platform" will be used.
	Scope string `json:"scope,omitempty"`
	// ServiceAccountEmail: Service account email
	// (https://cloud.google.com/iam/docs/service-accounts) to be used for
	// generating OAuth token. The service account must be within the same project
	// as the queue. The caller must have iam.serviceAccounts.actAs permission for
	// the service account.
	ServiceAccountEmail string `json:"serviceAccountEmail,omitempty"`
	// ForceSendFields is a list of field names (e.g. "Scope") to unconditionally
	// include in API requests. By default, fields with empty or default values are
	// omitted from API requests. See
	// https://pkg.go.dev/google.golang.org/api#hdr-ForceSendFields for more
	// details.
	ForceSendFields []string `json:"-"`
	// NullFields is a list of field names (e.g. "Scope") to include in API
	// requests with the JSON null value. By default, fields with empty values are
	// omitted from API requests. See
	// https://pkg.go.dev/google.golang.org/api#hdr-NullFields for more details.
	NullFields []string `json:"-"`
}

func (s OAuthToken) MarshalJSON() ([]byte, error) {
	type NoMethod OAuthToken
	return gensupport.MarshalJSON(NoMethod(s), s.ForceSendFields, s.NullFields)
}

// OidcToken: Contains information needed for generating an OpenID Connect
// token (https://developers.google.com/identity/protocols/OpenIDConnect). This
// type of authorization can be used for many scenarios, including calling
// Cloud Run, or endpoints where you intend to validate the token yourself.
type OidcToken struct {
	// Audience: Audience to be used when generating OIDC token. If not specified,
	// the URI specified in target will be used.
	Audience string `json:"audience,omitempty"`
	// ServiceAccountEmail: Service account email
	// (https://cloud.google.com/iam/docs/service-accounts) to be used for
	// generating OIDC token. The service account must be within the same project
	// as the queue. The caller must have iam.serviceAccounts.actAs permission for
	// the service account.
	ServiceAccountEmail string `json:"serviceAccountEmail,omitempty"`
	// ForceSendFields is a list of field names (e.g. "Audience") to
	// unconditionally include in API requests. By default, fields with empty or
	// default values are omitted from API requests. See
	// https://pkg.go.dev/google.golang.org/api#hdr-ForceSendFields for more
	// details.
	ForceSendFields []string `json:"-"`
	// NullFields is a list of field names (e.g. "Audience") to include in API
	// requests with the JSON null value. By default, fields with empty values are
	// omitted from API requests. See
	// https://pkg.go.dev/google.golang.org/api#hdr-NullFields for more details.
	NullFields []string `json:"-"`
}

func (s OidcToken) MarshalJSON() ([]byte, error) {
	type NoMethod OidcToken
	return gensupport.MarshalJSON(NoMethod(s), s.ForceSendFields, s.NullFields)
}

// PathOverride: PathOverride. Path message defines path override for HTTP
// targets.
type PathOverride struct {
	// Path: The URI path (e.g., /users/1234). Default is an empty string.
	Path string `json:"path,omitempty"`
	// ForceSendFields is a list of field names (e.g. "Path") to unconditionally
	// include in API requests. By default, fields with empty or default values are
	// omitted from API requests. See
	// https://pkg.go.dev/google.golang.org/api#hdr-ForceSendFields for more
	// details.
	ForceSendFields []string `json:"-"`
	// NullFields is a list of field names (e.g. "Path") to include in API requests
	// with the JSON null value. By default, fields with empty values are omitted
	// from API requests. See
	// https://pkg.go.dev/google.golang.org/api#hdr-NullFields for more details.
	NullFields []string `json:"-"`
}

func (s PathOverride) MarshalJSON() ([]byte, error) {
	type NoMethod PathOverride
	return gensupport.MarshalJSON(NoMethod(s), s.ForceSendFields, s.NullFields)
}

// PauseQueueRequest: Request message for PauseQueue.
type PauseQueueRequest struct {
}

// Policy: An Identity and Access Management (IAM) policy, which specifies
// access controls for Google Cloud resources. A `Policy` is a collection of
// `bindings`. A `binding` binds one or more `members`, or principals, to a
// single `role`. Principals can be user accounts, service accounts, Google
// groups, and domains (such as G Suite). A `role` is a named list of
// permissions; each `role` can be an IAM predefined role or a user-created
// custom role. For some types of Google Cloud resources, a `binding` can also
// specify a `condition`, which is a logical expression that allows access to a
// resource only if the expression evaluates to `true`. A condition can add
// constraints based on attributes of the request, the resource, or both. To
// learn which resources support conditions in their IAM policies, see the IAM
// documentation
// (https://cloud.google.com/iam/help/conditions/resource-policies). **JSON
// example:** ``` { "bindings": [ { "role":
// "roles/resourcemanager.organizationAdmin", "members": [
// "user:<EMAIL>", "group:<EMAIL>", "domain:google.com",
// "serviceAccount:<EMAIL>" ] }, { "role":
// "roles/resourcemanager.organizationViewer", "members": [
// "user:<EMAIL>" ], "condition": { "title": "expirable access",
// "description": "Does not grant access after Sep 2020", "expression":
// "request.time < timestamp('2020-10-01T00:00:00.000Z')", } } ], "etag":
// "BwWWja0YfJA=", "version": 3 } ``` **YAML example:** ``` bindings: -
// members: - user:<EMAIL> - group:<EMAIL> -
// domain:google.com - serviceAccount:<EMAIL>
// role: roles/resourcemanager.organizationAdmin - members: -
// user:<EMAIL> role: roles/resourcemanager.organizationViewer
// condition: title: expirable access description: Does not grant access after
// Sep 2020 expression: request.time < timestamp('2020-10-01T00:00:00.000Z')
// etag: BwWWja0YfJA= version: 3 ``` For a description of IAM and its features,
// see the IAM documentation (https://cloud.google.com/iam/docs/).
type Policy struct {
	// Bindings: Associates a list of `members`, or principals, with a `role`.
	// Optionally, may specify a `condition` that determines how and when the
	// `bindings` are applied. Each of the `bindings` must contain at least one
	// principal. The `bindings` in a `Policy` can refer to up to 1,500 principals;
	// up to 250 of these principals can be Google groups. Each occurrence of a
	// principal counts towards these limits. For example, if the `bindings` grant
	// 50 different roles to `user:<EMAIL>`, and not to any other
	// principal, then you can add another 1,450 principals to the `bindings` in
	// the `Policy`.
	Bindings []*Binding `json:"bindings,omitempty"`
	// Etag: `etag` is used for optimistic concurrency control as a way to help
	// prevent simultaneous updates of a policy from overwriting each other. It is
	// strongly suggested that systems make use of the `etag` in the
	// read-modify-write cycle to perform policy updates in order to avoid race
	// conditions: An `etag` is returned in the response to `getIamPolicy`, and
	// systems are expected to put that etag in the request to `setIamPolicy` to
	// ensure that their change will be applied to the same version of the policy.
	// **Important:** If you use IAM Conditions, you must include the `etag` field
	// whenever you call `setIamPolicy`. If you omit this field, then IAM allows
	// you to overwrite a version `3` policy with a version `1` policy, and all of
	// the conditions in the version `3` policy are lost.
	Etag string `json:"etag,omitempty"`
	// Version: Specifies the format of the policy. Valid values are `0`, `1`, and
	// `3`. Requests that specify an invalid value are rejected. Any operation that
	// affects conditional role bindings must specify version `3`. This requirement
	// applies to the following operations: * Getting a policy that includes a
	// conditional role binding * Adding a conditional role binding to a policy *
	// Changing a conditional role binding in a policy * Removing any role binding,
	// with or without a condition, from a policy that includes conditions
	// **Important:** If you use IAM Conditions, you must include the `etag` field
	// whenever you call `setIamPolicy`. If you omit this field, then IAM allows
	// you to overwrite a version `3` policy with a version `1` policy, and all of
	// the conditions in the version `3` policy are lost. If a policy does not
	// include any conditions, operations on that policy may specify any valid
	// version or leave the field unset. To learn which resources support
	// conditions in their IAM policies, see the IAM documentation
	// (https://cloud.google.com/iam/help/conditions/resource-policies).
	Version int64 `json:"version,omitempty"`

	// ServerResponse contains the HTTP response code and headers from the server.
	googleapi.ServerResponse `json:"-"`
	// ForceSendFields is a list of field names (e.g. "Bindings") to
	// unconditionally include in API requests. By default, fields with empty or
	// default values are omitted from API requests. See
	// https://pkg.go.dev/google.golang.org/api#hdr-ForceSendFields for more
	// details.
	ForceSendFields []string `json:"-"`
	// NullFields is a list of field names (e.g. "Bindings") to include in API
	// requests with the JSON null value. By default, fields with empty values are
	// omitted from API requests. See
	// https://pkg.go.dev/google.golang.org/api#hdr-NullFields for more details.
	NullFields []string `json:"-"`
}

func (s Policy) MarshalJSON() ([]byte, error) {
	type NoMethod Policy
	return gensupport.MarshalJSON(NoMethod(s), s.ForceSendFields, s.NullFields)
}

// PullMessage: The pull message contains data that can be used by the caller
// of LeaseTasks to process the task. This proto can only be used for tasks in
// a queue which has pull_target set.
type PullMessage struct {
	// Payload: A data payload consumed by the worker to execute the task.
	Payload string `json:"payload,omitempty"`
	// Tag: The task's tag. Tags allow similar tasks to be processed in a batch. If
	// you label tasks with a tag, your worker can lease tasks with the same tag
	// using filter. For example, if you want to aggregate the events associated
	// with a specific user once a day, you could tag tasks with the user ID. The
	// task's tag can only be set when the task is created. The tag must be less
	// than 500 characters. SDK compatibility: Although the SDK allows tags to be
	// either string or bytes
	// (https://cloud.google.com/appengine/docs/standard/java/javadoc/com/google/appengine/api/taskqueue/TaskOptions.html#tag-byte:A-),
	// only UTF-8 encoded tags can be used in Cloud Tasks. If a tag isn't UTF-8
	// encoded, the tag will be empty when the task is returned by Cloud Tasks.
	Tag string `json:"tag,omitempty"`
	// ForceSendFields is a list of field names (e.g. "Payload") to unconditionally
	// include in API requests. By default, fields with empty or default values are
	// omitted from API requests. See
	// https://pkg.go.dev/google.golang.org/api#hdr-ForceSendFields for more
	// details.
	ForceSendFields []string `json:"-"`
	// NullFields is a list of field names (e.g. "Payload") to include in API
	// requests with the JSON null value. By default, fields with empty values are
	// omitted from API requests. See
	// https://pkg.go.dev/google.golang.org/api#hdr-NullFields for more details.
	NullFields []string `json:"-"`
}

func (s PullMessage) MarshalJSON() ([]byte, error) {
	type NoMethod PullMessage
	return gensupport.MarshalJSON(NoMethod(s), s.ForceSendFields, s.NullFields)
}

// PullTarget: Pull target.
type PullTarget struct {
}

// PurgeQueueRequest: Request message for PurgeQueue.
type PurgeQueueRequest struct {
}

// QueryOverride: QueryOverride. Query message defines query override for HTTP
// targets.
type QueryOverride struct {
	// QueryParams: The query parameters (e.g., qparam1=123&qparam2=456). Default
	// is an empty string.
	QueryParams string `json:"queryParams,omitempty"`
	// ForceSendFields is a list of field names (e.g. "QueryParams") to
	// unconditionally include in API requests. By default, fields with empty or
	// default values are omitted from API requests. See
	// https://pkg.go.dev/google.golang.org/api#hdr-ForceSendFields for more
	// details.
	ForceSendFields []string `json:"-"`
	// NullFields is a list of field names (e.g. "QueryParams") to include in API
	// requests with the JSON null value. By default, fields with empty values are
	// omitted from API requests. See
	// https://pkg.go.dev/google.golang.org/api#hdr-NullFields for more details.
	NullFields []string `json:"-"`
}

func (s QueryOverride) MarshalJSON() ([]byte, error) {
	type NoMethod QueryOverride
	return gensupport.MarshalJSON(NoMethod(s), s.ForceSendFields, s.NullFields)
}

// Queue: A queue is a container of related tasks. Queues are configured to
// manage how those tasks are dispatched. Configurable properties include rate
// limits, retry options, target types, and others.
type Queue struct {
	// AppEngineHttpTarget: App Engine HTTP target. An App Engine queue is a queue
	// that has an AppEngineHttpTarget.
	AppEngineHttpTarget *AppEngineHttpTarget `json:"appEngineHttpTarget,omitempty"`
	// HttpTarget: An http_target is used to override the target values for HTTP
	// tasks.
	HttpTarget *HttpTarget `json:"httpTarget,omitempty"`
	// Name: Caller-specified and required in CreateQueue, after which it becomes
	// output only. The queue name. The queue name must have the following format:
	// `projects/PROJECT_ID/locations/LOCATION_ID/queues/QUEUE_ID` * `PROJECT_ID`
	// can contain letters ([A-Za-z]), numbers ([0-9]), hyphens (-), colons (:), or
	// periods (.). For more information, see Identifying projects
	// (https://cloud.google.com/resource-manager/docs/creating-managing-projects#identifying_projects)
	// * `LOCATION_ID` is the canonical ID for the queue's location. The list of
	// available locations can be obtained by calling ListLocations. For more
	// information, see https://cloud.google.com/about/locations/. * `QUEUE_ID` can
	// contain letters ([A-Za-z]), numbers ([0-9]), or hyphens (-). The maximum
	// length is 100 characters.
	Name string `json:"name,omitempty"`
	// PullTarget: Pull target. A pull queue is a queue that has a PullTarget.
	PullTarget *PullTarget `json:"pullTarget,omitempty"`
	// PurgeTime: Output only. The last time this queue was purged. All tasks that
	// were created before this time were purged. A queue can be purged using
	// PurgeQueue, the App Engine Task Queue SDK, or the Cloud Console
	// (https://cloud.google.com/appengine/docs/standard/python/taskqueue/push/deleting-tasks-and-queues#purging_all_tasks_from_a_queue).
	// Purge time will be truncated to the nearest microsecond. Purge time will be
	// unset if the queue has never been purged.
	PurgeTime string `json:"purgeTime,omitempty"`
	// RateLimits: Rate limits for task dispatches. rate_limits and retry_config
	// are related because they both control task attempts however they control how
	// tasks are attempted in different ways: * rate_limits controls the total rate
	// of dispatches from a queue (i.e. all traffic dispatched from the queue,
	// regardless of whether the dispatch is from a first attempt or a retry). *
	// retry_config controls what happens to particular a task after its first
	// attempt fails. That is, retry_config controls task retries (the second
	// attempt, third attempt, etc).
	RateLimits *RateLimits `json:"rateLimits,omitempty"`
	// RetryConfig: Settings that determine the retry behavior. * For tasks created
	// using Cloud Tasks: the queue-level retry settings apply to all tasks in the
	// queue that were created using Cloud Tasks. Retry settings cannot be set on
	// individual tasks. * For tasks created using the App Engine SDK: the
	// queue-level retry settings apply to all tasks in the queue which do not have
	// retry settings explicitly set on the task and were created by the App Engine
	// SDK. See App Engine documentation
	// (https://cloud.google.com/appengine/docs/standard/python/taskqueue/push/retrying-tasks).
	RetryConfig *RetryConfig `json:"retryConfig,omitempty"`
	// State: Output only. The state of the queue. `state` can only be changed by
	// called PauseQueue, ResumeQueue, or uploading queue.yaml/xml
	// (https://cloud.google.com/appengine/docs/python/config/queueref).
	// UpdateQueue cannot be used to change `state`.
	//
	// Possible values:
	//   "STATE_UNSPECIFIED" - Unspecified state.
	//   "RUNNING" - The queue is running. Tasks can be dispatched. If the queue
	// was created using Cloud Tasks and the queue has had no activity (method
	// calls or task dispatches) for 30 days, the queue may take a few minutes to
	// re-activate. Some method calls may return NOT_FOUND and tasks may not be
	// dispatched for a few minutes until the queue has been re-activated.
	//   "PAUSED" - Tasks are paused by the user. If the queue is paused then Cloud
	// Tasks will stop delivering tasks from it, but more tasks can still be added
	// to it by the user. When a pull queue is paused, all LeaseTasks calls will
	// return a FAILED_PRECONDITION.
	//   "DISABLED" - The queue is disabled. A queue becomes `DISABLED` when
	// [queue.yaml](https://cloud.google.com/appengine/docs/python/config/queueref)
	// or
	// [queue.xml](https://cloud.google.com/appengine/docs/standard/java/config/queu
	// eref) is uploaded which does not contain the queue. You cannot directly
	// disable a queue. When a queue is disabled, tasks can still be added to a
	// queue but the tasks are not dispatched and LeaseTasks calls return a
	// `FAILED_PRECONDITION` error. To permanently delete this queue and all of its
	// tasks, call DeleteQueue.
	State string `json:"state,omitempty"`
	// Stats: Output only. The realtime, informational statistics for a queue. In
	// order to receive the statistics the caller should include this field in the
	// FieldMask.
	Stats *QueueStats `json:"stats,omitempty"`
	// TaskTtl: The maximum amount of time that a task will be retained in this
	// queue. Queues created by Cloud Tasks have a default `task_ttl` of 31 days.
	// After a task has lived for `task_ttl`, the task will be deleted regardless
	// of whether it was dispatched or not. The `task_ttl` for queues created via
	// queue.yaml/xml is equal to the maximum duration because there is a storage
	// quota (https://cloud.google.com/appengine/quotas#Task_Queue) for these
	// queues. To view the maximum valid duration, see the documentation for
	// Duration.
	TaskTtl string `json:"taskTtl,omitempty"`
	// TombstoneTtl: The task tombstone time to live (TTL). After a task is deleted
	// or completed, the task's tombstone is retained for the length of time
	// specified by `tombstone_ttl`. The tombstone is used by task de-duplication;
	// another task with the same name can't be created until the tombstone has
	// expired. For more information about task de-duplication, see the
	// documentation for CreateTaskRequest. Queues created by Cloud Tasks have a
	// default `tombstone_ttl` of 1 hour.
	TombstoneTtl string `json:"tombstoneTtl,omitempty"`

	// ServerResponse contains the HTTP response code and headers from the server.
	googleapi.ServerResponse `json:"-"`
	// ForceSendFields is a list of field names (e.g. "AppEngineHttpTarget") to
	// unconditionally include in API requests. By default, fields with empty or
	// default values are omitted from API requests. See
	// https://pkg.go.dev/google.golang.org/api#hdr-ForceSendFields for more
	// details.
	ForceSendFields []string `json:"-"`
	// NullFields is a list of field names (e.g. "AppEngineHttpTarget") to include
	// in API requests with the JSON null value. By default, fields with empty
	// values are omitted from API requests. See
	// https://pkg.go.dev/google.golang.org/api#hdr-NullFields for more details.
	NullFields []string `json:"-"`
}

func (s Queue) MarshalJSON() ([]byte, error) {
	type NoMethod Queue
	return gensupport.MarshalJSON(NoMethod(s), s.ForceSendFields, s.NullFields)
}

// QueueStats: Statistics for a queue.
type QueueStats struct {
	// ConcurrentDispatchesCount: Output only. The number of requests that the
	// queue has dispatched but has not received a reply for yet.
	ConcurrentDispatchesCount int64 `json:"concurrentDispatchesCount,omitempty,string"`
	// EffectiveExecutionRate: Output only. The current maximum number of tasks per
	// second executed by the queue. The maximum value of this variable is
	// controlled by the RateLimits of the Queue. However, this value could be less
	// to avoid overloading the endpoints tasks in the queue are targeting.
	EffectiveExecutionRate float64 `json:"effectiveExecutionRate,omitempty"`
	// ExecutedLastMinuteCount: Output only. The number of tasks that the queue has
	// dispatched and received a reply for during the last minute. This variable
	// counts both successful and non-successful executions.
	ExecutedLastMinuteCount int64 `json:"executedLastMinuteCount,omitempty,string"`
	// OldestEstimatedArrivalTime: Output only. An estimation of the nearest time
	// in the future where a task in the queue is scheduled to be executed.
	OldestEstimatedArrivalTime string `json:"oldestEstimatedArrivalTime,omitempty"`
	// TasksCount: Output only. An estimation of the number of tasks in the queue,
	// that is, the tasks in the queue that haven't been executed, the tasks in the
	// queue which the queue has dispatched but has not yet received a reply for,
	// and the failed tasks that the queue is retrying.
	TasksCount int64 `json:"tasksCount,omitempty,string"`
	// ForceSendFields is a list of field names (e.g. "ConcurrentDispatchesCount")
	// to unconditionally include in API requests. By default, fields with empty or
	// default values are omitted from API requests. See
	// https://pkg.go.dev/google.golang.org/api#hdr-ForceSendFields for more
	// details.
	ForceSendFields []string `json:"-"`
	// NullFields is a list of field names (e.g. "ConcurrentDispatchesCount") to
	// include in API requests with the JSON null value. By default, fields with
	// empty values are omitted from API requests. See
	// https://pkg.go.dev/google.golang.org/api#hdr-NullFields for more details.
	NullFields []string `json:"-"`
}

func (s QueueStats) MarshalJSON() ([]byte, error) {
	type NoMethod QueueStats
	return gensupport.MarshalJSON(NoMethod(s), s.ForceSendFields, s.NullFields)
}

func (s *QueueStats) UnmarshalJSON(data []byte) error {
	type NoMethod QueueStats
	var s1 struct {
		EffectiveExecutionRate gensupport.JSONFloat64 `json:"effectiveExecutionRate"`
		*NoMethod
	}
	s1.NoMethod = (*NoMethod)(s)
	if err := json.Unmarshal(data, &s1); err != nil {
		return err
	}
	s.EffectiveExecutionRate = float64(s1.EffectiveExecutionRate)
	return nil
}

// RateLimits: Rate limits. This message determines the maximum rate that tasks
// can be dispatched by a queue, regardless of whether the dispatch is a first
// task attempt or a retry. Note: The debugging command, RunTask, will run a
// task even if the queue has reached its RateLimits.
type RateLimits struct {
	// MaxBurstSize: The max burst size. Max burst size limits how fast tasks in
	// queue are processed when many tasks are in the queue and the rate is high.
	// This field allows the queue to have a high rate so processing starts shortly
	// after a task is enqueued, but still limits resource usage when many tasks
	// are enqueued in a short period of time. The token bucket
	// (https://wikipedia.org/wiki/Token_Bucket) algorithm is used to control the
	// rate of task dispatches. Each queue has a token bucket that holds tokens, up
	// to the maximum specified by `max_burst_size`. Each time a task is
	// dispatched, a token is removed from the bucket. Tasks will be dispatched
	// until the queue's bucket runs out of tokens. The bucket will be continuously
	// refilled with new tokens based on max_dispatches_per_second. The default
	// value of `max_burst_size` is picked by Cloud Tasks based on the value of
	// max_dispatches_per_second. The maximum value of `max_burst_size` is 500. For
	// App Engine queues that were created or updated using `queue.yaml/xml`,
	// `max_burst_size` is equal to bucket_size
	// (https://cloud.google.com/appengine/docs/standard/python/config/queueref#bucket_size).
	// If UpdateQueue is called on a queue without explicitly setting a value for
	// `max_burst_size`, `max_burst_size` value will get updated if UpdateQueue is
	// updating max_dispatches_per_second.
	MaxBurstSize int64 `json:"maxBurstSize,omitempty"`
	// MaxConcurrentTasks: The maximum number of concurrent tasks that Cloud Tasks
	// allows to be dispatched for this queue. After this threshold has been
	// reached, Cloud Tasks stops dispatching tasks until the number of concurrent
	// requests decreases. If unspecified when the queue is created, Cloud Tasks
	// will pick the default. The maximum allowed value is 5,000. This field is
	// output only for pull queues and always -1, which indicates no limit. No
	// other queue types can have `max_concurrent_tasks` set to -1. This field has
	// the same meaning as max_concurrent_requests in queue.yaml/xml
	// (https://cloud.google.com/appengine/docs/standard/python/config/queueref#max_concurrent_requests).
	MaxConcurrentTasks int64 `json:"maxConcurrentTasks,omitempty"`
	// MaxTasksDispatchedPerSecond: The maximum rate at which tasks are dispatched
	// from this queue. If unspecified when the queue is created, Cloud Tasks will
	// pick the default. * For App Engine queues, the maximum allowed value is 500.
	// * This field is output only for pull queues. In addition to the
	// `max_tasks_dispatched_per_second` limit, a maximum of 10 QPS of LeaseTasks
	// requests are allowed per pull queue. This field has the same meaning as rate
	// in queue.yaml/xml
	// (https://cloud.google.com/appengine/docs/standard/python/config/queueref#rate).
	MaxTasksDispatchedPerSecond float64 `json:"maxTasksDispatchedPerSecond,omitempty"`
	// ForceSendFields is a list of field names (e.g. "MaxBurstSize") to
	// unconditionally include in API requests. By default, fields with empty or
	// default values are omitted from API requests. See
	// https://pkg.go.dev/google.golang.org/api#hdr-ForceSendFields for more
	// details.
	ForceSendFields []string `json:"-"`
	// NullFields is a list of field names (e.g. "MaxBurstSize") to include in API
	// requests with the JSON null value. By default, fields with empty values are
	// omitted from API requests. See
	// https://pkg.go.dev/google.golang.org/api#hdr-NullFields for more details.
	NullFields []string `json:"-"`
}

func (s RateLimits) MarshalJSON() ([]byte, error) {
	type NoMethod RateLimits
	return gensupport.MarshalJSON(NoMethod(s), s.ForceSendFields, s.NullFields)
}

func (s *RateLimits) UnmarshalJSON(data []byte) error {
	type NoMethod RateLimits
	var s1 struct {
		MaxTasksDispatchedPerSecond gensupport.JSONFloat64 `json:"maxTasksDispatchedPerSecond"`
		*NoMethod
	}
	s1.NoMethod = (*NoMethod)(s)
	if err := json.Unmarshal(data, &s1); err != nil {
		return err
	}
	s.MaxTasksDispatchedPerSecond = float64(s1.MaxTasksDispatchedPerSecond)
	return nil
}

// RenewLeaseRequest: Request message for renewing a lease using RenewLease.
type RenewLeaseRequest struct {
	// LeaseDuration: Required. The desired new lease duration, starting from now.
	// The maximum lease duration is 1 week. `lease_duration` will be truncated to
	// the nearest second.
	LeaseDuration string `json:"leaseDuration,omitempty"`
	// ResponseView: The response_view specifies which subset of the Task will be
	// returned. By default response_view is BASIC; not all information is
	// retrieved by default because some data, such as payloads, might be desirable
	// to return only when needed because of its large size or because of the
	// sensitivity of data that it contains. Authorization for FULL requires
	// `cloudtasks.tasks.fullView` Google IAM (https://cloud.google.com/iam/)
	// permission on the Task resource.
	//
	// Possible values:
	//   "VIEW_UNSPECIFIED" - Unspecified. Defaults to BASIC.
	//   "BASIC" - The basic view omits fields which can be large or can contain
	// sensitive data. This view does not include the (payload in
	// AppEngineHttpRequest and payload in PullMessage). These payloads are
	// desirable to return only when needed, because they can be large and because
	// of the sensitivity of the data that you choose to store in it.
	//   "FULL" - All information is returned. Authorization for FULL requires
	// `cloudtasks.tasks.fullView` [Google IAM](https://cloud.google.com/iam/)
	// permission on the Queue resource.
	ResponseView string `json:"responseView,omitempty"`
	// ScheduleTime: Required. The task's current schedule time, available in the
	// schedule_time returned by LeaseTasks response or RenewLease response. This
	// restriction is to ensure that your worker currently holds the lease.
	ScheduleTime string `json:"scheduleTime,omitempty"`
	// ForceSendFields is a list of field names (e.g. "LeaseDuration") to
	// unconditionally include in API requests. By default, fields with empty or
	// default values are omitted from API requests. See
	// https://pkg.go.dev/google.golang.org/api#hdr-ForceSendFields for more
	// details.
	ForceSendFields []string `json:"-"`
	// NullFields is a list of field names (e.g. "LeaseDuration") to include in API
	// requests with the JSON null value. By default, fields with empty values are
	// omitted from API requests. See
	// https://pkg.go.dev/google.golang.org/api#hdr-NullFields for more details.
	NullFields []string `json:"-"`
}

func (s RenewLeaseRequest) MarshalJSON() ([]byte, error) {
	type NoMethod RenewLeaseRequest
	return gensupport.MarshalJSON(NoMethod(s), s.ForceSendFields, s.NullFields)
}

// ResumeQueueRequest: Request message for ResumeQueue.
type ResumeQueueRequest struct {
}

// RetryConfig: Retry config. These settings determine how a failed task
// attempt is retried.
type RetryConfig struct {
	// MaxAttempts: The maximum number of attempts for a task. Cloud Tasks will
	// attempt the task `max_attempts` times (that is, if the first attempt fails,
	// then there will be `max_attempts - 1` retries). Must be > 0.
	MaxAttempts int64 `json:"maxAttempts,omitempty"`
	// MaxBackoff: A task will be scheduled for retry between min_backoff and
	// max_backoff duration after it fails, if the queue's RetryConfig specifies
	// that the task should be retried. If unspecified when the queue is created,
	// Cloud Tasks will pick the default. This field is output only for pull
	// queues. `max_backoff` will be truncated to the nearest second. This field
	// has the same meaning as max_backoff_seconds in queue.yaml/xml
	// (https://cloud.google.com/appengine/docs/standard/python/config/queueref#retry_parameters).
	MaxBackoff string `json:"maxBackoff,omitempty"`
	// MaxDoublings: The time between retries will double `max_doublings` times. A
	// task's retry interval starts at min_backoff, then doubles `max_doublings`
	// times, then increases linearly, and finally retries at intervals of
	// max_backoff up to max_attempts times. For example, if min_backoff is 10s,
	// max_backoff is 300s, and `max_doublings` is 3, then the a task will first be
	// retried in 10s. The retry interval will double three times, and then
	// increase linearly by 2^3 * 10s. Finally, the task will retry at intervals of
	// max_backoff until the task has been attempted max_attempts times. Thus, the
	// requests will retry at 10s, 20s, 40s, 80s, 160s, 240s, 300s, 300s, .... If
	// unspecified when the queue is created, Cloud Tasks will pick the default.
	// This field is output only for pull queues. This field has the same meaning
	// as max_doublings in queue.yaml/xml
	// (https://cloud.google.com/appengine/docs/standard/python/config/queueref#retry_parameters).
	MaxDoublings int64 `json:"maxDoublings,omitempty"`
	// MaxRetryDuration: If positive, `max_retry_duration` specifies the time limit
	// for retrying a failed task, measured from when the task was first attempted.
	// Once `max_retry_duration` time has passed *and* the task has been attempted
	// max_attempts times, no further attempts will be made and the task will be
	// deleted. If zero, then the task age is unlimited. If unspecified when the
	// queue is created, Cloud Tasks will pick the default. This field is output
	// only for pull queues. `max_retry_duration` will be truncated to the nearest
	// second. This field has the same meaning as task_age_limit in queue.yaml/xml
	// (https://cloud.google.com/appengine/docs/standard/python/config/queueref#retry_parameters).
	MaxRetryDuration string `json:"maxRetryDuration,omitempty"`
	// MinBackoff: A task will be scheduled for retry between min_backoff and
	// max_backoff duration after it fails, if the queue's RetryConfig specifies
	// that the task should be retried. If unspecified when the queue is created,
	// Cloud Tasks will pick the default. This field is output only for pull
	// queues. `min_backoff` will be truncated to the nearest second. This field
	// has the same meaning as min_backoff_seconds in queue.yaml/xml
	// (https://cloud.google.com/appengine/docs/standard/python/config/queueref#retry_parameters).
	MinBackoff string `json:"minBackoff,omitempty"`
	// UnlimitedAttempts: If true, then the number of attempts is unlimited.
	UnlimitedAttempts bool `json:"unlimitedAttempts,omitempty"`
	// ForceSendFields is a list of field names (e.g. "MaxAttempts") to
	// unconditionally include in API requests. By default, fields with empty or
	// default values are omitted from API requests. See
	// https://pkg.go.dev/google.golang.org/api#hdr-ForceSendFields for more
	// details.
	ForceSendFields []string `json:"-"`
	// NullFields is a list of field names (e.g. "MaxAttempts") to include in API
	// requests with the JSON null value. By default, fields with empty values are
	// omitted from API requests. See
	// https://pkg.go.dev/google.golang.org/api#hdr-NullFields for more details.
	NullFields []string `json:"-"`
}

func (s RetryConfig) MarshalJSON() ([]byte, error) {
	type NoMethod RetryConfig
	return gensupport.MarshalJSON(NoMethod(s), s.ForceSendFields, s.NullFields)
}

// RunTaskRequest: Request message for forcing a task to run now using RunTask.
type RunTaskRequest struct {
	// ResponseView: The response_view specifies which subset of the Task will be
	// returned. By default response_view is BASIC; not all information is
	// retrieved by default because some data, such as payloads, might be desirable
	// to return only when needed because of its large size or because of the
	// sensitivity of data that it contains. Authorization for FULL requires
	// `cloudtasks.tasks.fullView` Google IAM (https://cloud.google.com/iam/)
	// permission on the Task resource.
	//
	// Possible values:
	//   "VIEW_UNSPECIFIED" - Unspecified. Defaults to BASIC.
	//   "BASIC" - The basic view omits fields which can be large or can contain
	// sensitive data. This view does not include the (payload in
	// AppEngineHttpRequest and payload in PullMessage). These payloads are
	// desirable to return only when needed, because they can be large and because
	// of the sensitivity of the data that you choose to store in it.
	//   "FULL" - All information is returned. Authorization for FULL requires
	// `cloudtasks.tasks.fullView` [Google IAM](https://cloud.google.com/iam/)
	// permission on the Queue resource.
	ResponseView string `json:"responseView,omitempty"`
	// ForceSendFields is a list of field names (e.g. "ResponseView") to
	// unconditionally include in API requests. By default, fields with empty or
	// default values are omitted from API requests. See
	// https://pkg.go.dev/google.golang.org/api#hdr-ForceSendFields for more
	// details.
	ForceSendFields []string `json:"-"`
	// NullFields is a list of field names (e.g. "ResponseView") to include in API
	// requests with the JSON null value. By default, fields with empty values are
	// omitted from API requests. See
	// https://pkg.go.dev/google.golang.org/api#hdr-NullFields for more details.
	NullFields []string `json:"-"`
}

func (s RunTaskRequest) MarshalJSON() ([]byte, error) {
	type NoMethod RunTaskRequest
	return gensupport.MarshalJSON(NoMethod(s), s.ForceSendFields, s.NullFields)
}

// SetIamPolicyRequest: Request message for `SetIamPolicy` method.
type SetIamPolicyRequest struct {
	// Policy: REQUIRED: The complete policy to be applied to the `resource`. The
	// size of the policy is limited to a few 10s of KB. An empty policy is a valid
	// policy but certain Google Cloud services (such as Projects) might reject
	// them.
	Policy *Policy `json:"policy,omitempty"`
	// ForceSendFields is a list of field names (e.g. "Policy") to unconditionally
	// include in API requests. By default, fields with empty or default values are
	// omitted from API requests. See
	// https://pkg.go.dev/google.golang.org/api#hdr-ForceSendFields for more
	// details.
	ForceSendFields []string `json:"-"`
	// NullFields is a list of field names (e.g. "Policy") to include in API
	// requests with the JSON null value. By default, fields with empty values are
	// omitted from API requests. See
	// https://pkg.go.dev/google.golang.org/api#hdr-NullFields for more details.
	NullFields []string `json:"-"`
}

func (s SetIamPolicyRequest) MarshalJSON() ([]byte, error) {
	type NoMethod SetIamPolicyRequest
	return gensupport.MarshalJSON(NoMethod(s), s.ForceSendFields, s.NullFields)
}

// Status: The `Status` type defines a logical error model that is suitable for
// different programming environments, including REST APIs and RPC APIs. It is
// used by gRPC (https://github.com/grpc). Each `Status` message contains three
// pieces of data: error code, error message, and error details. You can find
// out more about this error model and how to work with it in the API Design
// Guide (https://cloud.google.com/apis/design/errors).
type Status struct {
	// Code: The status code, which should be an enum value of google.rpc.Code.
	Code int64 `json:"code,omitempty"`
	// Details: A list of messages that carry the error details. There is a common
	// set of message types for APIs to use.
	Details []googleapi.RawMessage `json:"details,omitempty"`
	// Message: A developer-facing error message, which should be in English. Any
	// user-facing error message should be localized and sent in the
	// google.rpc.Status.details field, or localized by the client.
	Message string `json:"message,omitempty"`
	// ForceSendFields is a list of field names (e.g. "Code") to unconditionally
	// include in API requests. By default, fields with empty or default values are
	// omitted from API requests. See
	// https://pkg.go.dev/google.golang.org/api#hdr-ForceSendFields for more
	// details.
	ForceSendFields []string `json:"-"`
	// NullFields is a list of field names (e.g. "Code") to include in API requests
	// with the JSON null value. By default, fields with empty values are omitted
	// from API requests. See
	// https://pkg.go.dev/google.golang.org/api#hdr-NullFields for more details.
	NullFields []string `json:"-"`
}

func (s Status) MarshalJSON() ([]byte, error) {
	type NoMethod Status
	return gensupport.MarshalJSON(NoMethod(s), s.ForceSendFields, s.NullFields)
}

// Task: A unit of scheduled work.
type Task struct {
	// AppEngineHttpRequest: App Engine HTTP request that is sent to the task's
	// target. Can be set only if app_engine_http_target is set on the queue. An
	// App Engine task is a task that has AppEngineHttpRequest set.
	AppEngineHttpRequest *AppEngineHttpRequest `json:"appEngineHttpRequest,omitempty"`
	// CreateTime: Output only. The time that the task was created. `create_time`
	// will be truncated to the nearest second.
	CreateTime string `json:"createTime,omitempty"`
	// HttpRequest: HTTP request that is sent to the task's target. An HTTP task is
	// a task that has HttpRequest set.
	HttpRequest *HttpRequest `json:"httpRequest,omitempty"`
	// Name: Optionally caller-specified in CreateTask. The task name. The task
	// name must have the following format:
	// `projects/PROJECT_ID/locations/LOCATION_ID/queues/QUEUE_ID/tasks/TASK_ID` *
	// `PROJECT_ID` can contain letters ([A-Za-z]), numbers ([0-9]), hyphens (-),
	// colons (:), or periods (.). For more information, see Identifying projects
	// (https://cloud.google.com/resource-manager/docs/creating-managing-projects#identifying_projects)
	// * `LOCATION_ID` is the canonical ID for the task's location. The list of
	// available locations can be obtained by calling ListLocations. For more
	// information, see https://cloud.google.com/about/locations/. * `QUEUE_ID` can
	// contain letters ([A-Za-z]), numbers ([0-9]), or hyphens (-). The maximum
	// length is 100 characters. * `TASK_ID` can contain only letters ([A-Za-z]),
	// numbers ([0-9]), hyphens (-), or underscores (_). The maximum length is 500
	// characters.
	Name string `json:"name,omitempty"`
	// PullMessage: LeaseTasks to process the task. Can be set only if pull_target
	// is set on the queue. A pull task is a task that has PullMessage set.
	PullMessage *PullMessage `json:"pullMessage,omitempty"`
	// ScheduleTime: The time when the task is scheduled to be attempted. For App
	// Engine queues, this is when the task will be attempted or retried. For pull
	// queues, this is the time when the task is available to be leased; if a task
	// is currently leased, this is the time when the current lease expires, that
	// is, the time that the task was leased plus the lease_duration.
	// `schedule_time` will be truncated to the nearest microsecond.
	ScheduleTime string `json:"scheduleTime,omitempty"`
	// Status: Output only. The task status.
	Status *TaskStatus `json:"status,omitempty"`
	// View: Output only. The view specifies which subset of the Task has been
	// returned.
	//
	// Possible values:
	//   "VIEW_UNSPECIFIED" - Unspecified. Defaults to BASIC.
	//   "BASIC" - The basic view omits fields which can be large or can contain
	// sensitive data. This view does not include the (payload in
	// AppEngineHttpRequest and payload in PullMessage). These payloads are
	// desirable to return only when needed, because they can be large and because
	// of the sensitivity of the data that you choose to store in it.
	//   "FULL" - All information is returned. Authorization for FULL requires
	// `cloudtasks.tasks.fullView` [Google IAM](https://cloud.google.com/iam/)
	// permission on the Queue resource.
	View string `json:"view,omitempty"`

	// ServerResponse contains the HTTP response code and headers from the server.
	googleapi.ServerResponse `json:"-"`
	// ForceSendFields is a list of field names (e.g. "AppEngineHttpRequest") to
	// unconditionally include in API requests. By default, fields with empty or
	// default values are omitted from API requests. See
	// https://pkg.go.dev/google.golang.org/api#hdr-ForceSendFields for more
	// details.
	ForceSendFields []string `json:"-"`
	// NullFields is a list of field names (e.g. "AppEngineHttpRequest") to include
	// in API requests with the JSON null value. By default, fields with empty
	// values are omitted from API requests. See
	// https://pkg.go.dev/google.golang.org/api#hdr-NullFields for more details.
	NullFields []string `json:"-"`
}

func (s Task) MarshalJSON() ([]byte, error) {
	type NoMethod Task
	return gensupport.MarshalJSON(NoMethod(s), s.ForceSendFields, s.NullFields)
}

// TaskStatus: Status of the task.
type TaskStatus struct {
	// AttemptDispatchCount: Output only. The number of attempts dispatched. This
	// count includes attempts which have been dispatched but haven't received a
	// response.
	AttemptDispatchCount int64 `json:"attemptDispatchCount,omitempty"`
	// AttemptResponseCount: Output only. The number of attempts which have
	// received a response. This field is not calculated for pull tasks.
	AttemptResponseCount int64 `json:"attemptResponseCount,omitempty"`
	// FirstAttemptStatus: Output only. The status of the task's first attempt.
	// Only dispatch_time will be set. The other AttemptStatus information is not
	// retained by Cloud Tasks. This field is not calculated for pull tasks.
	FirstAttemptStatus *AttemptStatus `json:"firstAttemptStatus,omitempty"`
	// LastAttemptStatus: Output only. The status of the task's last attempt. This
	// field is not calculated for pull tasks.
	LastAttemptStatus *AttemptStatus `json:"lastAttemptStatus,omitempty"`
	// ForceSendFields is a list of field names (e.g. "AttemptDispatchCount") to
	// unconditionally include in API requests. By default, fields with empty or
	// default values are omitted from API requests. See
	// https://pkg.go.dev/google.golang.org/api#hdr-ForceSendFields for more
	// details.
	ForceSendFields []string `json:"-"`
	// NullFields is a list of field names (e.g. "AttemptDispatchCount") to include
	// in API requests with the JSON null value. By default, fields with empty
	// values are omitted from API requests. See
	// https://pkg.go.dev/google.golang.org/api#hdr-NullFields for more details.
	NullFields []string `json:"-"`
}

func (s TaskStatus) MarshalJSON() ([]byte, error) {
	type NoMethod TaskStatus
	return gensupport.MarshalJSON(NoMethod(s), s.ForceSendFields, s.NullFields)
}

// TestIamPermissionsRequest: Request message for `TestIamPermissions` method.
type TestIamPermissionsRequest struct {
	// Permissions: The set of permissions to check for the `resource`. Permissions
	// with wildcards (such as `*` or `storage.*`) are not allowed. For more
	// information see IAM Overview
	// (https://cloud.google.com/iam/docs/overview#permissions).
	Permissions []string `json:"permissions,omitempty"`
	// ForceSendFields is a list of field names (e.g. "Permissions") to
	// unconditionally include in API requests. By default, fields with empty or
	// default values are omitted from API requests. See
	// https://pkg.go.dev/google.golang.org/api#hdr-ForceSendFields for more
	// details.
	ForceSendFields []string `json:"-"`
	// NullFields is a list of field names (e.g. "Permissions") to include in API
	// requests with the JSON null value. By default, fields with empty values are
	// omitted from API requests. See
	// https://pkg.go.dev/google.golang.org/api#hdr-NullFields for more details.
	NullFields []string `json:"-"`
}

func (s TestIamPermissionsRequest) MarshalJSON() ([]byte, error) {
	type NoMethod TestIamPermissionsRequest
	return gensupport.MarshalJSON(NoMethod(s), s.ForceSendFields, s.NullFields)
}

// TestIamPermissionsResponse: Response message for `TestIamPermissions`
// method.
type TestIamPermissionsResponse struct {
	// Permissions: A subset of `TestPermissionsRequest.permissions` that the
	// caller is allowed.
	Permissions []string `json:"permissions,omitempty"`

	// ServerResponse contains the HTTP response code and headers from the server.
	googleapi.ServerResponse `json:"-"`
	// ForceSendFields is a list of field names (e.g. "Permissions") to
	// unconditionally include in API requests. By default, fields with empty or
	// default values are omitted from API requests. See
	// https://pkg.go.dev/google.golang.org/api#hdr-ForceSendFields for more
	// details.
	ForceSendFields []string `json:"-"`
	// NullFields is a list of field names (e.g. "Permissions") to include in API
	// requests with the JSON null value. By default, fields with empty values are
	// omitted from API requests. See
	// https://pkg.go.dev/google.golang.org/api#hdr-NullFields for more details.
	NullFields []string `json:"-"`
}

func (s TestIamPermissionsResponse) MarshalJSON() ([]byte, error) {
	type NoMethod TestIamPermissionsResponse
	return gensupport.MarshalJSON(NoMethod(s), s.ForceSendFields, s.NullFields)
}

// UriOverride: Uri Override. When specified, all the HTTP tasks inside the
// queue will be partially or fully overridden depending on the configured
// values.
type UriOverride struct {
	// Host: Host override. When specified, replaces the host part of the task URL.
	// For example, if the task URL is "https://www.google.com," and host value is
	// set to "example.net", the overridden URI will be changed to
	// "https://example.net." Host value cannot be an empty string
	// (INVALID_ARGUMENT).
	Host string `json:"host,omitempty"`
	// PathOverride: URI path. When specified, replaces the existing path of the
	// task URL. Setting the path value to an empty string clears the URI path
	// segment.
	PathOverride *PathOverride `json:"pathOverride,omitempty"`
	// Port: Port override. When specified, replaces the port part of the task URI.
	// For instance, for a URI http://www.google.com/foo and port=123, the
	// overridden URI becomes http://www.google.com:123/foo. Note that the port
	// value must be a positive integer. Setting the port to 0 (Zero) clears the
	// URI port.
	Port int64 `json:"port,omitempty,string"`
	// QueryOverride: URI Query. When specified, replaces the query part of the
	// task URI. Setting the query value to an empty string clears the URI query
	// segment.
	QueryOverride *QueryOverride `json:"queryOverride,omitempty"`
	// Scheme: Scheme override. When specified, the task URI scheme is replaced by
	// the provided value (HTTP or HTTPS).
	//
	// Possible values:
	//   "SCHEME_UNSPECIFIED" - Scheme unspecified. Defaults to HTTPS.
	//   "HTTP" - Convert the scheme to HTTP, e.g., https://www.google.ca will
	// change to http://www.google.ca.
	//   "HTTPS" - Convert the scheme to HTTPS, e.g., http://www.google.ca will
	// change to https://www.google.ca.
	Scheme string `json:"scheme,omitempty"`
	// UriOverrideEnforceMode: URI Override Enforce Mode When specified, determines
	// the Target UriOverride mode. If not specified, it defaults to ALWAYS.
	//
	// Possible values:
	//   "URI_OVERRIDE_ENFORCE_MODE_UNSPECIFIED" - OverrideMode Unspecified.
	// Defaults to ALWAYS.
	//   "IF_NOT_EXISTS" - In the IF_NOT_EXISTS mode, queue-level configuration is
	// only applied where task-level configuration does not exist.
	//   "ALWAYS" - In the ALWAYS mode, queue-level configuration overrides all
	// task-level configuration
	UriOverrideEnforceMode string `json:"uriOverrideEnforceMode,omitempty"`
	// ForceSendFields is a list of field names (e.g. "Host") to unconditionally
	// include in API requests. By default, fields with empty or default values are
	// omitted from API requests. See
	// https://pkg.go.dev/google.golang.org/api#hdr-ForceSendFields for more
	// details.
	ForceSendFields []string `json:"-"`
	// NullFields is a list of field names (e.g. "Host") to include in API requests
	// with the JSON null value. By default, fields with empty values are omitted
	// from API requests. See
	// https://pkg.go.dev/google.golang.org/api#hdr-NullFields for more details.
	NullFields []string `json:"-"`
}

func (s UriOverride) MarshalJSON() ([]byte, error) {
	type NoMethod UriOverride
	return gensupport.MarshalJSON(NoMethod(s), s.ForceSendFields, s.NullFields)
}

type ApiQueueUpdateCall struct {
	s          *Service
	httpbody   *HttpBody
	urlParams_ gensupport.URLParams
	ctx_       context.Context
	header_    http.Header
}

// Update: Update queue list by uploading a queue.yaml file. The queue.yaml
// file is supplied in the request body as a YAML encoded string. This method
// was added to support gcloud clients versions before 322.0.0. New clients
// should use CreateQueue instead of this method.
func (r *ApiQueueService) Update(httpbody *HttpBody) *ApiQueueUpdateCall {
	c := &ApiQueueUpdateCall{s: r.s, urlParams_: make(gensupport.URLParams)}
	c.httpbody = httpbody
	return c
}

// AppId sets the optional parameter "appId": Required. The App ID is supplied
// as an HTTP parameter. Unlike internal usage of App ID, it does not include a
// region prefix. Rather, the App ID represents the Project ID against which to
// make the request.
func (c *ApiQueueUpdateCall) AppId(appId string) *ApiQueueUpdateCall {
	c.urlParams_.Set("appId", appId)
	return c
}

// Fields allows partial responses to be retrieved. See
// https://developers.google.com/gdata/docs/2.0/basics#PartialResponse for more
// details.
func (c *ApiQueueUpdateCall) Fields(s ...googleapi.Field) *ApiQueueUpdateCall {
	c.urlParams_.Set("fields", googleapi.CombineFields(s))
	return c
}

// Context sets the context to be used in this call's Do method.
func (c *ApiQueueUpdateCall) Context(ctx context.Context) *ApiQueueUpdateCall {
	c.ctx_ = ctx
	return c
}

// Header returns a http.Header that can be modified by the caller to add
// headers to the request.
func (c *ApiQueueUpdateCall) Header() http.Header {
	if c.header_ == nil {
		c.header_ = make(http.Header)
	}
	return c.header_
}

func (c *ApiQueueUpdateCall) doRequest(alt string) (*http.Response, error) {
	reqHeaders := gensupport.SetHeaders(c.s.userAgent(), "application/json", c.header_)
	var body io.Reader = nil
	body, err := googleapi.WithoutDataWrapper.JSONReader(c.httpbody)
	if err != nil {
		return nil, err
	}
	c.urlParams_.Set("alt", alt)
	c.urlParams_.Set("prettyPrint", "false")
	urls := googleapi.ResolveRelative(c.s.BasePath, "api/queue/update")
	urls += "?" + c.urlParams_.Encode()
	req, err := http.NewRequest("POST", urls, body)
	if err != nil {
		return nil, err
	}
	req.Header = reqHeaders
	return gensupport.SendRequest(c.ctx_, c.s.client, req)
}

// Do executes the "cloudtasks.api.queue.update" call.
// Any non-2xx status code is an error. Response headers are in either
// *Empty.ServerResponse.Header or (if a response was returned at all) in
// error.(*googleapi.Error).Header. Use googleapi.IsNotModified to check
// whether the returned error was because http.StatusNotModified was returned.
func (c *ApiQueueUpdateCall) Do(opts ...googleapi.CallOption) (*Empty, error) {
	gensupport.SetOptions(c.urlParams_, opts...)
	res, err := c.doRequest("json")
	if res != nil && res.StatusCode == http.StatusNotModified {
		if res.Body != nil {
			res.Body.Close()
		}
		return nil, gensupport.WrapError(&googleapi.Error{
			Code:   res.StatusCode,
			Header: res.Header,
		})
	}
	if err != nil {
		return nil, err
	}
	defer googleapi.CloseBody(res)
	if err := googleapi.CheckResponse(res); err != nil {
		return nil, gensupport.WrapError(err)
	}
	ret := &Empty{
		ServerResponse: googleapi.ServerResponse{
			Header:         res.Header,
			HTTPStatusCode: res.StatusCode,
		},
	}
	target := &ret
	if err := gensupport.DecodeResponse(target, res); err != nil {
		return nil, err
	}
	return ret, nil
}

type ProjectsLocationsGetCall struct {
	s            *Service
	name         string
	urlParams_   gensupport.URLParams
	ifNoneMatch_ string
	ctx_         context.Context
	header_      http.Header
}

// Get: Gets information about a location.
//
// - name: Resource name for the location.
func (r *ProjectsLocationsService) Get(name string) *ProjectsLocationsGetCall {
	c := &ProjectsLocationsGetCall{s: r.s, urlParams_: make(gensupport.URLParams)}
	c.name = name
	return c
}

// Fields allows partial responses to be retrieved. See
// https://developers.google.com/gdata/docs/2.0/basics#PartialResponse for more
// details.
func (c *ProjectsLocationsGetCall) Fields(s ...googleapi.Field) *ProjectsLocationsGetCall {
	c.urlParams_.Set("fields", googleapi.CombineFields(s))
	return c
}

// IfNoneMatch sets an optional parameter which makes the operation fail if the
// object's ETag matches the given value. This is useful for getting updates
// only after the object has changed since the last request.
func (c *ProjectsLocationsGetCall) IfNoneMatch(entityTag string) *ProjectsLocationsGetCall {
	c.ifNoneMatch_ = entityTag
	return c
}

// Context sets the context to be used in this call's Do method.
func (c *ProjectsLocationsGetCall) Context(ctx context.Context) *ProjectsLocationsGetCall {
	c.ctx_ = ctx
	return c
}

// Header returns a http.Header that can be modified by the caller to add
// headers to the request.
func (c *ProjectsLocationsGetCall) Header() http.Header {
	if c.header_ == nil {
		c.header_ = make(http.Header)
	}
	return c.header_
}

func (c *ProjectsLocationsGetCall) doRequest(alt string) (*http.Response, error) {
	reqHeaders := gensupport.SetHeaders(c.s.userAgent(), "", c.header_)
	if c.ifNoneMatch_ != "" {
		reqHeaders.Set("If-None-Match", c.ifNoneMatch_)
	}
	var body io.Reader = nil
	c.urlParams_.Set("alt", alt)
	c.urlParams_.Set("prettyPrint", "false")
	urls := googleapi.ResolveRelative(c.s.BasePath, "v2beta2/{+name}")
	urls += "?" + c.urlParams_.Encode()
	req, err := http.NewRequest("GET", urls, body)
	if err != nil {
		return nil, err
	}
	req.Header = reqHeaders
	googleapi.Expand(req.URL, map[string]string{
		"name": c.name,
	})
	return gensupport.SendRequest(c.ctx_, c.s.client, req)
}

// Do executes the "cloudtasks.projects.locations.get" call.
// Any non-2xx status code is an error. Response headers are in either
// *Location.ServerResponse.Header or (if a response was returned at all) in
// error.(*googleapi.Error).Header. Use googleapi.IsNotModified to check
// whether the returned error was because http.StatusNotModified was returned.
func (c *ProjectsLocationsGetCall) Do(opts ...googleapi.CallOption) (*Location, error) {
	gensupport.SetOptions(c.urlParams_, opts...)
	res, err := c.doRequest("json")
	if res != nil && res.StatusCode == http.StatusNotModified {
		if res.Body != nil {
			res.Body.Close()
		}
		return nil, gensupport.WrapError(&googleapi.Error{
			Code:   res.StatusCode,
			Header: res.Header,
		})
	}
	if err != nil {
		return nil, err
	}
	defer googleapi.CloseBody(res)
	if err := googleapi.CheckResponse(res); err != nil {
		return nil, gensupport.WrapError(err)
	}
	ret := &Location{
		ServerResponse: googleapi.ServerResponse{
			Header:         res.Header,
			HTTPStatusCode: res.StatusCode,
		},
	}
	target := &ret
	if err := gensupport.DecodeResponse(target, res); err != nil {
		return nil, err
	}
	return ret, nil
}

type ProjectsLocationsGetCmekConfigCall struct {
	s            *Service
	name         string
	urlParams_   gensupport.URLParams
	ifNoneMatch_ string
	ctx_         context.Context
	header_      http.Header
}

// GetCmekConfig: Gets the CMEK config. Gets the Customer Managed Encryption
// Key configured with the Cloud Tasks lcoation. By default there is no kms_key
// configured.
//
//   - name: The config. For example:
//     projects/PROJECT_ID/locations/LOCATION_ID/CmekConfig`.
func (r *ProjectsLocationsService) GetCmekConfig(name string) *ProjectsLocationsGetCmekConfigCall {
	c := &ProjectsLocationsGetCmekConfigCall{s: r.s, urlParams_: make(gensupport.URLParams)}
	c.name = name
	return c
}

// Fields allows partial responses to be retrieved. See
// https://developers.google.com/gdata/docs/2.0/basics#PartialResponse for more
// details.
func (c *ProjectsLocationsGetCmekConfigCall) Fields(s ...googleapi.Field) *ProjectsLocationsGetCmekConfigCall {
	c.urlParams_.Set("fields", googleapi.CombineFields(s))
	return c
}

// IfNoneMatch sets an optional parameter which makes the operation fail if the
// object's ETag matches the given value. This is useful for getting updates
// only after the object has changed since the last request.
func (c *ProjectsLocationsGetCmekConfigCall) IfNoneMatch(entityTag string) *ProjectsLocationsGetCmekConfigCall {
	c.ifNoneMatch_ = entityTag
	return c
}

// Context sets the context to be used in this call's Do method.
func (c *ProjectsLocationsGetCmekConfigCall) Context(ctx context.Context) *ProjectsLocationsGetCmekConfigCall {
	c.ctx_ = ctx
	return c
}

// Header returns a http.Header that can be modified by the caller to add
// headers to the request.
func (c *ProjectsLocationsGetCmekConfigCall) Header() http.Header {
	if c.header_ == nil {
		c.header_ = make(http.Header)
	}
	return c.header_
}

func (c *ProjectsLocationsGetCmekConfigCall) doRequest(alt string) (*http.Response, error) {
	reqHeaders := gensupport.SetHeaders(c.s.userAgent(), "", c.header_)
	if c.ifNoneMatch_ != "" {
		reqHeaders.Set("If-None-Match", c.ifNoneMatch_)
	}
	var body io.Reader = nil
	c.urlParams_.Set("alt", alt)
	c.urlParams_.Set("prettyPrint", "false")
	urls := googleapi.ResolveRelative(c.s.BasePath, "v2beta2/{+name}")
	urls += "?" + c.urlParams_.Encode()
	req, err := http.NewRequest("GET", urls, body)
	if err != nil {
		return nil, err
	}
	req.Header = reqHeaders
	googleapi.Expand(req.URL, map[string]string{
		"name": c.name,
	})
	return gensupport.SendRequest(c.ctx_, c.s.client, req)
}

// Do executes the "cloudtasks.projects.locations.getCmekConfig" call.
// Any non-2xx status code is an error. Response headers are in either
// *CmekConfig.ServerResponse.Header or (if a response was returned at all) in
// error.(*googleapi.Error).Header. Use googleapi.IsNotModified to check
// whether the returned error was because http.StatusNotModified was returned.
func (c *ProjectsLocationsGetCmekConfigCall) Do(opts ...googleapi.CallOption) (*CmekConfig, error) {
	gensupport.SetOptions(c.urlParams_, opts...)
	res, err := c.doRequest("json")
	if res != nil && res.StatusCode == http.StatusNotModified {
		if res.Body != nil {
			res.Body.Close()
		}
		return nil, gensupport.WrapError(&googleapi.Error{
			Code:   res.StatusCode,
			Header: res.Header,
		})
	}
	if err != nil {
		return nil, err
	}
	defer googleapi.CloseBody(res)
	if err := googleapi.CheckResponse(res); err != nil {
		return nil, gensupport.WrapError(err)
	}
	ret := &CmekConfig{
		ServerResponse: googleapi.ServerResponse{
			Header:         res.Header,
			HTTPStatusCode: res.StatusCode,
		},
	}
	target := &ret
	if err := gensupport.DecodeResponse(target, res); err != nil {
		return nil, err
	}
	return ret, nil
}

type ProjectsLocationsListCall struct {
	s            *Service
	name         string
	urlParams_   gensupport.URLParams
	ifNoneMatch_ string
	ctx_         context.Context
	header_      http.Header
}

// List: Lists information about the supported locations for this service.
//
// - name: The resource that owns the locations collection, if applicable.
func (r *ProjectsLocationsService) List(name string) *ProjectsLocationsListCall {
	c := &ProjectsLocationsListCall{s: r.s, urlParams_: make(gensupport.URLParams)}
	c.name = name
	return c
}

// Filter sets the optional parameter "filter": A filter to narrow down results
// to a preferred subset. The filtering language accepts strings like
// "displayName=tokyo", and is documented in more detail in AIP-160
// (https://google.aip.dev/160).
func (c *ProjectsLocationsListCall) Filter(filter string) *ProjectsLocationsListCall {
	c.urlParams_.Set("filter", filter)
	return c
}

// PageSize sets the optional parameter "pageSize": The maximum number of
// results to return. If not set, the service selects a default.
func (c *ProjectsLocationsListCall) PageSize(pageSize int64) *ProjectsLocationsListCall {
	c.urlParams_.Set("pageSize", fmt.Sprint(pageSize))
	return c
}

// PageToken sets the optional parameter "pageToken": A page token received
// from the `next_page_token` field in the response. Send that page token to
// receive the subsequent page.
func (c *ProjectsLocationsListCall) PageToken(pageToken string) *ProjectsLocationsListCall {
	c.urlParams_.Set("pageToken", pageToken)
	return c
}

// Fields allows partial responses to be retrieved. See
// https://developers.google.com/gdata/docs/2.0/basics#PartialResponse for more
// details.
func (c *ProjectsLocationsListCall) Fields(s ...googleapi.Field) *ProjectsLocationsListCall {
	c.urlParams_.Set("fields", googleapi.CombineFields(s))
	return c
}

// IfNoneMatch sets an optional parameter which makes the operation fail if the
// object's ETag matches the given value. This is useful for getting updates
// only after the object has changed since the last request.
func (c *ProjectsLocationsListCall) IfNoneMatch(entityTag string) *ProjectsLocationsListCall {
	c.ifNoneMatch_ = entityTag
	return c
}

// Context sets the context to be used in this call's Do method.
func (c *ProjectsLocationsListCall) Context(ctx context.Context) *ProjectsLocationsListCall {
	c.ctx_ = ctx
	return c
}

// Header returns a http.Header that can be modified by the caller to add
// headers to the request.
func (c *ProjectsLocationsListCall) Header() http.Header {
	if c.header_ == nil {
		c.header_ = make(http.Header)
	}
	return c.header_
}

func (c *ProjectsLocationsListCall) doRequest(alt string) (*http.Response, error) {
	reqHeaders := gensupport.SetHeaders(c.s.userAgent(), "", c.header_)
	if c.ifNoneMatch_ != "" {
		reqHeaders.Set("If-None-Match", c.ifNoneMatch_)
	}
	var body io.Reader = nil
	c.urlParams_.Set("alt", alt)
	c.urlParams_.Set("prettyPrint", "false")
	urls := googleapi.ResolveRelative(c.s.BasePath, "v2beta2/{+name}/locations")
	urls += "?" + c.urlParams_.Encode()
	req, err := http.NewRequest("GET", urls, body)
	if err != nil {
		return nil, err
	}
	req.Header = reqHeaders
	googleapi.Expand(req.URL, map[string]string{
		"name": c.name,
	})
	return gensupport.SendRequest(c.ctx_, c.s.client, req)
}

// Do executes the "cloudtasks.projects.locations.list" call.
// Any non-2xx status code is an error. Response headers are in either
// *ListLocationsResponse.ServerResponse.Header or (if a response was returned
// at all) in error.(*googleapi.Error).Header. Use googleapi.IsNotModified to
// check whether the returned error was because http.StatusNotModified was
// returned.
func (c *ProjectsLocationsListCall) Do(opts ...googleapi.CallOption) (*ListLocationsResponse, error) {
	gensupport.SetOptions(c.urlParams_, opts...)
	res, err := c.doRequest("json")
	if res != nil && res.StatusCode == http.StatusNotModified {
		if res.Body != nil {
			res.Body.Close()
		}
		return nil, gensupport.WrapError(&googleapi.Error{
			Code:   res.StatusCode,
			Header: res.Header,
		})
	}
	if err != nil {
		return nil, err
	}
	defer googleapi.CloseBody(res)
	if err := googleapi.CheckResponse(res); err != nil {
		return nil, gensupport.WrapError(err)
	}
	ret := &ListLocationsResponse{
		ServerResponse: googleapi.ServerResponse{
			Header:         res.Header,
			HTTPStatusCode: res.StatusCode,
		},
	}
	target := &ret
	if err := gensupport.DecodeResponse(target, res); err != nil {
		return nil, err
	}
	return ret, nil
}

// Pages invokes f for each page of results.
// A non-nil error returned from f will halt the iteration.
// The provided context supersedes any context provided to the Context method.
func (c *ProjectsLocationsListCall) Pages(ctx context.Context, f func(*ListLocationsResponse) error) error {
	c.ctx_ = ctx
	defer c.PageToken(c.urlParams_.Get("pageToken"))
	for {
		x, err := c.Do()
		if err != nil {
			return err
		}
		if err := f(x); err != nil {
			return err
		}
		if x.NextPageToken == "" {
			return nil
		}
		c.PageToken(x.NextPageToken)
	}
}

type ProjectsLocationsUpdateCmekConfigCall struct {
	s          *Service
	name       string
	cmekconfig *CmekConfig
	urlParams_ gensupport.URLParams
	ctx_       context.Context
	header_    http.Header
}

// UpdateCmekConfig: Creates or Updates a CMEK config. Updates the Customer
// Managed Encryption Key assotiated with the Cloud Tasks location (Creates if
// the key does not already exist). All new tasks created in the location will
// be encrypted at-rest with the KMS-key provided in the config.
//
//   - name: Output only. The config resource name which includes the project and
//     location and must end in 'cmekConfig', in the format
//     projects/PROJECT_ID/locations/LOCATION_ID/cmekConfig`.
func (r *ProjectsLocationsService) UpdateCmekConfig(name string, cmekconfig *CmekConfig) *ProjectsLocationsUpdateCmekConfigCall {
	c := &ProjectsLocationsUpdateCmekConfigCall{s: r.s, urlParams_: make(gensupport.URLParams)}
	c.name = name
	c.cmekconfig = cmekconfig
	return c
}

// UpdateMask sets the optional parameter "updateMask": List of fields to be
// updated in this request.
func (c *ProjectsLocationsUpdateCmekConfigCall) UpdateMask(updateMask string) *ProjectsLocationsUpdateCmekConfigCall {
	c.urlParams_.Set("updateMask", updateMask)
	return c
}

// Fields allows partial responses to be retrieved. See
// https://developers.google.com/gdata/docs/2.0/basics#PartialResponse for more
// details.
func (c *ProjectsLocationsUpdateCmekConfigCall) Fields(s ...googleapi.Field) *ProjectsLocationsUpdateCmekConfigCall {
	c.urlParams_.Set("fields", googleapi.CombineFields(s))
	return c
}

// Context sets the context to be used in this call's Do method.
func (c *ProjectsLocationsUpdateCmekConfigCall) Context(ctx context.Context) *ProjectsLocationsUpdateCmekConfigCall {
	c.ctx_ = ctx
	return c
}

// Header returns a http.Header that can be modified by the caller to add
// headers to the request.
func (c *ProjectsLocationsUpdateCmekConfigCall) Header() http.Header {
	if c.header_ == nil {
		c.header_ = make(http.Header)
	}
	return c.header_
}

func (c *ProjectsLocationsUpdateCmekConfigCall) doRequest(alt string) (*http.Response, error) {
	reqHeaders := gensupport.SetHeaders(c.s.userAgent(), "application/json", c.header_)
	var body io.Reader = nil
	body, err := googleapi.WithoutDataWrapper.JSONReader(c.cmekconfig)
	if err != nil {
		return nil, err
	}
	c.urlParams_.Set("alt", alt)
	c.urlParams_.Set("prettyPrint", "false")
	urls := googleapi.ResolveRelative(c.s.BasePath, "v2beta2/{+name}")
	urls += "?" + c.urlParams_.Encode()
	req, err := http.NewRequest("PATCH", urls, body)
	if err != nil {
		return nil, err
	}
	req.Header = reqHeaders
	googleapi.Expand(req.URL, map[string]string{
		"name": c.name,
	})
	return gensupport.SendRequest(c.ctx_, c.s.client, req)
}

// Do executes the "cloudtasks.projects.locations.updateCmekConfig" call.
// Any non-2xx status code is an error. Response headers are in either
// *CmekConfig.ServerResponse.Header or (if a response was returned at all) in
// error.(*googleapi.Error).Header. Use googleapi.IsNotModified to check
// whether the returned error was because http.StatusNotModified was returned.
func (c *ProjectsLocationsUpdateCmekConfigCall) Do(opts ...googleapi.CallOption) (*CmekConfig, error) {
	gensupport.SetOptions(c.urlParams_, opts...)
	res, err := c.doRequest("json")
	if res != nil && res.StatusCode == http.StatusNotModified {
		if res.Body != nil {
			res.Body.Close()
		}
		return nil, gensupport.WrapError(&googleapi.Error{
			Code:   res.StatusCode,
			Header: res.Header,
		})
	}
	if err != nil {
		return nil, err
	}
	defer googleapi.CloseBody(res)
	if err := googleapi.CheckResponse(res); err != nil {
		return nil, gensupport.WrapError(err)
	}
	ret := &CmekConfig{
		ServerResponse: googleapi.ServerResponse{
			Header:         res.Header,
			HTTPStatusCode: res.StatusCode,
		},
	}
	target := &ret
	if err := gensupport.DecodeResponse(target, res); err != nil {
		return nil, err
	}
	return ret, nil
}

type ProjectsLocationsQueuesCreateCall struct {
	s          *Service
	parent     string
	queue      *Queue
	urlParams_ gensupport.URLParams
	ctx_       context.Context
	header_    http.Header
}

// Create: Creates a queue. Queues created with this method allow tasks to live
// for a maximum of 31 days. After a task is 31 days old, the task will be
// deleted regardless of whether it was dispatched or not. WARNING: Using this
// method may have unintended side effects if you are using an App Engine
// `queue.yaml` or `queue.xml` file to manage your queues. Read Overview of
// Queue Management and queue.yaml
// (https://cloud.google.com/tasks/docs/queue-yaml) before using this method.
//
//   - parent: The location name in which the queue will be created. For example:
//     `projects/PROJECT_ID/locations/LOCATION_ID` The list of allowed locations
//     can be obtained by calling Cloud Tasks' implementation of ListLocations.
func (r *ProjectsLocationsQueuesService) Create(parent string, queue *Queue) *ProjectsLocationsQueuesCreateCall {
	c := &ProjectsLocationsQueuesCreateCall{s: r.s, urlParams_: make(gensupport.URLParams)}
	c.parent = parent
	c.queue = queue
	return c
}

// Fields allows partial responses to be retrieved. See
// https://developers.google.com/gdata/docs/2.0/basics#PartialResponse for more
// details.
func (c *ProjectsLocationsQueuesCreateCall) Fields(s ...googleapi.Field) *ProjectsLocationsQueuesCreateCall {
	c.urlParams_.Set("fields", googleapi.CombineFields(s))
	return c
}

// Context sets the context to be used in this call's Do method.
func (c *ProjectsLocationsQueuesCreateCall) Context(ctx context.Context) *ProjectsLocationsQueuesCreateCall {
	c.ctx_ = ctx
	return c
}

// Header returns a http.Header that can be modified by the caller to add
// headers to the request.
func (c *ProjectsLocationsQueuesCreateCall) Header() http.Header {
	if c.header_ == nil {
		c.header_ = make(http.Header)
	}
	return c.header_
}

func (c *ProjectsLocationsQueuesCreateCall) doRequest(alt string) (*http.Response, error) {
	reqHeaders := gensupport.SetHeaders(c.s.userAgent(), "application/json", c.header_)
	var body io.Reader = nil
	body, err := googleapi.WithoutDataWrapper.JSONReader(c.queue)
	if err != nil {
		return nil, err
	}
	c.urlParams_.Set("alt", alt)
	c.urlParams_.Set("prettyPrint", "false")
	urls := googleapi.ResolveRelative(c.s.BasePath, "v2beta2/{+parent}/queues")
	urls += "?" + c.urlParams_.Encode()
	req, err := http.NewRequest("POST", urls, body)
	if err != nil {
		return nil, err
	}
	req.Header = reqHeaders
	googleapi.Expand(req.URL, map[string]string{
		"parent": c.parent,
	})
	return gensupport.SendRequest(c.ctx_, c.s.client, req)
}

// Do executes the "cloudtasks.projects.locations.queues.create" call.
// Any non-2xx status code is an error. Response headers are in either
// *Queue.ServerResponse.Header or (if a response was returned at all) in
// error.(*googleapi.Error).Header. Use googleapi.IsNotModified to check
// whether the returned error was because http.StatusNotModified was returned.
func (c *ProjectsLocationsQueuesCreateCall) Do(opts ...googleapi.CallOption) (*Queue, error) {
	gensupport.SetOptions(c.urlParams_, opts...)
	res, err := c.doRequest("json")
	if res != nil && res.StatusCode == http.StatusNotModified {
		if res.Body != nil {
			res.Body.Close()
		}
		return nil, gensupport.WrapError(&googleapi.Error{
			Code:   res.StatusCode,
			Header: res.Header,
		})
	}
	if err != nil {
		return nil, err
	}
	defer googleapi.CloseBody(res)
	if err := googleapi.CheckResponse(res); err != nil {
		return nil, gensupport.WrapError(err)
	}
	ret := &Queue{
		ServerResponse: googleapi.ServerResponse{
			Header:         res.Header,
			HTTPStatusCode: res.StatusCode,
		},
	}
	target := &ret
	if err := gensupport.DecodeResponse(target, res); err != nil {
		return nil, err
	}
	return ret, nil
}

type ProjectsLocationsQueuesDeleteCall struct {
	s          *Service
	name       string
	urlParams_ gensupport.URLParams
	ctx_       context.Context
	header_    http.Header
}

// Delete: Deletes a queue. This command will delete the queue even if it has
// tasks in it. Note: If you delete a queue, you may be prevented from creating
// a new queue with the same name as the deleted queue for a tombstone window
// of up to 3 days. During this window, the CreateQueue operation may appear to
// recreate the queue, but this can be misleading. If you attempt to create a
// queue with the same name as one that is in the tombstone window, run
// GetQueue to confirm that the queue creation was successful. If GetQueue
// returns 200 response code, your queue was successfully created with the name
// of the previously deleted queue. Otherwise, your queue did not successfully
// recreate. WARNING: Using this method may have unintended side effects if you
// are using an App Engine `queue.yaml` or `queue.xml` file to manage your
// queues. Read Overview of Queue Management and queue.yaml
// (https://cloud.google.com/tasks/docs/queue-yaml) before using this method.
//
//   - name: The queue name. For example:
//     `projects/PROJECT_ID/locations/LOCATION_ID/queues/QUEUE_ID`.
func (r *ProjectsLocationsQueuesService) Delete(name string) *ProjectsLocationsQueuesDeleteCall {
	c := &ProjectsLocationsQueuesDeleteCall{s: r.s, urlParams_: make(gensupport.URLParams)}
	c.name = name
	return c
}

// Fields allows partial responses to be retrieved. See
// https://developers.google.com/gdata/docs/2.0/basics#PartialResponse for more
// details.
func (c *ProjectsLocationsQueuesDeleteCall) Fields(s ...googleapi.Field) *ProjectsLocationsQueuesDeleteCall {
	c.urlParams_.Set("fields", googleapi.CombineFields(s))
	return c
}

// Context sets the context to be used in this call's Do method.
func (c *ProjectsLocationsQueuesDeleteCall) Context(ctx context.Context) *ProjectsLocationsQueuesDeleteCall {
	c.ctx_ = ctx
	return c
}

// Header returns a http.Header that can be modified by the caller to add
// headers to the request.
func (c *ProjectsLocationsQueuesDeleteCall) Header() http.Header {
	if c.header_ == nil {
		c.header_ = make(http.Header)
	}
	return c.header_
}

func (c *ProjectsLocationsQueuesDeleteCall) doRequest(alt string) (*http.Response, error) {
	reqHeaders := gensupport.SetHeaders(c.s.userAgent(), "", c.header_)
	var body io.Reader = nil
	c.urlParams_.Set("alt", alt)
	c.urlParams_.Set("prettyPrint", "false")
	urls := googleapi.ResolveRelative(c.s.BasePath, "v2beta2/{+name}")
	urls += "?" + c.urlParams_.Encode()
	req, err := http.NewRequest("DELETE", urls, body)
	if err != nil {
		return nil, err
	}
	req.Header = reqHeaders
	googleapi.Expand(req.URL, map[string]string{
		"name": c.name,
	})
	return gensupport.SendRequest(c.ctx_, c.s.client, req)
}

// Do executes the "cloudtasks.projects.locations.queues.delete" call.
// Any non-2xx status code is an error. Response headers are in either
// *Empty.ServerResponse.Header or (if a response was returned at all) in
// error.(*googleapi.Error).Header. Use googleapi.IsNotModified to check
// whether the returned error was because http.StatusNotModified was returned.
func (c *ProjectsLocationsQueuesDeleteCall) Do(opts ...googleapi.CallOption) (*Empty, error) {
	gensupport.SetOptions(c.urlParams_, opts...)
	res, err := c.doRequest("json")
	if res != nil && res.StatusCode == http.StatusNotModified {
		if res.Body != nil {
			res.Body.Close()
		}
		return nil, gensupport.WrapError(&googleapi.Error{
			Code:   res.StatusCode,
			Header: res.Header,
		})
	}
	if err != nil {
		return nil, err
	}
	defer googleapi.CloseBody(res)
	if err := googleapi.CheckResponse(res); err != nil {
		return nil, gensupport.WrapError(err)
	}
	ret := &Empty{
		ServerResponse: googleapi.ServerResponse{
			Header:         res.Header,
			HTTPStatusCode: res.StatusCode,
		},
	}
	target := &ret
	if err := gensupport.DecodeResponse(target, res); err != nil {
		return nil, err
	}
	return ret, nil
}

type ProjectsLocationsQueuesGetCall struct {
	s            *Service
	name         string
	urlParams_   gensupport.URLParams
	ifNoneMatch_ string
	ctx_         context.Context
	header_      http.Header
}

// Get: Gets a queue.
//
//   - name: The resource name of the queue. For example:
//     `projects/PROJECT_ID/locations/LOCATION_ID/queues/QUEUE_ID`.
func (r *ProjectsLocationsQueuesService) Get(name string) *ProjectsLocationsQueuesGetCall {
	c := &ProjectsLocationsQueuesGetCall{s: r.s, urlParams_: make(gensupport.URLParams)}
	c.name = name
	return c
}

// ReadMask sets the optional parameter "readMask": Read mask is used for a
// more granular control over what the API returns. If the mask is not present
// all fields will be returned except [Queue.stats]. [Queue.stats] will be
// returned only if it was explicitly specified in the mask.
func (c *ProjectsLocationsQueuesGetCall) ReadMask(readMask string) *ProjectsLocationsQueuesGetCall {
	c.urlParams_.Set("readMask", readMask)
	return c
}

// Fields allows partial responses to be retrieved. See
// https://developers.google.com/gdata/docs/2.0/basics#PartialResponse for more
// details.
func (c *ProjectsLocationsQueuesGetCall) Fields(s ...googleapi.Field) *ProjectsLocationsQueuesGetCall {
	c.urlParams_.Set("fields", googleapi.CombineFields(s))
	return c
}

// IfNoneMatch sets an optional parameter which makes the operation fail if the
// object's ETag matches the given value. This is useful for getting updates
// only after the object has changed since the last request.
func (c *ProjectsLocationsQueuesGetCall) IfNoneMatch(entityTag string) *ProjectsLocationsQueuesGetCall {
	c.ifNoneMatch_ = entityTag
	return c
}

// Context sets the context to be used in this call's Do method.
func (c *ProjectsLocationsQueuesGetCall) Context(ctx context.Context) *ProjectsLocationsQueuesGetCall {
	c.ctx_ = ctx
	return c
}

// Header returns a http.Header that can be modified by the caller to add
// headers to the request.
func (c *ProjectsLocationsQueuesGetCall) Header() http.Header {
	if c.header_ == nil {
		c.header_ = make(http.Header)
	}
	return c.header_
}

func (c *ProjectsLocationsQueuesGetCall) doRequest(alt string) (*http.Response, error) {
	reqHeaders := gensupport.SetHeaders(c.s.userAgent(), "", c.header_)
	if c.ifNoneMatch_ != "" {
		reqHeaders.Set("If-None-Match", c.ifNoneMatch_)
	}
	var body io.Reader = nil
	c.urlParams_.Set("alt", alt)
	c.urlParams_.Set("prettyPrint", "false")
	urls := googleapi.ResolveRelative(c.s.BasePath, "v2beta2/{+name}")
	urls += "?" + c.urlParams_.Encode()
	req, err := http.NewRequest("GET", urls, body)
	if err != nil {
		return nil, err
	}
	req.Header = reqHeaders
	googleapi.Expand(req.URL, map[string]string{
		"name": c.name,
	})
	return gensupport.SendRequest(c.ctx_, c.s.client, req)
}

// Do executes the "cloudtasks.projects.locations.queues.get" call.
// Any non-2xx status code is an error. Response headers are in either
// *Queue.ServerResponse.Header or (if a response was returned at all) in
// error.(*googleapi.Error).Header. Use googleapi.IsNotModified to check
// whether the returned error was because http.StatusNotModified was returned.
func (c *ProjectsLocationsQueuesGetCall) Do(opts ...googleapi.CallOption) (*Queue, error) {
	gensupport.SetOptions(c.urlParams_, opts...)
	res, err := c.doRequest("json")
	if res != nil && res.StatusCode == http.StatusNotModified {
		if res.Body != nil {
			res.Body.Close()
		}
		return nil, gensupport.WrapError(&googleapi.Error{
			Code:   res.StatusCode,
			Header: res.Header,
		})
	}
	if err != nil {
		return nil, err
	}
	defer googleapi.CloseBody(res)
	if err := googleapi.CheckResponse(res); err != nil {
		return nil, gensupport.WrapError(err)
	}
	ret := &Queue{
		ServerResponse: googleapi.ServerResponse{
			Header:         res.Header,
			HTTPStatusCode: res.StatusCode,
		},
	}
	target := &ret
	if err := gensupport.DecodeResponse(target, res); err != nil {
		return nil, err
	}
	return ret, nil
}

type ProjectsLocationsQueuesGetIamPolicyCall struct {
	s                   *Service
	resource            string
	getiampolicyrequest *GetIamPolicyRequest
	urlParams_          gensupport.URLParams
	ctx_                context.Context
	header_             http.Header
}

// GetIamPolicy: Gets the access control policy for a Queue. Returns an empty
// policy if the resource exists and does not have a policy set. Authorization
// requires the following Google IAM (https://cloud.google.com/iam) permission
// on the specified resource parent: * `cloudtasks.queues.getIamPolicy`
//
//   - resource: REQUIRED: The resource for which the policy is being requested.
//     See Resource names (https://cloud.google.com/apis/design/resource_names)
//     for the appropriate value for this field.
func (r *ProjectsLocationsQueuesService) GetIamPolicy(resource string, getiampolicyrequest *GetIamPolicyRequest) *ProjectsLocationsQueuesGetIamPolicyCall {
	c := &ProjectsLocationsQueuesGetIamPolicyCall{s: r.s, urlParams_: make(gensupport.URLParams)}
	c.resource = resource
	c.getiampolicyrequest = getiampolicyrequest
	return c
}

// Fields allows partial responses to be retrieved. See
// https://developers.google.com/gdata/docs/2.0/basics#PartialResponse for more
// details.
func (c *ProjectsLocationsQueuesGetIamPolicyCall) Fields(s ...googleapi.Field) *ProjectsLocationsQueuesGetIamPolicyCall {
	c.urlParams_.Set("fields", googleapi.CombineFields(s))
	return c
}

// Context sets the context to be used in this call's Do method.
func (c *ProjectsLocationsQueuesGetIamPolicyCall) Context(ctx context.Context) *ProjectsLocationsQueuesGetIamPolicyCall {
	c.ctx_ = ctx
	return c
}

// Header returns a http.Header that can be modified by the caller to add
// headers to the request.
func (c *ProjectsLocationsQueuesGetIamPolicyCall) Header() http.Header {
	if c.header_ == nil {
		c.header_ = make(http.Header)
	}
	return c.header_
}

func (c *ProjectsLocationsQueuesGetIamPolicyCall) doRequest(alt string) (*http.Response, error) {
	reqHeaders := gensupport.SetHeaders(c.s.userAgent(), "application/json", c.header_)
	var body io.Reader = nil
	body, err := googleapi.WithoutDataWrapper.JSONReader(c.getiampolicyrequest)
	if err != nil {
		return nil, err
	}
	c.urlParams_.Set("alt", alt)
	c.urlParams_.Set("prettyPrint", "false")
	urls := googleapi.ResolveRelative(c.s.BasePath, "v2beta2/{+resource}:getIamPolicy")
	urls += "?" + c.urlParams_.Encode()
	req, err := http.NewRequest("POST", urls, body)
	if err != nil {
		return nil, err
	}
	req.Header = reqHeaders
	googleapi.Expand(req.URL, map[string]string{
		"resource": c.resource,
	})
	return gensupport.SendRequest(c.ctx_, c.s.client, req)
}

// Do executes the "cloudtasks.projects.locations.queues.getIamPolicy" call.
// Any non-2xx status code is an error. Response headers are in either
// *Policy.ServerResponse.Header or (if a response was returned at all) in
// error.(*googleapi.Error).Header. Use googleapi.IsNotModified to check
// whether the returned error was because http.StatusNotModified was returned.
func (c *ProjectsLocationsQueuesGetIamPolicyCall) Do(opts ...googleapi.CallOption) (*Policy, error) {
	gensupport.SetOptions(c.urlParams_, opts...)
	res, err := c.doRequest("json")
	if res != nil && res.StatusCode == http.StatusNotModified {
		if res.Body != nil {
			res.Body.Close()
		}
		return nil, gensupport.WrapError(&googleapi.Error{
			Code:   res.StatusCode,
			Header: res.Header,
		})
	}
	if err != nil {
		return nil, err
	}
	defer googleapi.CloseBody(res)
	if err := googleapi.CheckResponse(res); err != nil {
		return nil, gensupport.WrapError(err)
	}
	ret := &Policy{
		ServerResponse: googleapi.ServerResponse{
			Header:         res.Header,
			HTTPStatusCode: res.StatusCode,
		},
	}
	target := &ret
	if err := gensupport.DecodeResponse(target, res); err != nil {
		return nil, err
	}
	return ret, nil
}

type ProjectsLocationsQueuesListCall struct {
	s            *Service
	parent       string
	urlParams_   gensupport.URLParams
	ifNoneMatch_ string
	ctx_         context.Context
	header_      http.Header
}

// List: Lists queues. Queues are returned in lexicographical order.
//
//   - parent: The location name. For example:
//     `projects/PROJECT_ID/locations/LOCATION_ID`.
func (r *ProjectsLocationsQueuesService) List(parent string) *ProjectsLocationsQueuesListCall {
	c := &ProjectsLocationsQueuesListCall{s: r.s, urlParams_: make(gensupport.URLParams)}
	c.parent = parent
	return c
}

// Filter sets the optional parameter "filter": `filter` can be used to specify
// a subset of queues. Any Queue field can be used as a filter and several
// operators as supported. For example: `<=, <, >=, >, !=, =, :`. The filter
// syntax is the same as described in Stackdriver's Advanced Logs Filters
// (https://cloud.google.com/logging/docs/view/advanced_filters). Sample filter
// "app_engine_http_target: *". Note that using filters might cause fewer
// queues than the requested_page size to be returned.
func (c *ProjectsLocationsQueuesListCall) Filter(filter string) *ProjectsLocationsQueuesListCall {
	c.urlParams_.Set("filter", filter)
	return c
}

// PageSize sets the optional parameter "pageSize": Requested page size. The
// maximum page size is 9800. If unspecified, the page size will be the
// maximum. Fewer queues than requested might be returned, even if more queues
// exist; use the next_page_token in the response to determine if more queues
// exist.
func (c *ProjectsLocationsQueuesListCall) PageSize(pageSize int64) *ProjectsLocationsQueuesListCall {
	c.urlParams_.Set("pageSize", fmt.Sprint(pageSize))
	return c
}

// PageToken sets the optional parameter "pageToken": A token identifying the
// page of results to return. To request the first page results, page_token
// must be empty. To request the next page of results, page_token must be the
// value of next_page_token returned from the previous call to ListQueues
// method. It is an error to switch the value of the filter while iterating
// through pages.
func (c *ProjectsLocationsQueuesListCall) PageToken(pageToken string) *ProjectsLocationsQueuesListCall {
	c.urlParams_.Set("pageToken", pageToken)
	return c
}

// ReadMask sets the optional parameter "readMask": Read mask is used for a
// more granular control over what the API returns. If the mask is not present
// all fields will be returned except [Queue.stats]. [Queue.stats] will be
// returned only if it was explicitly specified in the mask.
func (c *ProjectsLocationsQueuesListCall) ReadMask(readMask string) *ProjectsLocationsQueuesListCall {
	c.urlParams_.Set("readMask", readMask)
	return c
}

// Fields allows partial responses to be retrieved. See
// https://developers.google.com/gdata/docs/2.0/basics#PartialResponse for more
// details.
func (c *ProjectsLocationsQueuesListCall) Fields(s ...googleapi.Field) *ProjectsLocationsQueuesListCall {
	c.urlParams_.Set("fields", googleapi.CombineFields(s))
	return c
}

// IfNoneMatch sets an optional parameter which makes the operation fail if the
// object's ETag matches the given value. This is useful for getting updates
// only after the object has changed since the last request.
func (c *ProjectsLocationsQueuesListCall) IfNoneMatch(entityTag string) *ProjectsLocationsQueuesListCall {
	c.ifNoneMatch_ = entityTag
	return c
}

// Context sets the context to be used in this call's Do method.
func (c *ProjectsLocationsQueuesListCall) Context(ctx context.Context) *ProjectsLocationsQueuesListCall {
	c.ctx_ = ctx
	return c
}

// Header returns a http.Header that can be modified by the caller to add
// headers to the request.
func (c *ProjectsLocationsQueuesListCall) Header() http.Header {
	if c.header_ == nil {
		c.header_ = make(http.Header)
	}
	return c.header_
}

func (c *ProjectsLocationsQueuesListCall) doRequest(alt string) (*http.Response, error) {
	reqHeaders := gensupport.SetHeaders(c.s.userAgent(), "", c.header_)
	if c.ifNoneMatch_ != "" {
		reqHeaders.Set("If-None-Match", c.ifNoneMatch_)
	}
	var body io.Reader = nil
	c.urlParams_.Set("alt", alt)
	c.urlParams_.Set("prettyPrint", "false")
	urls := googleapi.ResolveRelative(c.s.BasePath, "v2beta2/{+parent}/queues")
	urls += "?" + c.urlParams_.Encode()
	req, err := http.NewRequest("GET", urls, body)
	if err != nil {
		return nil, err
	}
	req.Header = reqHeaders
	googleapi.Expand(req.URL, map[string]string{
		"parent": c.parent,
	})
	return gensupport.SendRequest(c.ctx_, c.s.client, req)
}

// Do executes the "cloudtasks.projects.locations.queues.list" call.
// Any non-2xx status code is an error. Response headers are in either
// *ListQueuesResponse.ServerResponse.Header or (if a response was returned at
// all) in error.(*googleapi.Error).Header. Use googleapi.IsNotModified to
// check whether the returned error was because http.StatusNotModified was
// returned.
func (c *ProjectsLocationsQueuesListCall) Do(opts ...googleapi.CallOption) (*ListQueuesResponse, error) {
	gensupport.SetOptions(c.urlParams_, opts...)
	res, err := c.doRequest("json")
	if res != nil && res.StatusCode == http.StatusNotModified {
		if res.Body != nil {
			res.Body.Close()
		}
		return nil, gensupport.WrapError(&googleapi.Error{
			Code:   res.StatusCode,
			Header: res.Header,
		})
	}
	if err != nil {
		return nil, err
	}
	defer googleapi.CloseBody(res)
	if err := googleapi.CheckResponse(res); err != nil {
		return nil, gensupport.WrapError(err)
	}
	ret := &ListQueuesResponse{
		ServerResponse: googleapi.ServerResponse{
			Header:         res.Header,
			HTTPStatusCode: res.StatusCode,
		},
	}
	target := &ret
	if err := gensupport.DecodeResponse(target, res); err != nil {
		return nil, err
	}
	return ret, nil
}

// Pages invokes f for each page of results.
// A non-nil error returned from f will halt the iteration.
// The provided context supersedes any context provided to the Context method.
func (c *ProjectsLocationsQueuesListCall) Pages(ctx context.Context, f func(*ListQueuesResponse) error) error {
	c.ctx_ = ctx
	defer c.PageToken(c.urlParams_.Get("pageToken"))
	for {
		x, err := c.Do()
		if err != nil {
			return err
		}
		if err := f(x); err != nil {
			return err
		}
		if x.NextPageToken == "" {
			return nil
		}
		c.PageToken(x.NextPageToken)
	}
}

type ProjectsLocationsQueuesPatchCall struct {
	s          *Service
	name       string
	queue      *Queue
	urlParams_ gensupport.URLParams
	ctx_       context.Context
	header_    http.Header
}

// Patch: Updates a queue. This method creates the queue if it does not exist
// and updates the queue if it does exist. Queues created with this method
// allow tasks to live for a maximum of 31 days. After a task is 31 days old,
// the task will be deleted regardless of whether it was dispatched or not.
// WARNING: Using this method may have unintended side effects if you are using
// an App Engine `queue.yaml` or `queue.xml` file to manage your queues. Read
// Overview of Queue Management and queue.yaml
// (https://cloud.google.com/tasks/docs/queue-yaml) before using this method.
//
//   - name: Caller-specified and required in CreateQueue, after which it becomes
//     output only. The queue name. The queue name must have the following
//     format: `projects/PROJECT_ID/locations/LOCATION_ID/queues/QUEUE_ID` *
//     `PROJECT_ID` can contain letters ([A-Za-z]), numbers ([0-9]), hyphens (-),
//     colons (:), or periods (.). For more information, see Identifying projects
//     (https://cloud.google.com/resource-manager/docs/creating-managing-projects#identifying_projects)
//   - `LOCATION_ID` is the canonical ID for the queue's location. The list of
//     available locations can be obtained by calling ListLocations. For more
//     information, see https://cloud.google.com/about/locations/. * `QUEUE_ID`
//     can contain letters ([A-Za-z]), numbers ([0-9]), or hyphens (-). The
//     maximum length is 100 characters.
func (r *ProjectsLocationsQueuesService) Patch(name string, queue *Queue) *ProjectsLocationsQueuesPatchCall {
	c := &ProjectsLocationsQueuesPatchCall{s: r.s, urlParams_: make(gensupport.URLParams)}
	c.name = name
	c.queue = queue
	return c
}

// UpdateMask sets the optional parameter "updateMask": A mask used to specify
// which fields of the queue are being updated. If empty, then all fields will
// be updated.
func (c *ProjectsLocationsQueuesPatchCall) UpdateMask(updateMask string) *ProjectsLocationsQueuesPatchCall {
	c.urlParams_.Set("updateMask", updateMask)
	return c
}

// Fields allows partial responses to be retrieved. See
// https://developers.google.com/gdata/docs/2.0/basics#PartialResponse for more
// details.
func (c *ProjectsLocationsQueuesPatchCall) Fields(s ...googleapi.Field) *ProjectsLocationsQueuesPatchCall {
	c.urlParams_.Set("fields", googleapi.CombineFields(s))
	return c
}

// Context sets the context to be used in this call's Do method.
func (c *ProjectsLocationsQueuesPatchCall) Context(ctx context.Context) *ProjectsLocationsQueuesPatchCall {
	c.ctx_ = ctx
	return c
}

// Header returns a http.Header that can be modified by the caller to add
// headers to the request.
func (c *ProjectsLocationsQueuesPatchCall) Header() http.Header {
	if c.header_ == nil {
		c.header_ = make(http.Header)
	}
	return c.header_
}

func (c *ProjectsLocationsQueuesPatchCall) doRequest(alt string) (*http.Response, error) {
	reqHeaders := gensupport.SetHeaders(c.s.userAgent(), "application/json", c.header_)
	var body io.Reader = nil
	body, err := googleapi.WithoutDataWrapper.JSONReader(c.queue)
	if err != nil {
		return nil, err
	}
	c.urlParams_.Set("alt", alt)
	c.urlParams_.Set("prettyPrint", "false")
	urls := googleapi.ResolveRelative(c.s.BasePath, "v2beta2/{+name}")
	urls += "?" + c.urlParams_.Encode()
	req, err := http.NewRequest("PATCH", urls, body)
	if err != nil {
		return nil, err
	}
	req.Header = reqHeaders
	googleapi.Expand(req.URL, map[string]string{
		"name": c.name,
	})
	return gensupport.SendRequest(c.ctx_, c.s.client, req)
}

// Do executes the "cloudtasks.projects.locations.queues.patch" call.
// Any non-2xx status code is an error. Response headers are in either
// *Queue.ServerResponse.Header or (if a response was returned at all) in
// error.(*googleapi.Error).Header. Use googleapi.IsNotModified to check
// whether the returned error was because http.StatusNotModified was returned.
func (c *ProjectsLocationsQueuesPatchCall) Do(opts ...googleapi.CallOption) (*Queue, error) {
	gensupport.SetOptions(c.urlParams_, opts...)
	res, err := c.doRequest("json")
	if res != nil && res.StatusCode == http.StatusNotModified {
		if res.Body != nil {
			res.Body.Close()
		}
		return nil, gensupport.WrapError(&googleapi.Error{
			Code:   res.StatusCode,
			Header: res.Header,
		})
	}
	if err != nil {
		return nil, err
	}
	defer googleapi.CloseBody(res)
	if err := googleapi.CheckResponse(res); err != nil {
		return nil, gensupport.WrapError(err)
	}
	ret := &Queue{
		ServerResponse: googleapi.ServerResponse{
			Header:         res.Header,
			HTTPStatusCode: res.StatusCode,
		},
	}
	target := &ret
	if err := gensupport.DecodeResponse(target, res); err != nil {
		return nil, err
	}
	return ret, nil
}

type ProjectsLocationsQueuesPauseCall struct {
	s                 *Service
	name              string
	pausequeuerequest *PauseQueueRequest
	urlParams_        gensupport.URLParams
	ctx_              context.Context
	header_           http.Header
}

// Pause: Pauses the queue. If a queue is paused then the system will stop
// dispatching tasks until the queue is resumed via ResumeQueue. Tasks can
// still be added when the queue is paused. A queue is paused if its state is
// PAUSED.
//
//   - name: The queue name. For example:
//     `projects/PROJECT_ID/location/LOCATION_ID/queues/QUEUE_ID`.
func (r *ProjectsLocationsQueuesService) Pause(name string, pausequeuerequest *PauseQueueRequest) *ProjectsLocationsQueuesPauseCall {
	c := &ProjectsLocationsQueuesPauseCall{s: r.s, urlParams_: make(gensupport.URLParams)}
	c.name = name
	c.pausequeuerequest = pausequeuerequest
	return c
}

// Fields allows partial responses to be retrieved. See
// https://developers.google.com/gdata/docs/2.0/basics#PartialResponse for more
// details.
func (c *ProjectsLocationsQueuesPauseCall) Fields(s ...googleapi.Field) *ProjectsLocationsQueuesPauseCall {
	c.urlParams_.Set("fields", googleapi.CombineFields(s))
	return c
}

// Context sets the context to be used in this call's Do method.
func (c *ProjectsLocationsQueuesPauseCall) Context(ctx context.Context) *ProjectsLocationsQueuesPauseCall {
	c.ctx_ = ctx
	return c
}

// Header returns a http.Header that can be modified by the caller to add
// headers to the request.
func (c *ProjectsLocationsQueuesPauseCall) Header() http.Header {
	if c.header_ == nil {
		c.header_ = make(http.Header)
	}
	return c.header_
}

func (c *ProjectsLocationsQueuesPauseCall) doRequest(alt string) (*http.Response, error) {
	reqHeaders := gensupport.SetHeaders(c.s.userAgent(), "application/json", c.header_)
	var body io.Reader = nil
	body, err := googleapi.WithoutDataWrapper.JSONReader(c.pausequeuerequest)
	if err != nil {
		return nil, err
	}
	c.urlParams_.Set("alt", alt)
	c.urlParams_.Set("prettyPrint", "false")
	urls := googleapi.ResolveRelative(c.s.BasePath, "v2beta2/{+name}:pause")
	urls += "?" + c.urlParams_.Encode()
	req, err := http.NewRequest("POST", urls, body)
	if err != nil {
		return nil, err
	}
	req.Header = reqHeaders
	googleapi.Expand(req.URL, map[string]string{
		"name": c.name,
	})
	return gensupport.SendRequest(c.ctx_, c.s.client, req)
}

// Do executes the "cloudtasks.projects.locations.queues.pause" call.
// Any non-2xx status code is an error. Response headers are in either
// *Queue.ServerResponse.Header or (if a response was returned at all) in
// error.(*googleapi.Error).Header. Use googleapi.IsNotModified to check
// whether the returned error was because http.StatusNotModified was returned.
func (c *ProjectsLocationsQueuesPauseCall) Do(opts ...googleapi.CallOption) (*Queue, error) {
	gensupport.SetOptions(c.urlParams_, opts...)
	res, err := c.doRequest("json")
	if res != nil && res.StatusCode == http.StatusNotModified {
		if res.Body != nil {
			res.Body.Close()
		}
		return nil, gensupport.WrapError(&googleapi.Error{
			Code:   res.StatusCode,
			Header: res.Header,
		})
	}
	if err != nil {
		return nil, err
	}
	defer googleapi.CloseBody(res)
	if err := googleapi.CheckResponse(res); err != nil {
		return nil, gensupport.WrapError(err)
	}
	ret := &Queue{
		ServerResponse: googleapi.ServerResponse{
			Header:         res.Header,
			HTTPStatusCode: res.StatusCode,
		},
	}
	target := &ret
	if err := gensupport.DecodeResponse(target, res); err != nil {
		return nil, err
	}
	return ret, nil
}

type ProjectsLocationsQueuesPurgeCall struct {
	s                 *Service
	name              string
	purgequeuerequest *PurgeQueueRequest
	urlParams_        gensupport.URLParams
	ctx_              context.Context
	header_           http.Header
}

// Purge: Purges a queue by deleting all of its tasks. All tasks created before
// this method is called are permanently deleted. Purge operations can take up
// to one minute to take effect. Tasks might be dispatched before the purge
// takes effect. A purge is irreversible.
//
//   - name: The queue name. For example:
//     `projects/PROJECT_ID/location/LOCATION_ID/queues/QUEUE_ID`.
func (r *ProjectsLocationsQueuesService) Purge(name string, purgequeuerequest *PurgeQueueRequest) *ProjectsLocationsQueuesPurgeCall {
	c := &ProjectsLocationsQueuesPurgeCall{s: r.s, urlParams_: make(gensupport.URLParams)}
	c.name = name
	c.purgequeuerequest = purgequeuerequest
	return c
}

// Fields allows partial responses to be retrieved. See
// https://developers.google.com/gdata/docs/2.0/basics#PartialResponse for more
// details.
func (c *ProjectsLocationsQueuesPurgeCall) Fields(s ...googleapi.Field) *ProjectsLocationsQueuesPurgeCall {
	c.urlParams_.Set("fields", googleapi.CombineFields(s))
	return c
}

// Context sets the context to be used in this call's Do method.
func (c *ProjectsLocationsQueuesPurgeCall) Context(ctx context.Context) *ProjectsLocationsQueuesPurgeCall {
	c.ctx_ = ctx
	return c
}

// Header returns a http.Header that can be modified by the caller to add
// headers to the request.
func (c *ProjectsLocationsQueuesPurgeCall) Header() http.Header {
	if c.header_ == nil {
		c.header_ = make(http.Header)
	}
	return c.header_
}

func (c *ProjectsLocationsQueuesPurgeCall) doRequest(alt string) (*http.Response, error) {
	reqHeaders := gensupport.SetHeaders(c.s.userAgent(), "application/json", c.header_)
	var body io.Reader = nil
	body, err := googleapi.WithoutDataWrapper.JSONReader(c.purgequeuerequest)
	if err != nil {
		return nil, err
	}
	c.urlParams_.Set("alt", alt)
	c.urlParams_.Set("prettyPrint", "false")
	urls := googleapi.ResolveRelative(c.s.BasePath, "v2beta2/{+name}:purge")
	urls += "?" + c.urlParams_.Encode()
	req, err := http.NewRequest("POST", urls, body)
	if err != nil {
		return nil, err
	}
	req.Header = reqHeaders
	googleapi.Expand(req.URL, map[string]string{
		"name": c.name,
	})
	return gensupport.SendRequest(c.ctx_, c.s.client, req)
}

// Do executes the "cloudtasks.projects.locations.queues.purge" call.
// Any non-2xx status code is an error. Response headers are in either
// *Queue.ServerResponse.Header or (if a response was returned at all) in
// error.(*googleapi.Error).Header. Use googleapi.IsNotModified to check
// whether the returned error was because http.StatusNotModified was returned.
func (c *ProjectsLocationsQueuesPurgeCall) Do(opts ...googleapi.CallOption) (*Queue, error) {
	gensupport.SetOptions(c.urlParams_, opts...)
	res, err := c.doRequest("json")
	if res != nil && res.StatusCode == http.StatusNotModified {
		if res.Body != nil {
			res.Body.Close()
		}
		return nil, gensupport.WrapError(&googleapi.Error{
			Code:   res.StatusCode,
			Header: res.Header,
		})
	}
	if err != nil {
		return nil, err
	}
	defer googleapi.CloseBody(res)
	if err := googleapi.CheckResponse(res); err != nil {
		return nil, gensupport.WrapError(err)
	}
	ret := &Queue{
		ServerResponse: googleapi.ServerResponse{
			Header:         res.Header,
			HTTPStatusCode: res.StatusCode,
		},
	}
	target := &ret
	if err := gensupport.DecodeResponse(target, res); err != nil {
		return nil, err
	}
	return ret, nil
}

type ProjectsLocationsQueuesResumeCall struct {
	s                  *Service
	name               string
	resumequeuerequest *ResumeQueueRequest
	urlParams_         gensupport.URLParams
	ctx_               context.Context
	header_            http.Header
}

// Resume: Resume a queue. This method resumes a queue after it has been PAUSED
// or DISABLED. The state of a queue is stored in the queue's state; after
// calling this method it will be set to RUNNING. WARNING: Resuming many
// high-QPS queues at the same time can lead to target overloading. If you are
// resuming high-QPS queues, follow the 500/50/5 pattern described in Managing
// Cloud Tasks Scaling Risks
// (https://cloud.google.com/tasks/docs/manage-cloud-task-scaling).
//
//   - name: The queue name. For example:
//     `projects/PROJECT_ID/location/LOCATION_ID/queues/QUEUE_ID`.
func (r *ProjectsLocationsQueuesService) Resume(name string, resumequeuerequest *ResumeQueueRequest) *ProjectsLocationsQueuesResumeCall {
	c := &ProjectsLocationsQueuesResumeCall{s: r.s, urlParams_: make(gensupport.URLParams)}
	c.name = name
	c.resumequeuerequest = resumequeuerequest
	return c
}

// Fields allows partial responses to be retrieved. See
// https://developers.google.com/gdata/docs/2.0/basics#PartialResponse for more
// details.
func (c *ProjectsLocationsQueuesResumeCall) Fields(s ...googleapi.Field) *ProjectsLocationsQueuesResumeCall {
	c.urlParams_.Set("fields", googleapi.CombineFields(s))
	return c
}

// Context sets the context to be used in this call's Do method.
func (c *ProjectsLocationsQueuesResumeCall) Context(ctx context.Context) *ProjectsLocationsQueuesResumeCall {
	c.ctx_ = ctx
	return c
}

// Header returns a http.Header that can be modified by the caller to add
// headers to the request.
func (c *ProjectsLocationsQueuesResumeCall) Header() http.Header {
	if c.header_ == nil {
		c.header_ = make(http.Header)
	}
	return c.header_
}

func (c *ProjectsLocationsQueuesResumeCall) doRequest(alt string) (*http.Response, error) {
	reqHeaders := gensupport.SetHeaders(c.s.userAgent(), "application/json", c.header_)
	var body io.Reader = nil
	body, err := googleapi.WithoutDataWrapper.JSONReader(c.resumequeuerequest)
	if err != nil {
		return nil, err
	}
	c.urlParams_.Set("alt", alt)
	c.urlParams_.Set("prettyPrint", "false")
	urls := googleapi.ResolveRelative(c.s.BasePath, "v2beta2/{+name}:resume")
	urls += "?" + c.urlParams_.Encode()
	req, err := http.NewRequest("POST", urls, body)
	if err != nil {
		return nil, err
	}
	req.Header = reqHeaders
	googleapi.Expand(req.URL, map[string]string{
		"name": c.name,
	})
	return gensupport.SendRequest(c.ctx_, c.s.client, req)
}

// Do executes the "cloudtasks.projects.locations.queues.resume" call.
// Any non-2xx status code is an error. Response headers are in either
// *Queue.ServerResponse.Header or (if a response was returned at all) in
// error.(*googleapi.Error).Header. Use googleapi.IsNotModified to check
// whether the returned error was because http.StatusNotModified was returned.
func (c *ProjectsLocationsQueuesResumeCall) Do(opts ...googleapi.CallOption) (*Queue, error) {
	gensupport.SetOptions(c.urlParams_, opts...)
	res, err := c.doRequest("json")
	if res != nil && res.StatusCode == http.StatusNotModified {
		if res.Body != nil {
			res.Body.Close()
		}
		return nil, gensupport.WrapError(&googleapi.Error{
			Code:   res.StatusCode,
			Header: res.Header,
		})
	}
	if err != nil {
		return nil, err
	}
	defer googleapi.CloseBody(res)
	if err := googleapi.CheckResponse(res); err != nil {
		return nil, gensupport.WrapError(err)
	}
	ret := &Queue{
		ServerResponse: googleapi.ServerResponse{
			Header:         res.Header,
			HTTPStatusCode: res.StatusCode,
		},
	}
	target := &ret
	if err := gensupport.DecodeResponse(target, res); err != nil {
		return nil, err
	}
	return ret, nil
}

type ProjectsLocationsQueuesSetIamPolicyCall struct {
	s                   *Service
	resource            string
	setiampolicyrequest *SetIamPolicyRequest
	urlParams_          gensupport.URLParams
	ctx_                context.Context
	header_             http.Header
}

// SetIamPolicy: Sets the access control policy for a Queue. Replaces any
// existing policy. Note: The Cloud Console does not check queue-level IAM
// permissions yet. Project-level permissions are required to use the Cloud
// Console. Authorization requires the following Google IAM
// (https://cloud.google.com/iam) permission on the specified resource parent:
// * `cloudtasks.queues.setIamPolicy`
//
//   - resource: REQUIRED: The resource for which the policy is being specified.
//     See Resource names (https://cloud.google.com/apis/design/resource_names)
//     for the appropriate value for this field.
func (r *ProjectsLocationsQueuesService) SetIamPolicy(resource string, setiampolicyrequest *SetIamPolicyRequest) *ProjectsLocationsQueuesSetIamPolicyCall {
	c := &ProjectsLocationsQueuesSetIamPolicyCall{s: r.s, urlParams_: make(gensupport.URLParams)}
	c.resource = resource
	c.setiampolicyrequest = setiampolicyrequest
	return c
}

// Fields allows partial responses to be retrieved. See
// https://developers.google.com/gdata/docs/2.0/basics#PartialResponse for more
// details.
func (c *ProjectsLocationsQueuesSetIamPolicyCall) Fields(s ...googleapi.Field) *ProjectsLocationsQueuesSetIamPolicyCall {
	c.urlParams_.Set("fields", googleapi.CombineFields(s))
	return c
}

// Context sets the context to be used in this call's Do method.
func (c *ProjectsLocationsQueuesSetIamPolicyCall) Context(ctx context.Context) *ProjectsLocationsQueuesSetIamPolicyCall {
	c.ctx_ = ctx
	return c
}

// Header returns a http.Header that can be modified by the caller to add
// headers to the request.
func (c *ProjectsLocationsQueuesSetIamPolicyCall) Header() http.Header {
	if c.header_ == nil {
		c.header_ = make(http.Header)
	}
	return c.header_
}

func (c *ProjectsLocationsQueuesSetIamPolicyCall) doRequest(alt string) (*http.Response, error) {
	reqHeaders := gensupport.SetHeaders(c.s.userAgent(), "application/json", c.header_)
	var body io.Reader = nil
	body, err := googleapi.WithoutDataWrapper.JSONReader(c.setiampolicyrequest)
	if err != nil {
		return nil, err
	}
	c.urlParams_.Set("alt", alt)
	c.urlParams_.Set("prettyPrint", "false")
	urls := googleapi.ResolveRelative(c.s.BasePath, "v2beta2/{+resource}:setIamPolicy")
	urls += "?" + c.urlParams_.Encode()
	req, err := http.NewRequest("POST", urls, body)
	if err != nil {
		return nil, err
	}
	req.Header = reqHeaders
	googleapi.Expand(req.URL, map[string]string{
		"resource": c.resource,
	})
	return gensupport.SendRequest(c.ctx_, c.s.client, req)
}

// Do executes the "cloudtasks.projects.locations.queues.setIamPolicy" call.
// Any non-2xx status code is an error. Response headers are in either
// *Policy.ServerResponse.Header or (if a response was returned at all) in
// error.(*googleapi.Error).Header. Use googleapi.IsNotModified to check
// whether the returned error was because http.StatusNotModified was returned.
func (c *ProjectsLocationsQueuesSetIamPolicyCall) Do(opts ...googleapi.CallOption) (*Policy, error) {
	gensupport.SetOptions(c.urlParams_, opts...)
	res, err := c.doRequest("json")
	if res != nil && res.StatusCode == http.StatusNotModified {
		if res.Body != nil {
			res.Body.Close()
		}
		return nil, gensupport.WrapError(&googleapi.Error{
			Code:   res.StatusCode,
			Header: res.Header,
		})
	}
	if err != nil {
		return nil, err
	}
	defer googleapi.CloseBody(res)
	if err := googleapi.CheckResponse(res); err != nil {
		return nil, gensupport.WrapError(err)
	}
	ret := &Policy{
		ServerResponse: googleapi.ServerResponse{
			Header:         res.Header,
			HTTPStatusCode: res.StatusCode,
		},
	}
	target := &ret
	if err := gensupport.DecodeResponse(target, res); err != nil {
		return nil, err
	}
	return ret, nil
}

type ProjectsLocationsQueuesTestIamPermissionsCall struct {
	s                         *Service
	resource                  string
	testiampermissionsrequest *TestIamPermissionsRequest
	urlParams_                gensupport.URLParams
	ctx_                      context.Context
	header_                   http.Header
}

// TestIamPermissions: Returns permissions that a caller has on a Queue. If the
// resource does not exist, this will return an empty set of permissions, not a
// NOT_FOUND error. Note: This operation is designed to be used for building
// permission-aware UIs and command-line tools, not for authorization checking.
// This operation may "fail open" without warning.
//
//   - resource: REQUIRED: The resource for which the policy detail is being
//     requested. See Resource names
//     (https://cloud.google.com/apis/design/resource_names) for the appropriate
//     value for this field.
func (r *ProjectsLocationsQueuesService) TestIamPermissions(resource string, testiampermissionsrequest *TestIamPermissionsRequest) *ProjectsLocationsQueuesTestIamPermissionsCall {
	c := &ProjectsLocationsQueuesTestIamPermissionsCall{s: r.s, urlParams_: make(gensupport.URLParams)}
	c.resource = resource
	c.testiampermissionsrequest = testiampermissionsrequest
	return c
}

// Fields allows partial responses to be retrieved. See
// https://developers.google.com/gdata/docs/2.0/basics#PartialResponse for more
// details.
func (c *ProjectsLocationsQueuesTestIamPermissionsCall) Fields(s ...googleapi.Field) *ProjectsLocationsQueuesTestIamPermissionsCall {
	c.urlParams_.Set("fields", googleapi.CombineFields(s))
	return c
}

// Context sets the context to be used in this call's Do method.
func (c *ProjectsLocationsQueuesTestIamPermissionsCall) Context(ctx context.Context) *ProjectsLocationsQueuesTestIamPermissionsCall {
	c.ctx_ = ctx
	return c
}

// Header returns a http.Header that can be modified by the caller to add
// headers to the request.
func (c *ProjectsLocationsQueuesTestIamPermissionsCall) Header() http.Header {
	if c.header_ == nil {
		c.header_ = make(http.Header)
	}
	return c.header_
}

func (c *ProjectsLocationsQueuesTestIamPermissionsCall) doRequest(alt string) (*http.Response, error) {
	reqHeaders := gensupport.SetHeaders(c.s.userAgent(), "application/json", c.header_)
	var body io.Reader = nil
	body, err := googleapi.WithoutDataWrapper.JSONReader(c.testiampermissionsrequest)
	if err != nil {
		return nil, err
	}
	c.urlParams_.Set("alt", alt)
	c.urlParams_.Set("prettyPrint", "false")
	urls := googleapi.ResolveRelative(c.s.BasePath, "v2beta2/{+resource}:testIamPermissions")
	urls += "?" + c.urlParams_.Encode()
	req, err := http.NewRequest("POST", urls, body)
	if err != nil {
		return nil, err
	}
	req.Header = reqHeaders
	googleapi.Expand(req.URL, map[string]string{
		"resource": c.resource,
	})
	return gensupport.SendRequest(c.ctx_, c.s.client, req)
}

// Do executes the "cloudtasks.projects.locations.queues.testIamPermissions" call.
// Any non-2xx status code is an error. Response headers are in either
// *TestIamPermissionsResponse.ServerResponse.Header or (if a response was
// returned at all) in error.(*googleapi.Error).Header. Use
// googleapi.IsNotModified to check whether the returned error was because
// http.StatusNotModified was returned.
func (c *ProjectsLocationsQueuesTestIamPermissionsCall) Do(opts ...googleapi.CallOption) (*TestIamPermissionsResponse, error) {
	gensupport.SetOptions(c.urlParams_, opts...)
	res, err := c.doRequest("json")
	if res != nil && res.StatusCode == http.StatusNotModified {
		if res.Body != nil {
			res.Body.Close()
		}
		return nil, gensupport.WrapError(&googleapi.Error{
			Code:   res.StatusCode,
			Header: res.Header,
		})
	}
	if err != nil {
		return nil, err
	}
	defer googleapi.CloseBody(res)
	if err := googleapi.CheckResponse(res); err != nil {
		return nil, gensupport.WrapError(err)
	}
	ret := &TestIamPermissionsResponse{
		ServerResponse: googleapi.ServerResponse{
			Header:         res.Header,
			HTTPStatusCode: res.StatusCode,
		},
	}
	target := &ret
	if err := gensupport.DecodeResponse(target, res); err != nil {
		return nil, err
	}
	return ret, nil
}

type ProjectsLocationsQueuesTasksAcknowledgeCall struct {
	s                      *Service
	name                   string
	acknowledgetaskrequest *AcknowledgeTaskRequest
	urlParams_             gensupport.URLParams
	ctx_                   context.Context
	header_                http.Header
}

// Acknowledge: Acknowledges a pull task. The worker, that is, the entity that
// leased this task must call this method to indicate that the work associated
// with the task has finished. The worker must acknowledge a task within the
// lease_duration or the lease will expire and the task will become available
// to be leased again. After the task is acknowledged, it will not be returned
// by a later LeaseTasks, GetTask, or ListTasks.
//
//   - name: The task name. For example:
//     `projects/PROJECT_ID/locations/LOCATION_ID/queues/QUEUE_ID/tasks/TASK_ID`.
func (r *ProjectsLocationsQueuesTasksService) Acknowledge(name string, acknowledgetaskrequest *AcknowledgeTaskRequest) *ProjectsLocationsQueuesTasksAcknowledgeCall {
	c := &ProjectsLocationsQueuesTasksAcknowledgeCall{s: r.s, urlParams_: make(gensupport.URLParams)}
	c.name = name
	c.acknowledgetaskrequest = acknowledgetaskrequest
	return c
}

// Fields allows partial responses to be retrieved. See
// https://developers.google.com/gdata/docs/2.0/basics#PartialResponse for more
// details.
func (c *ProjectsLocationsQueuesTasksAcknowledgeCall) Fields(s ...googleapi.Field) *ProjectsLocationsQueuesTasksAcknowledgeCall {
	c.urlParams_.Set("fields", googleapi.CombineFields(s))
	return c
}

// Context sets the context to be used in this call's Do method.
func (c *ProjectsLocationsQueuesTasksAcknowledgeCall) Context(ctx context.Context) *ProjectsLocationsQueuesTasksAcknowledgeCall {
	c.ctx_ = ctx
	return c
}

// Header returns a http.Header that can be modified by the caller to add
// headers to the request.
func (c *ProjectsLocationsQueuesTasksAcknowledgeCall) Header() http.Header {
	if c.header_ == nil {
		c.header_ = make(http.Header)
	}
	return c.header_
}

func (c *ProjectsLocationsQueuesTasksAcknowledgeCall) doRequest(alt string) (*http.Response, error) {
	reqHeaders := gensupport.SetHeaders(c.s.userAgent(), "application/json", c.header_)
	var body io.Reader = nil
	body, err := googleapi.WithoutDataWrapper.JSONReader(c.acknowledgetaskrequest)
	if err != nil {
		return nil, err
	}
	c.urlParams_.Set("alt", alt)
	c.urlParams_.Set("prettyPrint", "false")
	urls := googleapi.ResolveRelative(c.s.BasePath, "v2beta2/{+name}:acknowledge")
	urls += "?" + c.urlParams_.Encode()
	req, err := http.NewRequest("POST", urls, body)
	if err != nil {
		return nil, err
	}
	req.Header = reqHeaders
	googleapi.Expand(req.URL, map[string]string{
		"name": c.name,
	})
	return gensupport.SendRequest(c.ctx_, c.s.client, req)
}

// Do executes the "cloudtasks.projects.locations.queues.tasks.acknowledge" call.
// Any non-2xx status code is an error. Response headers are in either
// *Empty.ServerResponse.Header or (if a response was returned at all) in
// error.(*googleapi.Error).Header. Use googleapi.IsNotModified to check
// whether the returned error was because http.StatusNotModified was returned.
func (c *ProjectsLocationsQueuesTasksAcknowledgeCall) Do(opts ...googleapi.CallOption) (*Empty, error) {
	gensupport.SetOptions(c.urlParams_, opts...)
	res, err := c.doRequest("json")
	if res != nil && res.StatusCode == http.StatusNotModified {
		if res.Body != nil {
			res.Body.Close()
		}
		return nil, gensupport.WrapError(&googleapi.Error{
			Code:   res.StatusCode,
			Header: res.Header,
		})
	}
	if err != nil {
		return nil, err
	}
	defer googleapi.CloseBody(res)
	if err := googleapi.CheckResponse(res); err != nil {
		return nil, gensupport.WrapError(err)
	}
	ret := &Empty{
		ServerResponse: googleapi.ServerResponse{
			Header:         res.Header,
			HTTPStatusCode: res.StatusCode,
		},
	}
	target := &ret
	if err := gensupport.DecodeResponse(target, res); err != nil {
		return nil, err
	}
	return ret, nil
}

type ProjectsLocationsQueuesTasksBufferCall struct {
	s                 *Service
	queue             string
	taskId            string
	buffertaskrequest *BufferTaskRequest
	urlParams_        gensupport.URLParams
	ctx_              context.Context
	header_           http.Header
}

// Buffer: Creates and buffers a new task without the need to explicitly define
// a Task message. The queue must have HTTP target. To create the task with a
// custom ID, use the following format and set TASK_ID to your desired ID:
// projects/PROJECT_ID/locations/LOCATION_ID/queues/QUEUE_ID/tasks/TASK_ID:buffe
// r To create the task with an automatically generated ID, use the following
// format:
// projects/PROJECT_ID/locations/LOCATION_ID/queues/QUEUE_ID/tasks:buffer.
//
//   - queue: The parent queue name. For example:
//     projects/PROJECT_ID/locations/LOCATION_ID/queues/QUEUE_ID` The queue must
//     already exist.
//   - taskId: Optional. Task ID for the task being created. If not provided, a
//     random task ID is assigned to the task.
func (r *ProjectsLocationsQueuesTasksService) Buffer(queue string, taskId string, buffertaskrequest *BufferTaskRequest) *ProjectsLocationsQueuesTasksBufferCall {
	c := &ProjectsLocationsQueuesTasksBufferCall{s: r.s, urlParams_: make(gensupport.URLParams)}
	c.queue = queue
	c.taskId = taskId
	c.buffertaskrequest = buffertaskrequest
	return c
}

// Fields allows partial responses to be retrieved. See
// https://developers.google.com/gdata/docs/2.0/basics#PartialResponse for more
// details.
func (c *ProjectsLocationsQueuesTasksBufferCall) Fields(s ...googleapi.Field) *ProjectsLocationsQueuesTasksBufferCall {
	c.urlParams_.Set("fields", googleapi.CombineFields(s))
	return c
}

// Context sets the context to be used in this call's Do method.
func (c *ProjectsLocationsQueuesTasksBufferCall) Context(ctx context.Context) *ProjectsLocationsQueuesTasksBufferCall {
	c.ctx_ = ctx
	return c
}

// Header returns a http.Header that can be modified by the caller to add
// headers to the request.
func (c *ProjectsLocationsQueuesTasksBufferCall) Header() http.Header {
	if c.header_ == nil {
		c.header_ = make(http.Header)
	}
	return c.header_
}

func (c *ProjectsLocationsQueuesTasksBufferCall) doRequest(alt string) (*http.Response, error) {
	reqHeaders := gensupport.SetHeaders(c.s.userAgent(), "application/json", c.header_)
	var body io.Reader = nil
	body, err := googleapi.WithoutDataWrapper.JSONReader(c.buffertaskrequest)
	if err != nil {
		return nil, err
	}
	c.urlParams_.Set("alt", alt)
	c.urlParams_.Set("prettyPrint", "false")
	urls := googleapi.ResolveRelative(c.s.BasePath, "v2beta2/{+queue}/tasks/{taskId}:buffer")
	urls += "?" + c.urlParams_.Encode()
	req, err := http.NewRequest("POST", urls, body)
	if err != nil {
		return nil, err
	}
	req.Header = reqHeaders
	googleapi.Expand(req.URL, map[string]string{
		"queue":  c.queue,
		"taskId": c.taskId,
	})
	return gensupport.SendRequest(c.ctx_, c.s.client, req)
}

// Do executes the "cloudtasks.projects.locations.queues.tasks.buffer" call.
// Any non-2xx status code is an error. Response headers are in either
// *BufferTaskResponse.ServerResponse.Header or (if a response was returned at
// all) in error.(*googleapi.Error).Header. Use googleapi.IsNotModified to
// check whether the returned error was because http.StatusNotModified was
// returned.
func (c *ProjectsLocationsQueuesTasksBufferCall) Do(opts ...googleapi.CallOption) (*BufferTaskResponse, error) {
	gensupport.SetOptions(c.urlParams_, opts...)
	res, err := c.doRequest("json")
	if res != nil && res.StatusCode == http.StatusNotModified {
		if res.Body != nil {
			res.Body.Close()
		}
		return nil, gensupport.WrapError(&googleapi.Error{
			Code:   res.StatusCode,
			Header: res.Header,
		})
	}
	if err != nil {
		return nil, err
	}
	defer googleapi.CloseBody(res)
	if err := googleapi.CheckResponse(res); err != nil {
		return nil, gensupport.WrapError(err)
	}
	ret := &BufferTaskResponse{
		ServerResponse: googleapi.ServerResponse{
			Header:         res.Header,
			HTTPStatusCode: res.StatusCode,
		},
	}
	target := &ret
	if err := gensupport.DecodeResponse(target, res); err != nil {
		return nil, err
	}
	return ret, nil
}

type ProjectsLocationsQueuesTasksCancelLeaseCall struct {
	s                  *Service
	name               string
	cancelleaserequest *CancelLeaseRequest
	urlParams_         gensupport.URLParams
	ctx_               context.Context
	header_            http.Header
}

// CancelLease: Cancel a pull task's lease. The worker can use this method to
// cancel a task's lease by setting its schedule_time to now. This will make
// the task available to be leased to the next caller of LeaseTasks.
//
//   - name: The task name. For example:
//     `projects/PROJECT_ID/locations/LOCATION_ID/queues/QUEUE_ID/tasks/TASK_ID`.
func (r *ProjectsLocationsQueuesTasksService) CancelLease(name string, cancelleaserequest *CancelLeaseRequest) *ProjectsLocationsQueuesTasksCancelLeaseCall {
	c := &ProjectsLocationsQueuesTasksCancelLeaseCall{s: r.s, urlParams_: make(gensupport.URLParams)}
	c.name = name
	c.cancelleaserequest = cancelleaserequest
	return c
}

// Fields allows partial responses to be retrieved. See
// https://developers.google.com/gdata/docs/2.0/basics#PartialResponse for more
// details.
func (c *ProjectsLocationsQueuesTasksCancelLeaseCall) Fields(s ...googleapi.Field) *ProjectsLocationsQueuesTasksCancelLeaseCall {
	c.urlParams_.Set("fields", googleapi.CombineFields(s))
	return c
}

// Context sets the context to be used in this call's Do method.
func (c *ProjectsLocationsQueuesTasksCancelLeaseCall) Context(ctx context.Context) *ProjectsLocationsQueuesTasksCancelLeaseCall {
	c.ctx_ = ctx
	return c
}

// Header returns a http.Header that can be modified by the caller to add
// headers to the request.
func (c *ProjectsLocationsQueuesTasksCancelLeaseCall) Header() http.Header {
	if c.header_ == nil {
		c.header_ = make(http.Header)
	}
	return c.header_
}

func (c *ProjectsLocationsQueuesTasksCancelLeaseCall) doRequest(alt string) (*http.Response, error) {
	reqHeaders := gensupport.SetHeaders(c.s.userAgent(), "application/json", c.header_)
	var body io.Reader = nil
	body, err := googleapi.WithoutDataWrapper.JSONReader(c.cancelleaserequest)
	if err != nil {
		return nil, err
	}
	c.urlParams_.Set("alt", alt)
	c.urlParams_.Set("prettyPrint", "false")
	urls := googleapi.ResolveRelative(c.s.BasePath, "v2beta2/{+name}:cancelLease")
	urls += "?" + c.urlParams_.Encode()
	req, err := http.NewRequest("POST", urls, body)
	if err != nil {
		return nil, err
	}
	req.Header = reqHeaders
	googleapi.Expand(req.URL, map[string]string{
		"name": c.name,
	})
	return gensupport.SendRequest(c.ctx_, c.s.client, req)
}

// Do executes the "cloudtasks.projects.locations.queues.tasks.cancelLease" call.
// Any non-2xx status code is an error. Response headers are in either
// *Task.ServerResponse.Header or (if a response was returned at all) in
// error.(*googleapi.Error).Header. Use googleapi.IsNotModified to check
// whether the returned error was because http.StatusNotModified was returned.
func (c *ProjectsLocationsQueuesTasksCancelLeaseCall) Do(opts ...googleapi.CallOption) (*Task, error) {
	gensupport.SetOptions(c.urlParams_, opts...)
	res, err := c.doRequest("json")
	if res != nil && res.StatusCode == http.StatusNotModified {
		if res.Body != nil {
			res.Body.Close()
		}
		return nil, gensupport.WrapError(&googleapi.Error{
			Code:   res.StatusCode,
			Header: res.Header,
		})
	}
	if err != nil {
		return nil, err
	}
	defer googleapi.CloseBody(res)
	if err := googleapi.CheckResponse(res); err != nil {
		return nil, gensupport.WrapError(err)
	}
	ret := &Task{
		ServerResponse: googleapi.ServerResponse{
			Header:         res.Header,
			HTTPStatusCode: res.StatusCode,
		},
	}
	target := &ret
	if err := gensupport.DecodeResponse(target, res); err != nil {
		return nil, err
	}
	return ret, nil
}

type ProjectsLocationsQueuesTasksCreateCall struct {
	s                 *Service
	parent            string
	createtaskrequest *CreateTaskRequest
	urlParams_        gensupport.URLParams
	ctx_              context.Context
	header_           http.Header
}

// Create: Creates a task and adds it to a queue. Tasks cannot be updated after
// creation; there is no UpdateTask command. * For App Engine queues, the
// maximum task size is 100KB. * For pull queues, the maximum task size is 1MB.
//
//   - parent: The queue name. For example:
//     `projects/PROJECT_ID/locations/LOCATION_ID/queues/QUEUE_ID` The queue must
//     already exist.
func (r *ProjectsLocationsQueuesTasksService) Create(parent string, createtaskrequest *CreateTaskRequest) *ProjectsLocationsQueuesTasksCreateCall {
	c := &ProjectsLocationsQueuesTasksCreateCall{s: r.s, urlParams_: make(gensupport.URLParams)}
	c.parent = parent
	c.createtaskrequest = createtaskrequest
	return c
}

// Fields allows partial responses to be retrieved. See
// https://developers.google.com/gdata/docs/2.0/basics#PartialResponse for more
// details.
func (c *ProjectsLocationsQueuesTasksCreateCall) Fields(s ...googleapi.Field) *ProjectsLocationsQueuesTasksCreateCall {
	c.urlParams_.Set("fields", googleapi.CombineFields(s))
	return c
}

// Context sets the context to be used in this call's Do method.
func (c *ProjectsLocationsQueuesTasksCreateCall) Context(ctx context.Context) *ProjectsLocationsQueuesTasksCreateCall {
	c.ctx_ = ctx
	return c
}

// Header returns a http.Header that can be modified by the caller to add
// headers to the request.
func (c *ProjectsLocationsQueuesTasksCreateCall) Header() http.Header {
	if c.header_ == nil {
		c.header_ = make(http.Header)
	}
	return c.header_
}

func (c *ProjectsLocationsQueuesTasksCreateCall) doRequest(alt string) (*http.Response, error) {
	reqHeaders := gensupport.SetHeaders(c.s.userAgent(), "application/json", c.header_)
	var body io.Reader = nil
	body, err := googleapi.WithoutDataWrapper.JSONReader(c.createtaskrequest)
	if err != nil {
		return nil, err
	}
	c.urlParams_.Set("alt", alt)
	c.urlParams_.Set("prettyPrint", "false")
	urls := googleapi.ResolveRelative(c.s.BasePath, "v2beta2/{+parent}/tasks")
	urls += "?" + c.urlParams_.Encode()
	req, err := http.NewRequest("POST", urls, body)
	if err != nil {
		return nil, err
	}
	req.Header = reqHeaders
	googleapi.Expand(req.URL, map[string]string{
		"parent": c.parent,
	})
	return gensupport.SendRequest(c.ctx_, c.s.client, req)
}

// Do executes the "cloudtasks.projects.locations.queues.tasks.create" call.
// Any non-2xx status code is an error. Response headers are in either
// *Task.ServerResponse.Header or (if a response was returned at all) in
// error.(*googleapi.Error).Header. Use googleapi.IsNotModified to check
// whether the returned error was because http.StatusNotModified was returned.
func (c *ProjectsLocationsQueuesTasksCreateCall) Do(opts ...googleapi.CallOption) (*Task, error) {
	gensupport.SetOptions(c.urlParams_, opts...)
	res, err := c.doRequest("json")
	if res != nil && res.StatusCode == http.StatusNotModified {
		if res.Body != nil {
			res.Body.Close()
		}
		return nil, gensupport.WrapError(&googleapi.Error{
			Code:   res.StatusCode,
			Header: res.Header,
		})
	}
	if err != nil {
		return nil, err
	}
	defer googleapi.CloseBody(res)
	if err := googleapi.CheckResponse(res); err != nil {
		return nil, gensupport.WrapError(err)
	}
	ret := &Task{
		ServerResponse: googleapi.ServerResponse{
			Header:         res.Header,
			HTTPStatusCode: res.StatusCode,
		},
	}
	target := &ret
	if err := gensupport.DecodeResponse(target, res); err != nil {
		return nil, err
	}
	return ret, nil
}

type ProjectsLocationsQueuesTasksDeleteCall struct {
	s          *Service
	name       string
	urlParams_ gensupport.URLParams
	ctx_       context.Context
	header_    http.Header
}

// Delete: Deletes a task. A task can be deleted if it is scheduled or
// dispatched. A task cannot be deleted if it has completed successfully or
// permanently failed.
//
//   - name: The task name. For example:
//     `projects/PROJECT_ID/locations/LOCATION_ID/queues/QUEUE_ID/tasks/TASK_ID`.
func (r *ProjectsLocationsQueuesTasksService) Delete(name string) *ProjectsLocationsQueuesTasksDeleteCall {
	c := &ProjectsLocationsQueuesTasksDeleteCall{s: r.s, urlParams_: make(gensupport.URLParams)}
	c.name = name
	return c
}

// Fields allows partial responses to be retrieved. See
// https://developers.google.com/gdata/docs/2.0/basics#PartialResponse for more
// details.
func (c *ProjectsLocationsQueuesTasksDeleteCall) Fields(s ...googleapi.Field) *ProjectsLocationsQueuesTasksDeleteCall {
	c.urlParams_.Set("fields", googleapi.CombineFields(s))
	return c
}

// Context sets the context to be used in this call's Do method.
func (c *ProjectsLocationsQueuesTasksDeleteCall) Context(ctx context.Context) *ProjectsLocationsQueuesTasksDeleteCall {
	c.ctx_ = ctx
	return c
}

// Header returns a http.Header that can be modified by the caller to add
// headers to the request.
func (c *ProjectsLocationsQueuesTasksDeleteCall) Header() http.Header {
	if c.header_ == nil {
		c.header_ = make(http.Header)
	}
	return c.header_
}

func (c *ProjectsLocationsQueuesTasksDeleteCall) doRequest(alt string) (*http.Response, error) {
	reqHeaders := gensupport.SetHeaders(c.s.userAgent(), "", c.header_)
	var body io.Reader = nil
	c.urlParams_.Set("alt", alt)
	c.urlParams_.Set("prettyPrint", "false")
	urls := googleapi.ResolveRelative(c.s.BasePath, "v2beta2/{+name}")
	urls += "?" + c.urlParams_.Encode()
	req, err := http.NewRequest("DELETE", urls, body)
	if err != nil {
		return nil, err
	}
	req.Header = reqHeaders
	googleapi.Expand(req.URL, map[string]string{
		"name": c.name,
	})
	return gensupport.SendRequest(c.ctx_, c.s.client, req)
}

// Do executes the "cloudtasks.projects.locations.queues.tasks.delete" call.
// Any non-2xx status code is an error. Response headers are in either
// *Empty.ServerResponse.Header or (if a response was returned at all) in
// error.(*googleapi.Error).Header. Use googleapi.IsNotModified to check
// whether the returned error was because http.StatusNotModified was returned.
func (c *ProjectsLocationsQueuesTasksDeleteCall) Do(opts ...googleapi.CallOption) (*Empty, error) {
	gensupport.SetOptions(c.urlParams_, opts...)
	res, err := c.doRequest("json")
	if res != nil && res.StatusCode == http.StatusNotModified {
		if res.Body != nil {
			res.Body.Close()
		}
		return nil, gensupport.WrapError(&googleapi.Error{
			Code:   res.StatusCode,
			Header: res.Header,
		})
	}
	if err != nil {
		return nil, err
	}
	defer googleapi.CloseBody(res)
	if err := googleapi.CheckResponse(res); err != nil {
		return nil, gensupport.WrapError(err)
	}
	ret := &Empty{
		ServerResponse: googleapi.ServerResponse{
			Header:         res.Header,
			HTTPStatusCode: res.StatusCode,
		},
	}
	target := &ret
	if err := gensupport.DecodeResponse(target, res); err != nil {
		return nil, err
	}
	return ret, nil
}

type ProjectsLocationsQueuesTasksGetCall struct {
	s            *Service
	name         string
	urlParams_   gensupport.URLParams
	ifNoneMatch_ string
	ctx_         context.Context
	header_      http.Header
}

// Get: Gets a task.
//
//   - name: The task name. For example:
//     `projects/PROJECT_ID/locations/LOCATION_ID/queues/QUEUE_ID/tasks/TASK_ID`.
func (r *ProjectsLocationsQueuesTasksService) Get(name string) *ProjectsLocationsQueuesTasksGetCall {
	c := &ProjectsLocationsQueuesTasksGetCall{s: r.s, urlParams_: make(gensupport.URLParams)}
	c.name = name
	return c
}

// ResponseView sets the optional parameter "responseView": The response_view
// specifies which subset of the Task will be returned. By default
// response_view is BASIC; not all information is retrieved by default because
// some data, such as payloads, might be desirable to return only when needed
// because of its large size or because of the sensitivity of data that it
// contains. Authorization for FULL requires `cloudtasks.tasks.fullView` Google
// IAM (https://cloud.google.com/iam/) permission on the Task resource.
//
// Possible values:
//
//	"VIEW_UNSPECIFIED" - Unspecified. Defaults to BASIC.
//	"BASIC" - The basic view omits fields which can be large or can contain
//
// sensitive data. This view does not include the (payload in
// AppEngineHttpRequest and payload in PullMessage). These payloads are
// desirable to return only when needed, because they can be large and because
// of the sensitivity of the data that you choose to store in it.
//
//	"FULL" - All information is returned. Authorization for FULL requires
//
// `cloudtasks.tasks.fullView` [Google IAM](https://cloud.google.com/iam/)
// permission on the Queue resource.
func (c *ProjectsLocationsQueuesTasksGetCall) ResponseView(responseView string) *ProjectsLocationsQueuesTasksGetCall {
	c.urlParams_.Set("responseView", responseView)
	return c
}

// Fields allows partial responses to be retrieved. See
// https://developers.google.com/gdata/docs/2.0/basics#PartialResponse for more
// details.
func (c *ProjectsLocationsQueuesTasksGetCall) Fields(s ...googleapi.Field) *ProjectsLocationsQueuesTasksGetCall {
	c.urlParams_.Set("fields", googleapi.CombineFields(s))
	return c
}

// IfNoneMatch sets an optional parameter which makes the operation fail if the
// object's ETag matches the given value. This is useful for getting updates
// only after the object has changed since the last request.
func (c *ProjectsLocationsQueuesTasksGetCall) IfNoneMatch(entityTag string) *ProjectsLocationsQueuesTasksGetCall {
	c.ifNoneMatch_ = entityTag
	return c
}

// Context sets the context to be used in this call's Do method.
func (c *ProjectsLocationsQueuesTasksGetCall) Context(ctx context.Context) *ProjectsLocationsQueuesTasksGetCall {
	c.ctx_ = ctx
	return c
}

// Header returns a http.Header that can be modified by the caller to add
// headers to the request.
func (c *ProjectsLocationsQueuesTasksGetCall) Header() http.Header {
	if c.header_ == nil {
		c.header_ = make(http.Header)
	}
	return c.header_
}

func (c *ProjectsLocationsQueuesTasksGetCall) doRequest(alt string) (*http.Response, error) {
	reqHeaders := gensupport.SetHeaders(c.s.userAgent(), "", c.header_)
	if c.ifNoneMatch_ != "" {
		reqHeaders.Set("If-None-Match", c.ifNoneMatch_)
	}
	var body io.Reader = nil
	c.urlParams_.Set("alt", alt)
	c.urlParams_.Set("prettyPrint", "false")
	urls := googleapi.ResolveRelative(c.s.BasePath, "v2beta2/{+name}")
	urls += "?" + c.urlParams_.Encode()
	req, err := http.NewRequest("GET", urls, body)
	if err != nil {
		return nil, err
	}
	req.Header = reqHeaders
	googleapi.Expand(req.URL, map[string]string{
		"name": c.name,
	})
	return gensupport.SendRequest(c.ctx_, c.s.client, req)
}

// Do executes the "cloudtasks.projects.locations.queues.tasks.get" call.
// Any non-2xx status code is an error. Response headers are in either
// *Task.ServerResponse.Header or (if a response was returned at all) in
// error.(*googleapi.Error).Header. Use googleapi.IsNotModified to check
// whether the returned error was because http.StatusNotModified was returned.
func (c *ProjectsLocationsQueuesTasksGetCall) Do(opts ...googleapi.CallOption) (*Task, error) {
	gensupport.SetOptions(c.urlParams_, opts...)
	res, err := c.doRequest("json")
	if res != nil && res.StatusCode == http.StatusNotModified {
		if res.Body != nil {
			res.Body.Close()
		}
		return nil, gensupport.WrapError(&googleapi.Error{
			Code:   res.StatusCode,
			Header: res.Header,
		})
	}
	if err != nil {
		return nil, err
	}
	defer googleapi.CloseBody(res)
	if err := googleapi.CheckResponse(res); err != nil {
		return nil, gensupport.WrapError(err)
	}
	ret := &Task{
		ServerResponse: googleapi.ServerResponse{
			Header:         res.Header,
			HTTPStatusCode: res.StatusCode,
		},
	}
	target := &ret
	if err := gensupport.DecodeResponse(target, res); err != nil {
		return nil, err
	}
	return ret, nil
}

type ProjectsLocationsQueuesTasksLeaseCall struct {
	s                 *Service
	parent            string
	leasetasksrequest *LeaseTasksRequest
	urlParams_        gensupport.URLParams
	ctx_              context.Context
	header_           http.Header
}

// Lease: Leases tasks from a pull queue for lease_duration. This method is
// invoked by the worker to obtain a lease. The worker must acknowledge the
// task via AcknowledgeTask after they have performed the work associated with
// the task. The payload is intended to store data that the worker needs to
// perform the work associated with the task. To return the payloads in the
// response, set response_view to FULL. A maximum of 10 qps of LeaseTasks
// requests are allowed per queue. RESOURCE_EXHAUSTED is returned when this
// limit is exceeded. RESOURCE_EXHAUSTED is also returned when
// max_tasks_dispatched_per_second is exceeded.
//
//   - parent: The queue name. For example:
//     `projects/PROJECT_ID/locations/LOCATION_ID/queues/QUEUE_ID`.
func (r *ProjectsLocationsQueuesTasksService) Lease(parent string, leasetasksrequest *LeaseTasksRequest) *ProjectsLocationsQueuesTasksLeaseCall {
	c := &ProjectsLocationsQueuesTasksLeaseCall{s: r.s, urlParams_: make(gensupport.URLParams)}
	c.parent = parent
	c.leasetasksrequest = leasetasksrequest
	return c
}

// Fields allows partial responses to be retrieved. See
// https://developers.google.com/gdata/docs/2.0/basics#PartialResponse for more
// details.
func (c *ProjectsLocationsQueuesTasksLeaseCall) Fields(s ...googleapi.Field) *ProjectsLocationsQueuesTasksLeaseCall {
	c.urlParams_.Set("fields", googleapi.CombineFields(s))
	return c
}

// Context sets the context to be used in this call's Do method.
func (c *ProjectsLocationsQueuesTasksLeaseCall) Context(ctx context.Context) *ProjectsLocationsQueuesTasksLeaseCall {
	c.ctx_ = ctx
	return c
}

// Header returns a http.Header that can be modified by the caller to add
// headers to the request.
func (c *ProjectsLocationsQueuesTasksLeaseCall) Header() http.Header {
	if c.header_ == nil {
		c.header_ = make(http.Header)
	}
	return c.header_
}

func (c *ProjectsLocationsQueuesTasksLeaseCall) doRequest(alt string) (*http.Response, error) {
	reqHeaders := gensupport.SetHeaders(c.s.userAgent(), "application/json", c.header_)
	var body io.Reader = nil
	body, err := googleapi.WithoutDataWrapper.JSONReader(c.leasetasksrequest)
	if err != nil {
		return nil, err
	}
	c.urlParams_.Set("alt", alt)
	c.urlParams_.Set("prettyPrint", "false")
	urls := googleapi.ResolveRelative(c.s.BasePath, "v2beta2/{+parent}/tasks:lease")
	urls += "?" + c.urlParams_.Encode()
	req, err := http.NewRequest("POST", urls, body)
	if err != nil {
		return nil, err
	}
	req.Header = reqHeaders
	googleapi.Expand(req.URL, map[string]string{
		"parent": c.parent,
	})
	return gensupport.SendRequest(c.ctx_, c.s.client, req)
}

// Do executes the "cloudtasks.projects.locations.queues.tasks.lease" call.
// Any non-2xx status code is an error. Response headers are in either
// *LeaseTasksResponse.ServerResponse.Header or (if a response was returned at
// all) in error.(*googleapi.Error).Header. Use googleapi.IsNotModified to
// check whether the returned error was because http.StatusNotModified was
// returned.
func (c *ProjectsLocationsQueuesTasksLeaseCall) Do(opts ...googleapi.CallOption) (*LeaseTasksResponse, error) {
	gensupport.SetOptions(c.urlParams_, opts...)
	res, err := c.doRequest("json")
	if res != nil && res.StatusCode == http.StatusNotModified {
		if res.Body != nil {
			res.Body.Close()
		}
		return nil, gensupport.WrapError(&googleapi.Error{
			Code:   res.StatusCode,
			Header: res.Header,
		})
	}
	if err != nil {
		return nil, err
	}
	defer googleapi.CloseBody(res)
	if err := googleapi.CheckResponse(res); err != nil {
		return nil, gensupport.WrapError(err)
	}
	ret := &LeaseTasksResponse{
		ServerResponse: googleapi.ServerResponse{
			Header:         res.Header,
			HTTPStatusCode: res.StatusCode,
		},
	}
	target := &ret
	if err := gensupport.DecodeResponse(target, res); err != nil {
		return nil, err
	}
	return ret, nil
}

type ProjectsLocationsQueuesTasksListCall struct {
	s            *Service
	parent       string
	urlParams_   gensupport.URLParams
	ifNoneMatch_ string
	ctx_         context.Context
	header_      http.Header
}

// List: Lists the tasks in a queue. By default, only the BASIC view is
// retrieved due to performance considerations; response_view controls the
// subset of information which is returned. The tasks may be returned in any
// order. The ordering may change at any time.
//
//   - parent: The queue name. For example:
//     `projects/PROJECT_ID/locations/LOCATION_ID/queues/QUEUE_ID`.
func (r *ProjectsLocationsQueuesTasksService) List(parent string) *ProjectsLocationsQueuesTasksListCall {
	c := &ProjectsLocationsQueuesTasksListCall{s: r.s, urlParams_: make(gensupport.URLParams)}
	c.parent = parent
	return c
}

// PageSize sets the optional parameter "pageSize": Maximum page size. Fewer
// tasks than requested might be returned, even if more tasks exist; use
// next_page_token in the response to determine if more tasks exist. The
// maximum page size is 1000. If unspecified, the page size will be the
// maximum.
func (c *ProjectsLocationsQueuesTasksListCall) PageSize(pageSize int64) *ProjectsLocationsQueuesTasksListCall {
	c.urlParams_.Set("pageSize", fmt.Sprint(pageSize))
	return c
}

// PageToken sets the optional parameter "pageToken": A token identifying the
// page of results to return. To request the first page results, page_token
// must be empty. To request the next page of results, page_token must be the
// value of next_page_token returned from the previous call to ListTasks
// method. The page token is valid for only 2 hours.
func (c *ProjectsLocationsQueuesTasksListCall) PageToken(pageToken string) *ProjectsLocationsQueuesTasksListCall {
	c.urlParams_.Set("pageToken", pageToken)
	return c
}

// ResponseView sets the optional parameter "responseView": The response_view
// specifies which subset of the Task will be returned. By default
// response_view is BASIC; not all information is retrieved by default because
// some data, such as payloads, might be desirable to return only when needed
// because of its large size or because of the sensitivity of data that it
// contains. Authorization for FULL requires `cloudtasks.tasks.fullView` Google
// IAM (https://cloud.google.com/iam/) permission on the Task resource.
//
// Possible values:
//
//	"VIEW_UNSPECIFIED" - Unspecified. Defaults to BASIC.
//	"BASIC" - The basic view omits fields which can be large or can contain
//
// sensitive data. This view does not include the (payload in
// AppEngineHttpRequest and payload in PullMessage). These payloads are
// desirable to return only when needed, because they can be large and because
// of the sensitivity of the data that you choose to store in it.
//
//	"FULL" - All information is returned. Authorization for FULL requires
//
// `cloudtasks.tasks.fullView` [Google IAM](https://cloud.google.com/iam/)
// permission on the Queue resource.
func (c *ProjectsLocationsQueuesTasksListCall) ResponseView(responseView string) *ProjectsLocationsQueuesTasksListCall {
	c.urlParams_.Set("responseView", responseView)
	return c
}

// Fields allows partial responses to be retrieved. See
// https://developers.google.com/gdata/docs/2.0/basics#PartialResponse for more
// details.
func (c *ProjectsLocationsQueuesTasksListCall) Fields(s ...googleapi.Field) *ProjectsLocationsQueuesTasksListCall {
	c.urlParams_.Set("fields", googleapi.CombineFields(s))
	return c
}

// IfNoneMatch sets an optional parameter which makes the operation fail if the
// object's ETag matches the given value. This is useful for getting updates
// only after the object has changed since the last request.
func (c *ProjectsLocationsQueuesTasksListCall) IfNoneMatch(entityTag string) *ProjectsLocationsQueuesTasksListCall {
	c.ifNoneMatch_ = entityTag
	return c
}

// Context sets the context to be used in this call's Do method.
func (c *ProjectsLocationsQueuesTasksListCall) Context(ctx context.Context) *ProjectsLocationsQueuesTasksListCall {
	c.ctx_ = ctx
	return c
}

// Header returns a http.Header that can be modified by the caller to add
// headers to the request.
func (c *ProjectsLocationsQueuesTasksListCall) Header() http.Header {
	if c.header_ == nil {
		c.header_ = make(http.Header)
	}
	return c.header_
}

func (c *ProjectsLocationsQueuesTasksListCall) doRequest(alt string) (*http.Response, error) {
	reqHeaders := gensupport.SetHeaders(c.s.userAgent(), "", c.header_)
	if c.ifNoneMatch_ != "" {
		reqHeaders.Set("If-None-Match", c.ifNoneMatch_)
	}
	var body io.Reader = nil
	c.urlParams_.Set("alt", alt)
	c.urlParams_.Set("prettyPrint", "false")
	urls := googleapi.ResolveRelative(c.s.BasePath, "v2beta2/{+parent}/tasks")
	urls += "?" + c.urlParams_.Encode()
	req, err := http.NewRequest("GET", urls, body)
	if err != nil {
		return nil, err
	}
	req.Header = reqHeaders
	googleapi.Expand(req.URL, map[string]string{
		"parent": c.parent,
	})
	return gensupport.SendRequest(c.ctx_, c.s.client, req)
}

// Do executes the "cloudtasks.projects.locations.queues.tasks.list" call.
// Any non-2xx status code is an error. Response headers are in either
// *ListTasksResponse.ServerResponse.Header or (if a response was returned at
// all) in error.(*googleapi.Error).Header. Use googleapi.IsNotModified to
// check whether the returned error was because http.StatusNotModified was
// returned.
func (c *ProjectsLocationsQueuesTasksListCall) Do(opts ...googleapi.CallOption) (*ListTasksResponse, error) {
	gensupport.SetOptions(c.urlParams_, opts...)
	res, err := c.doRequest("json")
	if res != nil && res.StatusCode == http.StatusNotModified {
		if res.Body != nil {
			res.Body.Close()
		}
		return nil, gensupport.WrapError(&googleapi.Error{
			Code:   res.StatusCode,
			Header: res.Header,
		})
	}
	if err != nil {
		return nil, err
	}
	defer googleapi.CloseBody(res)
	if err := googleapi.CheckResponse(res); err != nil {
		return nil, gensupport.WrapError(err)
	}
	ret := &ListTasksResponse{
		ServerResponse: googleapi.ServerResponse{
			Header:         res.Header,
			HTTPStatusCode: res.StatusCode,
		},
	}
	target := &ret
	if err := gensupport.DecodeResponse(target, res); err != nil {
		return nil, err
	}
	return ret, nil
}

// Pages invokes f for each page of results.
// A non-nil error returned from f will halt the iteration.
// The provided context supersedes any context provided to the Context method.
func (c *ProjectsLocationsQueuesTasksListCall) Pages(ctx context.Context, f func(*ListTasksResponse) error) error {
	c.ctx_ = ctx
	defer c.PageToken(c.urlParams_.Get("pageToken"))
	for {
		x, err := c.Do()
		if err != nil {
			return err
		}
		if err := f(x); err != nil {
			return err
		}
		if x.NextPageToken == "" {
			return nil
		}
		c.PageToken(x.NextPageToken)
	}
}

type ProjectsLocationsQueuesTasksRenewLeaseCall struct {
	s                 *Service
	name              string
	renewleaserequest *RenewLeaseRequest
	urlParams_        gensupport.URLParams
	ctx_              context.Context
	header_           http.Header
}

// RenewLease: Renew the current lease of a pull task. The worker can use this
// method to extend the lease by a new duration, starting from now. The new
// task lease will be returned in the task's schedule_time.
//
//   - name: The task name. For example:
//     `projects/PROJECT_ID/locations/LOCATION_ID/queues/QUEUE_ID/tasks/TASK_ID`.
func (r *ProjectsLocationsQueuesTasksService) RenewLease(name string, renewleaserequest *RenewLeaseRequest) *ProjectsLocationsQueuesTasksRenewLeaseCall {
	c := &ProjectsLocationsQueuesTasksRenewLeaseCall{s: r.s, urlParams_: make(gensupport.URLParams)}
	c.name = name
	c.renewleaserequest = renewleaserequest
	return c
}

// Fields allows partial responses to be retrieved. See
// https://developers.google.com/gdata/docs/2.0/basics#PartialResponse for more
// details.
func (c *ProjectsLocationsQueuesTasksRenewLeaseCall) Fields(s ...googleapi.Field) *ProjectsLocationsQueuesTasksRenewLeaseCall {
	c.urlParams_.Set("fields", googleapi.CombineFields(s))
	return c
}

// Context sets the context to be used in this call's Do method.
func (c *ProjectsLocationsQueuesTasksRenewLeaseCall) Context(ctx context.Context) *ProjectsLocationsQueuesTasksRenewLeaseCall {
	c.ctx_ = ctx
	return c
}

// Header returns a http.Header that can be modified by the caller to add
// headers to the request.
func (c *ProjectsLocationsQueuesTasksRenewLeaseCall) Header() http.Header {
	if c.header_ == nil {
		c.header_ = make(http.Header)
	}
	return c.header_
}

func (c *ProjectsLocationsQueuesTasksRenewLeaseCall) doRequest(alt string) (*http.Response, error) {
	reqHeaders := gensupport.SetHeaders(c.s.userAgent(), "application/json", c.header_)
	var body io.Reader = nil
	body, err := googleapi.WithoutDataWrapper.JSONReader(c.renewleaserequest)
	if err != nil {
		return nil, err
	}
	c.urlParams_.Set("alt", alt)
	c.urlParams_.Set("prettyPrint", "false")
	urls := googleapi.ResolveRelative(c.s.BasePath, "v2beta2/{+name}:renewLease")
	urls += "?" + c.urlParams_.Encode()
	req, err := http.NewRequest("POST", urls, body)
	if err != nil {
		return nil, err
	}
	req.Header = reqHeaders
	googleapi.Expand(req.URL, map[string]string{
		"name": c.name,
	})
	return gensupport.SendRequest(c.ctx_, c.s.client, req)
}

// Do executes the "cloudtasks.projects.locations.queues.tasks.renewLease" call.
// Any non-2xx status code is an error. Response headers are in either
// *Task.ServerResponse.Header or (if a response was returned at all) in
// error.(*googleapi.Error).Header. Use googleapi.IsNotModified to check
// whether the returned error was because http.StatusNotModified was returned.
func (c *ProjectsLocationsQueuesTasksRenewLeaseCall) Do(opts ...googleapi.CallOption) (*Task, error) {
	gensupport.SetOptions(c.urlParams_, opts...)
	res, err := c.doRequest("json")
	if res != nil && res.StatusCode == http.StatusNotModified {
		if res.Body != nil {
			res.Body.Close()
		}
		return nil, gensupport.WrapError(&googleapi.Error{
			Code:   res.StatusCode,
			Header: res.Header,
		})
	}
	if err != nil {
		return nil, err
	}
	defer googleapi.CloseBody(res)
	if err := googleapi.CheckResponse(res); err != nil {
		return nil, gensupport.WrapError(err)
	}
	ret := &Task{
		ServerResponse: googleapi.ServerResponse{
			Header:         res.Header,
			HTTPStatusCode: res.StatusCode,
		},
	}
	target := &ret
	if err := gensupport.DecodeResponse(target, res); err != nil {
		return nil, err
	}
	return ret, nil
}

type ProjectsLocationsQueuesTasksRunCall struct {
	s              *Service
	name           string
	runtaskrequest *RunTaskRequest
	urlParams_     gensupport.URLParams
	ctx_           context.Context
	header_        http.Header
}

// Run: Forces a task to run now. When this method is called, Cloud Tasks will
// dispatch the task, even if the task is already running, the queue has
// reached its RateLimits or is PAUSED. This command is meant to be used for
// manual debugging. For example, RunTask can be used to retry a failed task
// after a fix has been made or to manually force a task to be dispatched now.
// The dispatched task is returned. That is, the task that is returned contains
// the status after the task is dispatched but before the task is received by
// its target. If Cloud Tasks receives a successful response from the task's
// target, then the task will be deleted; otherwise the task's schedule_time
// will be reset to the time that RunTask was called plus the retry delay
// specified in the queue's RetryConfig. RunTask returns NOT_FOUND when it is
// called on a task that has already succeeded or permanently failed. RunTask
// cannot be called on a pull task.
//
//   - name: The task name. For example:
//     `projects/PROJECT_ID/locations/LOCATION_ID/queues/QUEUE_ID/tasks/TASK_ID`.
func (r *ProjectsLocationsQueuesTasksService) Run(name string, runtaskrequest *RunTaskRequest) *ProjectsLocationsQueuesTasksRunCall {
	c := &ProjectsLocationsQueuesTasksRunCall{s: r.s, urlParams_: make(gensupport.URLParams)}
	c.name = name
	c.runtaskrequest = runtaskrequest
	return c
}

// Fields allows partial responses to be retrieved. See
// https://developers.google.com/gdata/docs/2.0/basics#PartialResponse for more
// details.
func (c *ProjectsLocationsQueuesTasksRunCall) Fields(s ...googleapi.Field) *ProjectsLocationsQueuesTasksRunCall {
	c.urlParams_.Set("fields", googleapi.CombineFields(s))
	return c
}

// Context sets the context to be used in this call's Do method.
func (c *ProjectsLocationsQueuesTasksRunCall) Context(ctx context.Context) *ProjectsLocationsQueuesTasksRunCall {
	c.ctx_ = ctx
	return c
}

// Header returns a http.Header that can be modified by the caller to add
// headers to the request.
func (c *ProjectsLocationsQueuesTasksRunCall) Header() http.Header {
	if c.header_ == nil {
		c.header_ = make(http.Header)
	}
	return c.header_
}

func (c *ProjectsLocationsQueuesTasksRunCall) doRequest(alt string) (*http.Response, error) {
	reqHeaders := gensupport.SetHeaders(c.s.userAgent(), "application/json", c.header_)
	var body io.Reader = nil
	body, err := googleapi.WithoutDataWrapper.JSONReader(c.runtaskrequest)
	if err != nil {
		return nil, err
	}
	c.urlParams_.Set("alt", alt)
	c.urlParams_.Set("prettyPrint", "false")
	urls := googleapi.ResolveRelative(c.s.BasePath, "v2beta2/{+name}:run")
	urls += "?" + c.urlParams_.Encode()
	req, err := http.NewRequest("POST", urls, body)
	if err != nil {
		return nil, err
	}
	req.Header = reqHeaders
	googleapi.Expand(req.URL, map[string]string{
		"name": c.name,
	})
	return gensupport.SendRequest(c.ctx_, c.s.client, req)
}

// Do executes the "cloudtasks.projects.locations.queues.tasks.run" call.
// Any non-2xx status code is an error. Response headers are in either
// *Task.ServerResponse.Header or (if a response was returned at all) in
// error.(*googleapi.Error).Header. Use googleapi.IsNotModified to check
// whether the returned error was because http.StatusNotModified was returned.
func (c *ProjectsLocationsQueuesTasksRunCall) Do(opts ...googleapi.CallOption) (*Task, error) {
	gensupport.SetOptions(c.urlParams_, opts...)
	res, err := c.doRequest("json")
	if res != nil && res.StatusCode == http.StatusNotModified {
		if res.Body != nil {
			res.Body.Close()
		}
		return nil, gensupport.WrapError(&googleapi.Error{
			Code:   res.StatusCode,
			Header: res.Header,
		})
	}
	if err != nil {
		return nil, err
	}
	defer googleapi.CloseBody(res)
	if err := googleapi.CheckResponse(res); err != nil {
		return nil, gensupport.WrapError(err)
	}
	ret := &Task{
		ServerResponse: googleapi.ServerResponse{
			Header:         res.Header,
			HTTPStatusCode: res.StatusCode,
		},
	}
	target := &ret
	if err := gensupport.DecodeResponse(target, res); err != nil {
		return nil, err
	}
	return ret, nil
}
