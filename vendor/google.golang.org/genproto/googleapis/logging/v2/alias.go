// Copyright 2022 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

// Code generated by aliasgen. DO NOT EDIT.

// Package logging aliases all exported identifiers in package
// "cloud.google.com/go/logging/apiv2/loggingpb".
//
// Deprecated: Please use types in: cloud.google.com/go/logging/apiv2/loggingpb.
// Please read https://github.com/googleapis/google-cloud-go/blob/main/migration.md
// for more details.
package logging

import (
	src "cloud.google.com/go/logging/apiv2/loggingpb"
	grpc "google.golang.org/grpc"
)

// Deprecated: Please use consts in: cloud.google.com/go/logging/apiv2/loggingpb
const (
	LifecycleState_ACTIVE                                     = src.LifecycleState_ACTIVE
	LifecycleState_DELETE_REQUESTED                           = src.LifecycleState_DELETE_REQUESTED
	LifecycleState_LIFECYCLE_STATE_UNSPECIFIED                = src.LifecycleState_LIFECYCLE_STATE_UNSPECIFIED
	LogMetric_V1                                              = src.LogMetric_V1
	LogMetric_V2                                              = src.LogMetric_V2
	LogSink_V1                                                = src.LogSink_V1
	LogSink_V2                                                = src.LogSink_V2
	LogSink_VERSION_FORMAT_UNSPECIFIED                        = src.LogSink_VERSION_FORMAT_UNSPECIFIED
	OperationState_OPERATION_STATE_CANCELLED                  = src.OperationState_OPERATION_STATE_CANCELLED
	OperationState_OPERATION_STATE_FAILED                     = src.OperationState_OPERATION_STATE_FAILED
	OperationState_OPERATION_STATE_RUNNING                    = src.OperationState_OPERATION_STATE_RUNNING
	OperationState_OPERATION_STATE_SCHEDULED                  = src.OperationState_OPERATION_STATE_SCHEDULED
	OperationState_OPERATION_STATE_SUCCEEDED                  = src.OperationState_OPERATION_STATE_SUCCEEDED
	OperationState_OPERATION_STATE_UNSPECIFIED                = src.OperationState_OPERATION_STATE_UNSPECIFIED
	OperationState_OPERATION_STATE_WAITING_FOR_PERMISSIONS    = src.OperationState_OPERATION_STATE_WAITING_FOR_PERMISSIONS
	TailLogEntriesResponse_SuppressionInfo_NOT_CONSUMED       = src.TailLogEntriesResponse_SuppressionInfo_NOT_CONSUMED
	TailLogEntriesResponse_SuppressionInfo_RATE_LIMIT         = src.TailLogEntriesResponse_SuppressionInfo_RATE_LIMIT
	TailLogEntriesResponse_SuppressionInfo_REASON_UNSPECIFIED = src.TailLogEntriesResponse_SuppressionInfo_REASON_UNSPECIFIED
)

// Deprecated: Please use vars in: cloud.google.com/go/logging/apiv2/loggingpb
var (
	File_google_logging_v2_log_entry_proto              = src.File_google_logging_v2_log_entry_proto
	File_google_logging_v2_logging_config_proto         = src.File_google_logging_v2_logging_config_proto
	File_google_logging_v2_logging_metrics_proto        = src.File_google_logging_v2_logging_metrics_proto
	File_google_logging_v2_logging_proto                = src.File_google_logging_v2_logging_proto
	LifecycleState_name                                 = src.LifecycleState_name
	LifecycleState_value                                = src.LifecycleState_value
	LogMetric_ApiVersion_name                           = src.LogMetric_ApiVersion_name
	LogMetric_ApiVersion_value                          = src.LogMetric_ApiVersion_value
	LogSink_VersionFormat_name                          = src.LogSink_VersionFormat_name
	LogSink_VersionFormat_value                         = src.LogSink_VersionFormat_value
	OperationState_name                                 = src.OperationState_name
	OperationState_value                                = src.OperationState_value
	TailLogEntriesResponse_SuppressionInfo_Reason_name  = src.TailLogEntriesResponse_SuppressionInfo_Reason_name
	TailLogEntriesResponse_SuppressionInfo_Reason_value = src.TailLogEntriesResponse_SuppressionInfo_Reason_value
)

// Options that change functionality of a sink exporting data to BigQuery.
//
// Deprecated: Please use types in: cloud.google.com/go/logging/apiv2/loggingpb
type BigQueryOptions = src.BigQueryOptions

// Describes the customer-managed encryption key (CMEK) settings associated
// with a project, folder, organization, billing account, or flexible resource.
// Note: CMEK for the Log Router can currently only be configured for Google
// Cloud organizations. Once configured, it applies to all projects and folders
// in the Google Cloud organization. See [Enabling CMEK for Log
// Router](https://cloud.google.com/logging/docs/routing/managed-encryption)
// for more information.
//
// Deprecated: Please use types in: cloud.google.com/go/logging/apiv2/loggingpb
type CmekSettings = src.CmekSettings

// ConfigServiceV2Client is the client API for ConfigServiceV2 service. For
// semantics around ctx use and closing/ending streaming RPCs, please refer to
// https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
//
// Deprecated: Please use types in: cloud.google.com/go/logging/apiv2/loggingpb
type ConfigServiceV2Client = src.ConfigServiceV2Client

// ConfigServiceV2Server is the server API for ConfigServiceV2 service.
//
// Deprecated: Please use types in: cloud.google.com/go/logging/apiv2/loggingpb
type ConfigServiceV2Server = src.ConfigServiceV2Server

// Metadata for CopyLogEntries long running operations.
//
// Deprecated: Please use types in: cloud.google.com/go/logging/apiv2/loggingpb
type CopyLogEntriesMetadata = src.CopyLogEntriesMetadata

// The parameters to CopyLogEntries.
//
// Deprecated: Please use types in: cloud.google.com/go/logging/apiv2/loggingpb
type CopyLogEntriesRequest = src.CopyLogEntriesRequest

// Response type for CopyLogEntries long running operations.
//
// Deprecated: Please use types in: cloud.google.com/go/logging/apiv2/loggingpb
type CopyLogEntriesResponse = src.CopyLogEntriesResponse

// The parameters to `CreateBucket`.
//
// Deprecated: Please use types in: cloud.google.com/go/logging/apiv2/loggingpb
type CreateBucketRequest = src.CreateBucketRequest

// The parameters to `CreateExclusion`.
//
// Deprecated: Please use types in: cloud.google.com/go/logging/apiv2/loggingpb
type CreateExclusionRequest = src.CreateExclusionRequest

// The parameters to CreateLogMetric.
//
// Deprecated: Please use types in: cloud.google.com/go/logging/apiv2/loggingpb
type CreateLogMetricRequest = src.CreateLogMetricRequest

// The parameters to `CreateSink`.
//
// Deprecated: Please use types in: cloud.google.com/go/logging/apiv2/loggingpb
type CreateSinkRequest = src.CreateSinkRequest

// The parameters to `CreateView`.
//
// Deprecated: Please use types in: cloud.google.com/go/logging/apiv2/loggingpb
type CreateViewRequest = src.CreateViewRequest

// The parameters to `DeleteBucket`.
//
// Deprecated: Please use types in: cloud.google.com/go/logging/apiv2/loggingpb
type DeleteBucketRequest = src.DeleteBucketRequest

// The parameters to `DeleteExclusion`.
//
// Deprecated: Please use types in: cloud.google.com/go/logging/apiv2/loggingpb
type DeleteExclusionRequest = src.DeleteExclusionRequest

// The parameters to DeleteLogMetric.
//
// Deprecated: Please use types in: cloud.google.com/go/logging/apiv2/loggingpb
type DeleteLogMetricRequest = src.DeleteLogMetricRequest

// The parameters to DeleteLog.
//
// Deprecated: Please use types in: cloud.google.com/go/logging/apiv2/loggingpb
type DeleteLogRequest = src.DeleteLogRequest

// The parameters to `DeleteSink`.
//
// Deprecated: Please use types in: cloud.google.com/go/logging/apiv2/loggingpb
type DeleteSinkRequest = src.DeleteSinkRequest

// The parameters to `DeleteView`.
//
// Deprecated: Please use types in: cloud.google.com/go/logging/apiv2/loggingpb
type DeleteViewRequest = src.DeleteViewRequest

// The parameters to `GetBucket`.
//
// Deprecated: Please use types in: cloud.google.com/go/logging/apiv2/loggingpb
type GetBucketRequest = src.GetBucketRequest

// The parameters to
// [GetCmekSettings][google.logging.v2.ConfigServiceV2.GetCmekSettings]. See
// [Enabling CMEK for Log
// Router](https://cloud.google.com/logging/docs/routing/managed-encryption)
// for more information.
//
// Deprecated: Please use types in: cloud.google.com/go/logging/apiv2/loggingpb
type GetCmekSettingsRequest = src.GetCmekSettingsRequest

// The parameters to `GetExclusion`.
//
// Deprecated: Please use types in: cloud.google.com/go/logging/apiv2/loggingpb
type GetExclusionRequest = src.GetExclusionRequest

// The parameters to GetLogMetric.
//
// Deprecated: Please use types in: cloud.google.com/go/logging/apiv2/loggingpb
type GetLogMetricRequest = src.GetLogMetricRequest

// The parameters to
// [GetSettings][google.logging.v2.ConfigServiceV2.GetSettings]. See [Enabling
// CMEK for Log
// Router](https://cloud.google.com/logging/docs/routing/managed-encryption)
// for more information.
//
// Deprecated: Please use types in: cloud.google.com/go/logging/apiv2/loggingpb
type GetSettingsRequest = src.GetSettingsRequest

// The parameters to `GetSink`.
//
// Deprecated: Please use types in: cloud.google.com/go/logging/apiv2/loggingpb
type GetSinkRequest = src.GetSinkRequest

// The parameters to `GetView`.
//
// Deprecated: Please use types in: cloud.google.com/go/logging/apiv2/loggingpb
type GetViewRequest = src.GetViewRequest

// LogBucket lifecycle states.
//
// Deprecated: Please use types in: cloud.google.com/go/logging/apiv2/loggingpb
type LifecycleState = src.LifecycleState

// The parameters to `ListBuckets`.
//
// Deprecated: Please use types in: cloud.google.com/go/logging/apiv2/loggingpb
type ListBucketsRequest = src.ListBucketsRequest

// The response from ListBuckets.
//
// Deprecated: Please use types in: cloud.google.com/go/logging/apiv2/loggingpb
type ListBucketsResponse = src.ListBucketsResponse

// The parameters to `ListExclusions`.
//
// Deprecated: Please use types in: cloud.google.com/go/logging/apiv2/loggingpb
type ListExclusionsRequest = src.ListExclusionsRequest

// Result returned from `ListExclusions`.
//
// Deprecated: Please use types in: cloud.google.com/go/logging/apiv2/loggingpb
type ListExclusionsResponse = src.ListExclusionsResponse

// The parameters to `ListLogEntries`.
//
// Deprecated: Please use types in: cloud.google.com/go/logging/apiv2/loggingpb
type ListLogEntriesRequest = src.ListLogEntriesRequest

// Result returned from `ListLogEntries`.
//
// Deprecated: Please use types in: cloud.google.com/go/logging/apiv2/loggingpb
type ListLogEntriesResponse = src.ListLogEntriesResponse

// The parameters to ListLogMetrics.
//
// Deprecated: Please use types in: cloud.google.com/go/logging/apiv2/loggingpb
type ListLogMetricsRequest = src.ListLogMetricsRequest

// Result returned from ListLogMetrics.
//
// Deprecated: Please use types in: cloud.google.com/go/logging/apiv2/loggingpb
type ListLogMetricsResponse = src.ListLogMetricsResponse

// The parameters to ListLogs.
//
// Deprecated: Please use types in: cloud.google.com/go/logging/apiv2/loggingpb
type ListLogsRequest = src.ListLogsRequest

// Result returned from ListLogs.
//
// Deprecated: Please use types in: cloud.google.com/go/logging/apiv2/loggingpb
type ListLogsResponse = src.ListLogsResponse

// The parameters to ListMonitoredResourceDescriptors
//
// Deprecated: Please use types in: cloud.google.com/go/logging/apiv2/loggingpb
type ListMonitoredResourceDescriptorsRequest = src.ListMonitoredResourceDescriptorsRequest

// Result returned from ListMonitoredResourceDescriptors.
//
// Deprecated: Please use types in: cloud.google.com/go/logging/apiv2/loggingpb
type ListMonitoredResourceDescriptorsResponse = src.ListMonitoredResourceDescriptorsResponse

// The parameters to `ListSinks`.
//
// Deprecated: Please use types in: cloud.google.com/go/logging/apiv2/loggingpb
type ListSinksRequest = src.ListSinksRequest

// Result returned from `ListSinks`.
//
// Deprecated: Please use types in: cloud.google.com/go/logging/apiv2/loggingpb
type ListSinksResponse = src.ListSinksResponse

// The parameters to `ListViews`.
//
// Deprecated: Please use types in: cloud.google.com/go/logging/apiv2/loggingpb
type ListViewsRequest = src.ListViewsRequest

// The response from ListViews.
//
// Deprecated: Please use types in: cloud.google.com/go/logging/apiv2/loggingpb
type ListViewsResponse = src.ListViewsResponse

// Describes a repository in which log entries are stored.
//
// Deprecated: Please use types in: cloud.google.com/go/logging/apiv2/loggingpb
type LogBucket = src.LogBucket

// An individual entry in a log.
//
// Deprecated: Please use types in: cloud.google.com/go/logging/apiv2/loggingpb
type LogEntry = src.LogEntry

// Additional information about a potentially long-running operation with
// which a log entry is associated.
//
// Deprecated: Please use types in: cloud.google.com/go/logging/apiv2/loggingpb
type LogEntryOperation = src.LogEntryOperation

// Additional information about the source code location that produced the log
// entry.
//
// Deprecated: Please use types in: cloud.google.com/go/logging/apiv2/loggingpb
type LogEntrySourceLocation = src.LogEntrySourceLocation
type LogEntry_JsonPayload = src.LogEntry_JsonPayload
type LogEntry_ProtoPayload = src.LogEntry_ProtoPayload
type LogEntry_TextPayload = src.LogEntry_TextPayload

// Specifies a set of log entries that are filtered out by a sink. If your
// Google Cloud resource receives a large volume of log entries, you can use
// exclusions to reduce your chargeable logs. Note that exclusions on
// organization-level and folder-level sinks don't apply to child resources.
// Note also that you cannot modify the _Required sink or exclude logs from it.
//
// Deprecated: Please use types in: cloud.google.com/go/logging/apiv2/loggingpb
type LogExclusion = src.LogExclusion

// Describes a logs-based metric. The value of the metric is the number of log
// entries that match a logs filter in a given time interval. Logs-based
// metrics can also be used to extract values from logs and create a
// distribution of the values. The distribution records the statistics of the
// extracted values along with an optional histogram of the values as specified
// by the bucket options.
//
// Deprecated: Please use types in: cloud.google.com/go/logging/apiv2/loggingpb
type LogMetric = src.LogMetric

// Logging API version.
//
// Deprecated: Please use types in: cloud.google.com/go/logging/apiv2/loggingpb
type LogMetric_ApiVersion = src.LogMetric_ApiVersion

// Describes a sink used to export log entries to one of the following
// destinations in any project: a Cloud Storage bucket, a BigQuery dataset, a
// Pub/Sub topic or a Cloud Logging log bucket. A logs filter controls which
// log entries are exported. The sink must be created within a project,
// organization, billing account, or folder.
//
// Deprecated: Please use types in: cloud.google.com/go/logging/apiv2/loggingpb
type LogSink = src.LogSink
type LogSink_BigqueryOptions = src.LogSink_BigqueryOptions

// Deprecated. This is unused.
//
// Deprecated: Please use types in: cloud.google.com/go/logging/apiv2/loggingpb
type LogSink_VersionFormat = src.LogSink_VersionFormat

// Additional information used to correlate multiple log entries. Used when a
// single LogEntry would exceed the Google Cloud Logging size limit and is
// split across multiple log entries.
//
// Deprecated: Please use types in: cloud.google.com/go/logging/apiv2/loggingpb
type LogSplit = src.LogSplit

// Describes a view over log entries in a bucket.
//
// Deprecated: Please use types in: cloud.google.com/go/logging/apiv2/loggingpb
type LogView = src.LogView

// LoggingServiceV2Client is the client API for LoggingServiceV2 service. For
// semantics around ctx use and closing/ending streaming RPCs, please refer to
// https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
//
// Deprecated: Please use types in: cloud.google.com/go/logging/apiv2/loggingpb
type LoggingServiceV2Client = src.LoggingServiceV2Client

// LoggingServiceV2Server is the server API for LoggingServiceV2 service.
//
// Deprecated: Please use types in: cloud.google.com/go/logging/apiv2/loggingpb
type LoggingServiceV2Server = src.LoggingServiceV2Server
type LoggingServiceV2_TailLogEntriesClient = src.LoggingServiceV2_TailLogEntriesClient
type LoggingServiceV2_TailLogEntriesServer = src.LoggingServiceV2_TailLogEntriesServer

// MetricsServiceV2Client is the client API for MetricsServiceV2 service. For
// semantics around ctx use and closing/ending streaming RPCs, please refer to
// https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
//
// Deprecated: Please use types in: cloud.google.com/go/logging/apiv2/loggingpb
type MetricsServiceV2Client = src.MetricsServiceV2Client

// MetricsServiceV2Server is the server API for MetricsServiceV2 service.
//
// Deprecated: Please use types in: cloud.google.com/go/logging/apiv2/loggingpb
type MetricsServiceV2Server = src.MetricsServiceV2Server

// List of different operation states. High level state of the operation. This
// is used to report the job's current state to the user. Once a long running
// operation is created, the current state of the operation can be queried even
// before the operation is finished and the final result is available.
//
// Deprecated: Please use types in: cloud.google.com/go/logging/apiv2/loggingpb
type OperationState = src.OperationState

// Describes the settings associated with a project, folder, organization,
// billing account, or flexible resource.
//
// Deprecated: Please use types in: cloud.google.com/go/logging/apiv2/loggingpb
type Settings = src.Settings

// The parameters to `TailLogEntries`.
//
// Deprecated: Please use types in: cloud.google.com/go/logging/apiv2/loggingpb
type TailLogEntriesRequest = src.TailLogEntriesRequest

// Result returned from `TailLogEntries`.
//
// Deprecated: Please use types in: cloud.google.com/go/logging/apiv2/loggingpb
type TailLogEntriesResponse = src.TailLogEntriesResponse

// Information about entries that were omitted from the session.
//
// Deprecated: Please use types in: cloud.google.com/go/logging/apiv2/loggingpb
type TailLogEntriesResponse_SuppressionInfo = src.TailLogEntriesResponse_SuppressionInfo

// An indicator of why entries were omitted.
//
// Deprecated: Please use types in: cloud.google.com/go/logging/apiv2/loggingpb
type TailLogEntriesResponse_SuppressionInfo_Reason = src.TailLogEntriesResponse_SuppressionInfo_Reason

// The parameters to `UndeleteBucket`.
//
// Deprecated: Please use types in: cloud.google.com/go/logging/apiv2/loggingpb
type UndeleteBucketRequest = src.UndeleteBucketRequest

// UnimplementedConfigServiceV2Server can be embedded to have forward
// compatible implementations.
//
// Deprecated: Please use types in: cloud.google.com/go/logging/apiv2/loggingpb
type UnimplementedConfigServiceV2Server = src.UnimplementedConfigServiceV2Server

// UnimplementedLoggingServiceV2Server can be embedded to have forward
// compatible implementations.
//
// Deprecated: Please use types in: cloud.google.com/go/logging/apiv2/loggingpb
type UnimplementedLoggingServiceV2Server = src.UnimplementedLoggingServiceV2Server

// UnimplementedMetricsServiceV2Server can be embedded to have forward
// compatible implementations.
//
// Deprecated: Please use types in: cloud.google.com/go/logging/apiv2/loggingpb
type UnimplementedMetricsServiceV2Server = src.UnimplementedMetricsServiceV2Server

// The parameters to `UpdateBucket`.
//
// Deprecated: Please use types in: cloud.google.com/go/logging/apiv2/loggingpb
type UpdateBucketRequest = src.UpdateBucketRequest

// The parameters to
// [UpdateCmekSettings][google.logging.v2.ConfigServiceV2.UpdateCmekSettings].
// See [Enabling CMEK for Log
// Router](https://cloud.google.com/logging/docs/routing/managed-encryption)
// for more information.
//
// Deprecated: Please use types in: cloud.google.com/go/logging/apiv2/loggingpb
type UpdateCmekSettingsRequest = src.UpdateCmekSettingsRequest

// The parameters to `UpdateExclusion`.
//
// Deprecated: Please use types in: cloud.google.com/go/logging/apiv2/loggingpb
type UpdateExclusionRequest = src.UpdateExclusionRequest

// The parameters to UpdateLogMetric.
//
// Deprecated: Please use types in: cloud.google.com/go/logging/apiv2/loggingpb
type UpdateLogMetricRequest = src.UpdateLogMetricRequest

// The parameters to
// [UpdateSettings][google.logging.v2.ConfigServiceV2.UpdateSettings]. See
// [Enabling CMEK for Log
// Router](https://cloud.google.com/logging/docs/routing/managed-encryption)
// for more information.
//
// Deprecated: Please use types in: cloud.google.com/go/logging/apiv2/loggingpb
type UpdateSettingsRequest = src.UpdateSettingsRequest

// The parameters to `UpdateSink`.
//
// Deprecated: Please use types in: cloud.google.com/go/logging/apiv2/loggingpb
type UpdateSinkRequest = src.UpdateSinkRequest

// The parameters to `UpdateView`.
//
// Deprecated: Please use types in: cloud.google.com/go/logging/apiv2/loggingpb
type UpdateViewRequest = src.UpdateViewRequest

// Error details for WriteLogEntries with partial success.
//
// Deprecated: Please use types in: cloud.google.com/go/logging/apiv2/loggingpb
type WriteLogEntriesPartialErrors = src.WriteLogEntriesPartialErrors

// The parameters to WriteLogEntries.
//
// Deprecated: Please use types in: cloud.google.com/go/logging/apiv2/loggingpb
type WriteLogEntriesRequest = src.WriteLogEntriesRequest

// Result returned from WriteLogEntries.
//
// Deprecated: Please use types in: cloud.google.com/go/logging/apiv2/loggingpb
type WriteLogEntriesResponse = src.WriteLogEntriesResponse

// Deprecated: Please use funcs in: cloud.google.com/go/logging/apiv2/loggingpb
func NewConfigServiceV2Client(cc grpc.ClientConnInterface) ConfigServiceV2Client {
	return src.NewConfigServiceV2Client(cc)
}

// Deprecated: Please use funcs in: cloud.google.com/go/logging/apiv2/loggingpb
func NewLoggingServiceV2Client(cc grpc.ClientConnInterface) LoggingServiceV2Client {
	return src.NewLoggingServiceV2Client(cc)
}

// Deprecated: Please use funcs in: cloud.google.com/go/logging/apiv2/loggingpb
func NewMetricsServiceV2Client(cc grpc.ClientConnInterface) MetricsServiceV2Client {
	return src.NewMetricsServiceV2Client(cc)
}

// Deprecated: Please use funcs in: cloud.google.com/go/logging/apiv2/loggingpb
func RegisterConfigServiceV2Server(s *grpc.Server, srv ConfigServiceV2Server) {
	src.RegisterConfigServiceV2Server(s, srv)
}

// Deprecated: Please use funcs in: cloud.google.com/go/logging/apiv2/loggingpb
func RegisterLoggingServiceV2Server(s *grpc.Server, srv LoggingServiceV2Server) {
	src.RegisterLoggingServiceV2Server(s, srv)
}

// Deprecated: Please use funcs in: cloud.google.com/go/logging/apiv2/loggingpb
func RegisterMetricsServiceV2Server(s *grpc.Server, srv MetricsServiceV2Server) {
	src.RegisterMetricsServiceV2Server(s, srv)
}
