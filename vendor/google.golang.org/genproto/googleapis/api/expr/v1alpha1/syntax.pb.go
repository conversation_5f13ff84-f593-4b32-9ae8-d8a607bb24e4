// Copyright 2025 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.26.0
// 	protoc        v4.24.4
// source: google/api/expr/v1alpha1/syntax.proto

package expr

import (
	reflect "reflect"
	sync "sync"

	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	durationpb "google.golang.org/protobuf/types/known/durationpb"
	structpb "google.golang.org/protobuf/types/known/structpb"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// CEL component specifier.
type SourceInfo_Extension_Component int32

const (
	// Unspecified, default.
	SourceInfo_Extension_COMPONENT_UNSPECIFIED SourceInfo_Extension_Component = 0
	// Parser. Converts a CEL string to an AST.
	SourceInfo_Extension_COMPONENT_PARSER SourceInfo_Extension_Component = 1
	// Type checker. Checks that references in an AST are defined and types
	// agree.
	SourceInfo_Extension_COMPONENT_TYPE_CHECKER SourceInfo_Extension_Component = 2
	// Runtime. Evaluates a parsed and optionally checked CEL AST against a
	// context.
	SourceInfo_Extension_COMPONENT_RUNTIME SourceInfo_Extension_Component = 3
)

// Enum value maps for SourceInfo_Extension_Component.
var (
	SourceInfo_Extension_Component_name = map[int32]string{
		0: "COMPONENT_UNSPECIFIED",
		1: "COMPONENT_PARSER",
		2: "COMPONENT_TYPE_CHECKER",
		3: "COMPONENT_RUNTIME",
	}
	SourceInfo_Extension_Component_value = map[string]int32{
		"COMPONENT_UNSPECIFIED":  0,
		"COMPONENT_PARSER":       1,
		"COMPONENT_TYPE_CHECKER": 2,
		"COMPONENT_RUNTIME":      3,
	}
)

func (x SourceInfo_Extension_Component) Enum() *SourceInfo_Extension_Component {
	p := new(SourceInfo_Extension_Component)
	*p = x
	return p
}

func (x SourceInfo_Extension_Component) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (SourceInfo_Extension_Component) Descriptor() protoreflect.EnumDescriptor {
	return file_google_api_expr_v1alpha1_syntax_proto_enumTypes[0].Descriptor()
}

func (SourceInfo_Extension_Component) Type() protoreflect.EnumType {
	return &file_google_api_expr_v1alpha1_syntax_proto_enumTypes[0]
}

func (x SourceInfo_Extension_Component) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use SourceInfo_Extension_Component.Descriptor instead.
func (SourceInfo_Extension_Component) EnumDescriptor() ([]byte, []int) {
	return file_google_api_expr_v1alpha1_syntax_proto_rawDescGZIP(), []int{3, 0, 0}
}

// An expression together with source information as returned by the parser.
type ParsedExpr struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The parsed expression.
	Expr *Expr `protobuf:"bytes,2,opt,name=expr,proto3" json:"expr,omitempty"`
	// The source info derived from input that generated the parsed `expr`.
	SourceInfo *SourceInfo `protobuf:"bytes,3,opt,name=source_info,json=sourceInfo,proto3" json:"source_info,omitempty"`
}

func (x *ParsedExpr) Reset() {
	*x = ParsedExpr{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_api_expr_v1alpha1_syntax_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ParsedExpr) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ParsedExpr) ProtoMessage() {}

func (x *ParsedExpr) ProtoReflect() protoreflect.Message {
	mi := &file_google_api_expr_v1alpha1_syntax_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ParsedExpr.ProtoReflect.Descriptor instead.
func (*ParsedExpr) Descriptor() ([]byte, []int) {
	return file_google_api_expr_v1alpha1_syntax_proto_rawDescGZIP(), []int{0}
}

func (x *ParsedExpr) GetExpr() *Expr {
	if x != nil {
		return x.Expr
	}
	return nil
}

func (x *ParsedExpr) GetSourceInfo() *SourceInfo {
	if x != nil {
		return x.SourceInfo
	}
	return nil
}

// An abstract representation of a common expression.
//
// Expressions are abstractly represented as a collection of identifiers,
// select statements, function calls, literals, and comprehensions. All
// operators with the exception of the '.' operator are modelled as function
// calls. This makes it easy to represent new operators into the existing AST.
//
// All references within expressions must resolve to a
// [Decl][google.api.expr.v1alpha1.Decl] provided at type-check for an
// expression to be valid. A reference may either be a bare identifier `name` or
// a qualified identifier `google.api.name`. References may either refer to a
// value or a function declaration.
//
// For example, the expression `google.api.name.startsWith('expr')` references
// the declaration `google.api.name` within a
// [Expr.Select][google.api.expr.v1alpha1.Expr.Select] expression, and the
// function declaration `startsWith`.
type Expr struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Required. An id assigned to this node by the parser which is unique in a
	// given expression tree. This is used to associate type information and other
	// attributes to a node in the parse tree.
	Id int64 `protobuf:"varint,2,opt,name=id,proto3" json:"id,omitempty"`
	// Required. Variants of expressions.
	//
	// Types that are assignable to ExprKind:
	//
	//	*Expr_ConstExpr
	//	*Expr_IdentExpr
	//	*Expr_SelectExpr
	//	*Expr_CallExpr
	//	*Expr_ListExpr
	//	*Expr_StructExpr
	//	*Expr_ComprehensionExpr
	ExprKind isExpr_ExprKind `protobuf_oneof:"expr_kind"`
}

func (x *Expr) Reset() {
	*x = Expr{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_api_expr_v1alpha1_syntax_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Expr) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Expr) ProtoMessage() {}

func (x *Expr) ProtoReflect() protoreflect.Message {
	mi := &file_google_api_expr_v1alpha1_syntax_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Expr.ProtoReflect.Descriptor instead.
func (*Expr) Descriptor() ([]byte, []int) {
	return file_google_api_expr_v1alpha1_syntax_proto_rawDescGZIP(), []int{1}
}

func (x *Expr) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (m *Expr) GetExprKind() isExpr_ExprKind {
	if m != nil {
		return m.ExprKind
	}
	return nil
}

func (x *Expr) GetConstExpr() *Constant {
	if x, ok := x.GetExprKind().(*Expr_ConstExpr); ok {
		return x.ConstExpr
	}
	return nil
}

func (x *Expr) GetIdentExpr() *Expr_Ident {
	if x, ok := x.GetExprKind().(*Expr_IdentExpr); ok {
		return x.IdentExpr
	}
	return nil
}

func (x *Expr) GetSelectExpr() *Expr_Select {
	if x, ok := x.GetExprKind().(*Expr_SelectExpr); ok {
		return x.SelectExpr
	}
	return nil
}

func (x *Expr) GetCallExpr() *Expr_Call {
	if x, ok := x.GetExprKind().(*Expr_CallExpr); ok {
		return x.CallExpr
	}
	return nil
}

func (x *Expr) GetListExpr() *Expr_CreateList {
	if x, ok := x.GetExprKind().(*Expr_ListExpr); ok {
		return x.ListExpr
	}
	return nil
}

func (x *Expr) GetStructExpr() *Expr_CreateStruct {
	if x, ok := x.GetExprKind().(*Expr_StructExpr); ok {
		return x.StructExpr
	}
	return nil
}

func (x *Expr) GetComprehensionExpr() *Expr_Comprehension {
	if x, ok := x.GetExprKind().(*Expr_ComprehensionExpr); ok {
		return x.ComprehensionExpr
	}
	return nil
}

type isExpr_ExprKind interface {
	isExpr_ExprKind()
}

type Expr_ConstExpr struct {
	// A literal expression.
	ConstExpr *Constant `protobuf:"bytes,3,opt,name=const_expr,json=constExpr,proto3,oneof"`
}

type Expr_IdentExpr struct {
	// An identifier expression.
	IdentExpr *Expr_Ident `protobuf:"bytes,4,opt,name=ident_expr,json=identExpr,proto3,oneof"`
}

type Expr_SelectExpr struct {
	// A field selection expression, e.g. `request.auth`.
	SelectExpr *Expr_Select `protobuf:"bytes,5,opt,name=select_expr,json=selectExpr,proto3,oneof"`
}

type Expr_CallExpr struct {
	// A call expression, including calls to predefined functions and operators.
	CallExpr *Expr_Call `protobuf:"bytes,6,opt,name=call_expr,json=callExpr,proto3,oneof"`
}

type Expr_ListExpr struct {
	// A list creation expression.
	ListExpr *Expr_CreateList `protobuf:"bytes,7,opt,name=list_expr,json=listExpr,proto3,oneof"`
}

type Expr_StructExpr struct {
	// A map or message creation expression.
	StructExpr *Expr_CreateStruct `protobuf:"bytes,8,opt,name=struct_expr,json=structExpr,proto3,oneof"`
}

type Expr_ComprehensionExpr struct {
	// A comprehension expression.
	ComprehensionExpr *Expr_Comprehension `protobuf:"bytes,9,opt,name=comprehension_expr,json=comprehensionExpr,proto3,oneof"`
}

func (*Expr_ConstExpr) isExpr_ExprKind() {}

func (*Expr_IdentExpr) isExpr_ExprKind() {}

func (*Expr_SelectExpr) isExpr_ExprKind() {}

func (*Expr_CallExpr) isExpr_ExprKind() {}

func (*Expr_ListExpr) isExpr_ExprKind() {}

func (*Expr_StructExpr) isExpr_ExprKind() {}

func (*Expr_ComprehensionExpr) isExpr_ExprKind() {}

// Represents a primitive literal.
//
// Named 'Constant' here for backwards compatibility.
//
// This is similar as the primitives supported in the well-known type
// `google.protobuf.Value`, but richer so it can represent CEL's full range of
// primitives.
//
// Lists and structs are not included as constants as these aggregate types may
// contain [Expr][google.api.expr.v1alpha1.Expr] elements which require
// evaluation and are thus not constant.
//
// Examples of literals include: `"hello"`, `b'bytes'`, `1u`, `4.2`, `-2`,
// `true`, `null`.
type Constant struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Required. The valid constant kinds.
	//
	// Types that are assignable to ConstantKind:
	//
	//	*Constant_NullValue
	//	*Constant_BoolValue
	//	*Constant_Int64Value
	//	*Constant_Uint64Value
	//	*Constant_DoubleValue
	//	*Constant_StringValue
	//	*Constant_BytesValue
	//	*Constant_DurationValue
	//	*Constant_TimestampValue
	ConstantKind isConstant_ConstantKind `protobuf_oneof:"constant_kind"`
}

func (x *Constant) Reset() {
	*x = Constant{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_api_expr_v1alpha1_syntax_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Constant) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Constant) ProtoMessage() {}

func (x *Constant) ProtoReflect() protoreflect.Message {
	mi := &file_google_api_expr_v1alpha1_syntax_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Constant.ProtoReflect.Descriptor instead.
func (*Constant) Descriptor() ([]byte, []int) {
	return file_google_api_expr_v1alpha1_syntax_proto_rawDescGZIP(), []int{2}
}

func (m *Constant) GetConstantKind() isConstant_ConstantKind {
	if m != nil {
		return m.ConstantKind
	}
	return nil
}

func (x *Constant) GetNullValue() structpb.NullValue {
	if x, ok := x.GetConstantKind().(*Constant_NullValue); ok {
		return x.NullValue
	}
	return structpb.NullValue_NULL_VALUE
}

func (x *Constant) GetBoolValue() bool {
	if x, ok := x.GetConstantKind().(*Constant_BoolValue); ok {
		return x.BoolValue
	}
	return false
}

func (x *Constant) GetInt64Value() int64 {
	if x, ok := x.GetConstantKind().(*Constant_Int64Value); ok {
		return x.Int64Value
	}
	return 0
}

func (x *Constant) GetUint64Value() uint64 {
	if x, ok := x.GetConstantKind().(*Constant_Uint64Value); ok {
		return x.Uint64Value
	}
	return 0
}

func (x *Constant) GetDoubleValue() float64 {
	if x, ok := x.GetConstantKind().(*Constant_DoubleValue); ok {
		return x.DoubleValue
	}
	return 0
}

func (x *Constant) GetStringValue() string {
	if x, ok := x.GetConstantKind().(*Constant_StringValue); ok {
		return x.StringValue
	}
	return ""
}

func (x *Constant) GetBytesValue() []byte {
	if x, ok := x.GetConstantKind().(*Constant_BytesValue); ok {
		return x.BytesValue
	}
	return nil
}

// Deprecated: Do not use.
func (x *Constant) GetDurationValue() *durationpb.Duration {
	if x, ok := x.GetConstantKind().(*Constant_DurationValue); ok {
		return x.DurationValue
	}
	return nil
}

// Deprecated: Do not use.
func (x *Constant) GetTimestampValue() *timestamppb.Timestamp {
	if x, ok := x.GetConstantKind().(*Constant_TimestampValue); ok {
		return x.TimestampValue
	}
	return nil
}

type isConstant_ConstantKind interface {
	isConstant_ConstantKind()
}

type Constant_NullValue struct {
	// null value.
	NullValue structpb.NullValue `protobuf:"varint,1,opt,name=null_value,json=nullValue,proto3,enum=google.protobuf.NullValue,oneof"`
}

type Constant_BoolValue struct {
	// boolean value.
	BoolValue bool `protobuf:"varint,2,opt,name=bool_value,json=boolValue,proto3,oneof"`
}

type Constant_Int64Value struct {
	// int64 value.
	Int64Value int64 `protobuf:"varint,3,opt,name=int64_value,json=int64Value,proto3,oneof"`
}

type Constant_Uint64Value struct {
	// uint64 value.
	Uint64Value uint64 `protobuf:"varint,4,opt,name=uint64_value,json=uint64Value,proto3,oneof"`
}

type Constant_DoubleValue struct {
	// double value.
	DoubleValue float64 `protobuf:"fixed64,5,opt,name=double_value,json=doubleValue,proto3,oneof"`
}

type Constant_StringValue struct {
	// string value.
	StringValue string `protobuf:"bytes,6,opt,name=string_value,json=stringValue,proto3,oneof"`
}

type Constant_BytesValue struct {
	// bytes value.
	BytesValue []byte `protobuf:"bytes,7,opt,name=bytes_value,json=bytesValue,proto3,oneof"`
}

type Constant_DurationValue struct {
	// protobuf.Duration value.
	//
	// Deprecated: duration is no longer considered a builtin cel type.
	//
	// Deprecated: Do not use.
	DurationValue *durationpb.Duration `protobuf:"bytes,8,opt,name=duration_value,json=durationValue,proto3,oneof"`
}

type Constant_TimestampValue struct {
	// protobuf.Timestamp value.
	//
	// Deprecated: timestamp is no longer considered a builtin cel type.
	//
	// Deprecated: Do not use.
	TimestampValue *timestamppb.Timestamp `protobuf:"bytes,9,opt,name=timestamp_value,json=timestampValue,proto3,oneof"`
}

func (*Constant_NullValue) isConstant_ConstantKind() {}

func (*Constant_BoolValue) isConstant_ConstantKind() {}

func (*Constant_Int64Value) isConstant_ConstantKind() {}

func (*Constant_Uint64Value) isConstant_ConstantKind() {}

func (*Constant_DoubleValue) isConstant_ConstantKind() {}

func (*Constant_StringValue) isConstant_ConstantKind() {}

func (*Constant_BytesValue) isConstant_ConstantKind() {}

func (*Constant_DurationValue) isConstant_ConstantKind() {}

func (*Constant_TimestampValue) isConstant_ConstantKind() {}

// Source information collected at parse time.
type SourceInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The syntax version of the source, e.g. `cel1`.
	SyntaxVersion string `protobuf:"bytes,1,opt,name=syntax_version,json=syntaxVersion,proto3" json:"syntax_version,omitempty"`
	// The location name. All position information attached to an expression is
	// relative to this location.
	//
	// The location could be a file, UI element, or similar. For example,
	// `acme/app/AnvilPolicy.cel`.
	Location string `protobuf:"bytes,2,opt,name=location,proto3" json:"location,omitempty"`
	// Monotonically increasing list of code point offsets where newlines
	// `\n` appear.
	//
	// The line number of a given position is the index `i` where for a given
	// `id` the `line_offsets[i] < id_positions[id] < line_offsets[i+1]`. The
	// column may be derivd from `id_positions[id] - line_offsets[i]`.
	LineOffsets []int32 `protobuf:"varint,3,rep,packed,name=line_offsets,json=lineOffsets,proto3" json:"line_offsets,omitempty"`
	// A map from the parse node id (e.g. `Expr.id`) to the code point offset
	// within the source.
	Positions map[int64]int32 `protobuf:"bytes,4,rep,name=positions,proto3" json:"positions,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"`
	// A map from the parse node id where a macro replacement was made to the
	// call `Expr` that resulted in a macro expansion.
	//
	// For example, `has(value.field)` is a function call that is replaced by a
	// `test_only` field selection in the AST. Likewise, the call
	// `list.exists(e, e > 10)` translates to a comprehension expression. The key
	// in the map corresponds to the expression id of the expanded macro, and the
	// value is the call `Expr` that was replaced.
	MacroCalls map[int64]*Expr `protobuf:"bytes,5,rep,name=macro_calls,json=macroCalls,proto3" json:"macro_calls,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	// A list of tags for extensions that were used while parsing or type checking
	// the source expression. For example, optimizations that require special
	// runtime support may be specified.
	//
	// These are used to check feature support between components in separate
	// implementations. This can be used to either skip redundant work or
	// report an error if the extension is unsupported.
	Extensions []*SourceInfo_Extension `protobuf:"bytes,6,rep,name=extensions,proto3" json:"extensions,omitempty"`
}

func (x *SourceInfo) Reset() {
	*x = SourceInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_api_expr_v1alpha1_syntax_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SourceInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SourceInfo) ProtoMessage() {}

func (x *SourceInfo) ProtoReflect() protoreflect.Message {
	mi := &file_google_api_expr_v1alpha1_syntax_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SourceInfo.ProtoReflect.Descriptor instead.
func (*SourceInfo) Descriptor() ([]byte, []int) {
	return file_google_api_expr_v1alpha1_syntax_proto_rawDescGZIP(), []int{3}
}

func (x *SourceInfo) GetSyntaxVersion() string {
	if x != nil {
		return x.SyntaxVersion
	}
	return ""
}

func (x *SourceInfo) GetLocation() string {
	if x != nil {
		return x.Location
	}
	return ""
}

func (x *SourceInfo) GetLineOffsets() []int32 {
	if x != nil {
		return x.LineOffsets
	}
	return nil
}

func (x *SourceInfo) GetPositions() map[int64]int32 {
	if x != nil {
		return x.Positions
	}
	return nil
}

func (x *SourceInfo) GetMacroCalls() map[int64]*Expr {
	if x != nil {
		return x.MacroCalls
	}
	return nil
}

func (x *SourceInfo) GetExtensions() []*SourceInfo_Extension {
	if x != nil {
		return x.Extensions
	}
	return nil
}

// A specific position in source.
type SourcePosition struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The soucre location name (e.g. file name).
	Location string `protobuf:"bytes,1,opt,name=location,proto3" json:"location,omitempty"`
	// The UTF-8 code unit offset.
	Offset int32 `protobuf:"varint,2,opt,name=offset,proto3" json:"offset,omitempty"`
	// The 1-based index of the starting line in the source text
	// where the issue occurs, or 0 if unknown.
	Line int32 `protobuf:"varint,3,opt,name=line,proto3" json:"line,omitempty"`
	// The 0-based index of the starting position within the line of source text
	// where the issue occurs.  Only meaningful if line is nonzero.
	Column int32 `protobuf:"varint,4,opt,name=column,proto3" json:"column,omitempty"`
}

func (x *SourcePosition) Reset() {
	*x = SourcePosition{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_api_expr_v1alpha1_syntax_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SourcePosition) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SourcePosition) ProtoMessage() {}

func (x *SourcePosition) ProtoReflect() protoreflect.Message {
	mi := &file_google_api_expr_v1alpha1_syntax_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SourcePosition.ProtoReflect.Descriptor instead.
func (*SourcePosition) Descriptor() ([]byte, []int) {
	return file_google_api_expr_v1alpha1_syntax_proto_rawDescGZIP(), []int{4}
}

func (x *SourcePosition) GetLocation() string {
	if x != nil {
		return x.Location
	}
	return ""
}

func (x *SourcePosition) GetOffset() int32 {
	if x != nil {
		return x.Offset
	}
	return 0
}

func (x *SourcePosition) GetLine() int32 {
	if x != nil {
		return x.Line
	}
	return 0
}

func (x *SourcePosition) GetColumn() int32 {
	if x != nil {
		return x.Column
	}
	return 0
}

// An identifier expression. e.g. `request`.
type Expr_Ident struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Required. Holds a single, unqualified identifier, possibly preceded by a
	// '.'.
	//
	// Qualified names are represented by the
	// [Expr.Select][google.api.expr.v1alpha1.Expr.Select] expression.
	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
}

func (x *Expr_Ident) Reset() {
	*x = Expr_Ident{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_api_expr_v1alpha1_syntax_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Expr_Ident) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Expr_Ident) ProtoMessage() {}

func (x *Expr_Ident) ProtoReflect() protoreflect.Message {
	mi := &file_google_api_expr_v1alpha1_syntax_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Expr_Ident.ProtoReflect.Descriptor instead.
func (*Expr_Ident) Descriptor() ([]byte, []int) {
	return file_google_api_expr_v1alpha1_syntax_proto_rawDescGZIP(), []int{1, 0}
}

func (x *Expr_Ident) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

// A field selection expression. e.g. `request.auth`.
type Expr_Select struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Required. The target of the selection expression.
	//
	// For example, in the select expression `request.auth`, the `request`
	// portion of the expression is the `operand`.
	Operand *Expr `protobuf:"bytes,1,opt,name=operand,proto3" json:"operand,omitempty"`
	// Required. The name of the field to select.
	//
	// For example, in the select expression `request.auth`, the `auth` portion
	// of the expression would be the `field`.
	Field string `protobuf:"bytes,2,opt,name=field,proto3" json:"field,omitempty"`
	// Whether the select is to be interpreted as a field presence test.
	//
	// This results from the macro `has(request.auth)`.
	TestOnly bool `protobuf:"varint,3,opt,name=test_only,json=testOnly,proto3" json:"test_only,omitempty"`
}

func (x *Expr_Select) Reset() {
	*x = Expr_Select{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_api_expr_v1alpha1_syntax_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Expr_Select) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Expr_Select) ProtoMessage() {}

func (x *Expr_Select) ProtoReflect() protoreflect.Message {
	mi := &file_google_api_expr_v1alpha1_syntax_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Expr_Select.ProtoReflect.Descriptor instead.
func (*Expr_Select) Descriptor() ([]byte, []int) {
	return file_google_api_expr_v1alpha1_syntax_proto_rawDescGZIP(), []int{1, 1}
}

func (x *Expr_Select) GetOperand() *Expr {
	if x != nil {
		return x.Operand
	}
	return nil
}

func (x *Expr_Select) GetField() string {
	if x != nil {
		return x.Field
	}
	return ""
}

func (x *Expr_Select) GetTestOnly() bool {
	if x != nil {
		return x.TestOnly
	}
	return false
}

// A call expression, including calls to predefined functions and operators.
//
// For example, `value == 10`, `size(map_value)`.
type Expr_Call struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The target of an method call-style expression. For example, `x` in
	// `x.f()`.
	Target *Expr `protobuf:"bytes,1,opt,name=target,proto3" json:"target,omitempty"`
	// Required. The name of the function or method being called.
	Function string `protobuf:"bytes,2,opt,name=function,proto3" json:"function,omitempty"`
	// The arguments.
	Args []*Expr `protobuf:"bytes,3,rep,name=args,proto3" json:"args,omitempty"`
}

func (x *Expr_Call) Reset() {
	*x = Expr_Call{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_api_expr_v1alpha1_syntax_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Expr_Call) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Expr_Call) ProtoMessage() {}

func (x *Expr_Call) ProtoReflect() protoreflect.Message {
	mi := &file_google_api_expr_v1alpha1_syntax_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Expr_Call.ProtoReflect.Descriptor instead.
func (*Expr_Call) Descriptor() ([]byte, []int) {
	return file_google_api_expr_v1alpha1_syntax_proto_rawDescGZIP(), []int{1, 2}
}

func (x *Expr_Call) GetTarget() *Expr {
	if x != nil {
		return x.Target
	}
	return nil
}

func (x *Expr_Call) GetFunction() string {
	if x != nil {
		return x.Function
	}
	return ""
}

func (x *Expr_Call) GetArgs() []*Expr {
	if x != nil {
		return x.Args
	}
	return nil
}

// A list creation expression.
//
// Lists may either be homogenous, e.g. `[1, 2, 3]`, or heterogeneous, e.g.
// `dyn([1, 'hello', 2.0])`
type Expr_CreateList struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The elements part of the list.
	Elements []*Expr `protobuf:"bytes,1,rep,name=elements,proto3" json:"elements,omitempty"`
	// The indices within the elements list which are marked as optional
	// elements.
	//
	// When an optional-typed value is present, the value it contains
	// is included in the list. If the optional-typed value is absent, the list
	// element is omitted from the CreateList result.
	OptionalIndices []int32 `protobuf:"varint,2,rep,packed,name=optional_indices,json=optionalIndices,proto3" json:"optional_indices,omitempty"`
}

func (x *Expr_CreateList) Reset() {
	*x = Expr_CreateList{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_api_expr_v1alpha1_syntax_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Expr_CreateList) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Expr_CreateList) ProtoMessage() {}

func (x *Expr_CreateList) ProtoReflect() protoreflect.Message {
	mi := &file_google_api_expr_v1alpha1_syntax_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Expr_CreateList.ProtoReflect.Descriptor instead.
func (*Expr_CreateList) Descriptor() ([]byte, []int) {
	return file_google_api_expr_v1alpha1_syntax_proto_rawDescGZIP(), []int{1, 3}
}

func (x *Expr_CreateList) GetElements() []*Expr {
	if x != nil {
		return x.Elements
	}
	return nil
}

func (x *Expr_CreateList) GetOptionalIndices() []int32 {
	if x != nil {
		return x.OptionalIndices
	}
	return nil
}

// A map or message creation expression.
//
// Maps are constructed as `{'key_name': 'value'}`. Message construction is
// similar, but prefixed with a type name and composed of field ids:
// `types.MyType{field_id: 'value'}`.
type Expr_CreateStruct struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The type name of the message to be created, empty when creating map
	// literals.
	MessageName string `protobuf:"bytes,1,opt,name=message_name,json=messageName,proto3" json:"message_name,omitempty"`
	// The entries in the creation expression.
	Entries []*Expr_CreateStruct_Entry `protobuf:"bytes,2,rep,name=entries,proto3" json:"entries,omitempty"`
}

func (x *Expr_CreateStruct) Reset() {
	*x = Expr_CreateStruct{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_api_expr_v1alpha1_syntax_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Expr_CreateStruct) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Expr_CreateStruct) ProtoMessage() {}

func (x *Expr_CreateStruct) ProtoReflect() protoreflect.Message {
	mi := &file_google_api_expr_v1alpha1_syntax_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Expr_CreateStruct.ProtoReflect.Descriptor instead.
func (*Expr_CreateStruct) Descriptor() ([]byte, []int) {
	return file_google_api_expr_v1alpha1_syntax_proto_rawDescGZIP(), []int{1, 4}
}

func (x *Expr_CreateStruct) GetMessageName() string {
	if x != nil {
		return x.MessageName
	}
	return ""
}

func (x *Expr_CreateStruct) GetEntries() []*Expr_CreateStruct_Entry {
	if x != nil {
		return x.Entries
	}
	return nil
}

// A comprehension expression applied to a list or map.
//
// Comprehensions are not part of the core syntax, but enabled with macros.
// A macro matches a specific call signature within a parsed AST and replaces
// the call with an alternate AST block. Macro expansion happens at parse
// time.
//
// The following macros are supported within CEL:
//
// Aggregate type macros may be applied to all elements in a list or all keys
// in a map:
//
//   - `all`, `exists`, `exists_one` -  test a predicate expression against
//     the inputs and return `true` if the predicate is satisfied for all,
//     any, or only one value `list.all(x, x < 10)`.
//   - `filter` - test a predicate expression against the inputs and return
//     the subset of elements which satisfy the predicate:
//     `payments.filter(p, p > 1000)`.
//   - `map` - apply an expression to all elements in the input and return the
//     output aggregate type: `[1, 2, 3].map(i, i * i)`.
//
// The `has(m.x)` macro tests whether the property `x` is present in struct
// `m`. The semantics of this macro depend on the type of `m`. For proto2
// messages `has(m.x)` is defined as 'defined, but not set`. For proto3, the
// macro tests whether the property is set to its default. For map and struct
// types, the macro tests whether the property `x` is defined on `m`.
//
// Comprehensions for the standard environment macros evaluation can be best
// visualized as the following pseudocode:
//
// ```
// let `accu_var` = `accu_init`
//
//	for (let `iter_var` in `iter_range`) {
//	  if (!`loop_condition`) {
//	    break
//	  }
//	  `accu_var` = `loop_step`
//	}
//
// return `result`
// ```
//
// Comprehensions for the optional V2 macros which support map-to-map
// translation differ slightly from the standard environment macros in that
// they expose both the key or index in addition to the value for each list
// or map entry:
//
// ```
// let `accu_var` = `accu_init`
//
//	for (let `iter_var`, `iter_var2` in `iter_range`) {
//	  if (!`loop_condition`) {
//	    break
//	  }
//	  `accu_var` = `loop_step`
//	}
//
// return `result`
// ```
type Expr_Comprehension struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The name of the first iteration variable.
	// When the iter_range is a list, this variable is the list element.
	// When the iter_range is a map, this variable is the map entry key.
	IterVar string `protobuf:"bytes,1,opt,name=iter_var,json=iterVar,proto3" json:"iter_var,omitempty"`
	// The name of the second iteration variable, empty if not set.
	// When the iter_range is a list, this variable is the integer index.
	// When the iter_range is a map, this variable is the map entry value.
	// This field is only set for comprehension v2 macros.
	IterVar2 string `protobuf:"bytes,8,opt,name=iter_var2,json=iterVar2,proto3" json:"iter_var2,omitempty"`
	// The range over which the comprehension iterates.
	IterRange *Expr `protobuf:"bytes,2,opt,name=iter_range,json=iterRange,proto3" json:"iter_range,omitempty"`
	// The name of the variable used for accumulation of the result.
	AccuVar string `protobuf:"bytes,3,opt,name=accu_var,json=accuVar,proto3" json:"accu_var,omitempty"`
	// The initial value of the accumulator.
	AccuInit *Expr `protobuf:"bytes,4,opt,name=accu_init,json=accuInit,proto3" json:"accu_init,omitempty"`
	// An expression which can contain iter_var, iter_var2, and accu_var.
	//
	// Returns false when the result has been computed and may be used as
	// a hint to short-circuit the remainder of the comprehension.
	LoopCondition *Expr `protobuf:"bytes,5,opt,name=loop_condition,json=loopCondition,proto3" json:"loop_condition,omitempty"`
	// An expression which can contain iter_var, iter_var2, and accu_var.
	//
	// Computes the next value of accu_var.
	LoopStep *Expr `protobuf:"bytes,6,opt,name=loop_step,json=loopStep,proto3" json:"loop_step,omitempty"`
	// An expression which can contain accu_var.
	//
	// Computes the result.
	Result *Expr `protobuf:"bytes,7,opt,name=result,proto3" json:"result,omitempty"`
}

func (x *Expr_Comprehension) Reset() {
	*x = Expr_Comprehension{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_api_expr_v1alpha1_syntax_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Expr_Comprehension) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Expr_Comprehension) ProtoMessage() {}

func (x *Expr_Comprehension) ProtoReflect() protoreflect.Message {
	mi := &file_google_api_expr_v1alpha1_syntax_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Expr_Comprehension.ProtoReflect.Descriptor instead.
func (*Expr_Comprehension) Descriptor() ([]byte, []int) {
	return file_google_api_expr_v1alpha1_syntax_proto_rawDescGZIP(), []int{1, 5}
}

func (x *Expr_Comprehension) GetIterVar() string {
	if x != nil {
		return x.IterVar
	}
	return ""
}

func (x *Expr_Comprehension) GetIterVar2() string {
	if x != nil {
		return x.IterVar2
	}
	return ""
}

func (x *Expr_Comprehension) GetIterRange() *Expr {
	if x != nil {
		return x.IterRange
	}
	return nil
}

func (x *Expr_Comprehension) GetAccuVar() string {
	if x != nil {
		return x.AccuVar
	}
	return ""
}

func (x *Expr_Comprehension) GetAccuInit() *Expr {
	if x != nil {
		return x.AccuInit
	}
	return nil
}

func (x *Expr_Comprehension) GetLoopCondition() *Expr {
	if x != nil {
		return x.LoopCondition
	}
	return nil
}

func (x *Expr_Comprehension) GetLoopStep() *Expr {
	if x != nil {
		return x.LoopStep
	}
	return nil
}

func (x *Expr_Comprehension) GetResult() *Expr {
	if x != nil {
		return x.Result
	}
	return nil
}

// Represents an entry.
type Expr_CreateStruct_Entry struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Required. An id assigned to this node by the parser which is unique
	// in a given expression tree. This is used to associate type
	// information and other attributes to the node.
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// The `Entry` key kinds.
	//
	// Types that are assignable to KeyKind:
	//
	//	*Expr_CreateStruct_Entry_FieldKey
	//	*Expr_CreateStruct_Entry_MapKey
	KeyKind isExpr_CreateStruct_Entry_KeyKind `protobuf_oneof:"key_kind"`
	// Required. The value assigned to the key.
	//
	// If the optional_entry field is true, the expression must resolve to an
	// optional-typed value. If the optional value is present, the key will be
	// set; however, if the optional value is absent, the key will be unset.
	Value *Expr `protobuf:"bytes,4,opt,name=value,proto3" json:"value,omitempty"`
	// Whether the key-value pair is optional.
	OptionalEntry bool `protobuf:"varint,5,opt,name=optional_entry,json=optionalEntry,proto3" json:"optional_entry,omitempty"`
}

func (x *Expr_CreateStruct_Entry) Reset() {
	*x = Expr_CreateStruct_Entry{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_api_expr_v1alpha1_syntax_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Expr_CreateStruct_Entry) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Expr_CreateStruct_Entry) ProtoMessage() {}

func (x *Expr_CreateStruct_Entry) ProtoReflect() protoreflect.Message {
	mi := &file_google_api_expr_v1alpha1_syntax_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Expr_CreateStruct_Entry.ProtoReflect.Descriptor instead.
func (*Expr_CreateStruct_Entry) Descriptor() ([]byte, []int) {
	return file_google_api_expr_v1alpha1_syntax_proto_rawDescGZIP(), []int{1, 4, 0}
}

func (x *Expr_CreateStruct_Entry) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (m *Expr_CreateStruct_Entry) GetKeyKind() isExpr_CreateStruct_Entry_KeyKind {
	if m != nil {
		return m.KeyKind
	}
	return nil
}

func (x *Expr_CreateStruct_Entry) GetFieldKey() string {
	if x, ok := x.GetKeyKind().(*Expr_CreateStruct_Entry_FieldKey); ok {
		return x.FieldKey
	}
	return ""
}

func (x *Expr_CreateStruct_Entry) GetMapKey() *Expr {
	if x, ok := x.GetKeyKind().(*Expr_CreateStruct_Entry_MapKey); ok {
		return x.MapKey
	}
	return nil
}

func (x *Expr_CreateStruct_Entry) GetValue() *Expr {
	if x != nil {
		return x.Value
	}
	return nil
}

func (x *Expr_CreateStruct_Entry) GetOptionalEntry() bool {
	if x != nil {
		return x.OptionalEntry
	}
	return false
}

type isExpr_CreateStruct_Entry_KeyKind interface {
	isExpr_CreateStruct_Entry_KeyKind()
}

type Expr_CreateStruct_Entry_FieldKey struct {
	// The field key for a message creator statement.
	FieldKey string `protobuf:"bytes,2,opt,name=field_key,json=fieldKey,proto3,oneof"`
}

type Expr_CreateStruct_Entry_MapKey struct {
	// The key expression for a map creation statement.
	MapKey *Expr `protobuf:"bytes,3,opt,name=map_key,json=mapKey,proto3,oneof"`
}

func (*Expr_CreateStruct_Entry_FieldKey) isExpr_CreateStruct_Entry_KeyKind() {}

func (*Expr_CreateStruct_Entry_MapKey) isExpr_CreateStruct_Entry_KeyKind() {}

// An extension that was requested for the source expression.
type SourceInfo_Extension struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Identifier for the extension. Example: constant_folding
	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	// If set, the listed components must understand the extension for the
	// expression to evaluate correctly.
	//
	// This field has set semantics, repeated values should be deduplicated.
	AffectedComponents []SourceInfo_Extension_Component `protobuf:"varint,2,rep,packed,name=affected_components,json=affectedComponents,proto3,enum=google.api.expr.v1alpha1.SourceInfo_Extension_Component" json:"affected_components,omitempty"`
	// Version info. May be skipped if it isn't meaningful for the extension.
	// (for example constant_folding might always be v0.0).
	Version *SourceInfo_Extension_Version `protobuf:"bytes,3,opt,name=version,proto3" json:"version,omitempty"`
}

func (x *SourceInfo_Extension) Reset() {
	*x = SourceInfo_Extension{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_api_expr_v1alpha1_syntax_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SourceInfo_Extension) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SourceInfo_Extension) ProtoMessage() {}

func (x *SourceInfo_Extension) ProtoReflect() protoreflect.Message {
	mi := &file_google_api_expr_v1alpha1_syntax_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SourceInfo_Extension.ProtoReflect.Descriptor instead.
func (*SourceInfo_Extension) Descriptor() ([]byte, []int) {
	return file_google_api_expr_v1alpha1_syntax_proto_rawDescGZIP(), []int{3, 0}
}

func (x *SourceInfo_Extension) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *SourceInfo_Extension) GetAffectedComponents() []SourceInfo_Extension_Component {
	if x != nil {
		return x.AffectedComponents
	}
	return nil
}

func (x *SourceInfo_Extension) GetVersion() *SourceInfo_Extension_Version {
	if x != nil {
		return x.Version
	}
	return nil
}

// Version
type SourceInfo_Extension_Version struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Major version changes indicate different required support level from
	// the required components.
	Major int64 `protobuf:"varint,1,opt,name=major,proto3" json:"major,omitempty"`
	// Minor version changes must not change the observed behavior from
	// existing implementations, but may be provided informationally.
	Minor int64 `protobuf:"varint,2,opt,name=minor,proto3" json:"minor,omitempty"`
}

func (x *SourceInfo_Extension_Version) Reset() {
	*x = SourceInfo_Extension_Version{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_api_expr_v1alpha1_syntax_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SourceInfo_Extension_Version) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SourceInfo_Extension_Version) ProtoMessage() {}

func (x *SourceInfo_Extension_Version) ProtoReflect() protoreflect.Message {
	mi := &file_google_api_expr_v1alpha1_syntax_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SourceInfo_Extension_Version.ProtoReflect.Descriptor instead.
func (*SourceInfo_Extension_Version) Descriptor() ([]byte, []int) {
	return file_google_api_expr_v1alpha1_syntax_proto_rawDescGZIP(), []int{3, 0, 0}
}

func (x *SourceInfo_Extension_Version) GetMajor() int64 {
	if x != nil {
		return x.Major
	}
	return 0
}

func (x *SourceInfo_Extension_Version) GetMinor() int64 {
	if x != nil {
		return x.Minor
	}
	return 0
}

var File_google_api_expr_v1alpha1_syntax_proto protoreflect.FileDescriptor

var file_google_api_expr_v1alpha1_syntax_proto_rawDesc = []byte{
	0x0a, 0x25, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x65, 0x78, 0x70,
	0x72, 0x2f, 0x76, 0x31, 0x61, 0x6c, 0x70, 0x68, 0x61, 0x31, 0x2f, 0x73, 0x79, 0x6e, 0x74, 0x61,
	0x78, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x18, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x65, 0x78, 0x70, 0x72, 0x2e, 0x76, 0x31, 0x61, 0x6c, 0x70, 0x68, 0x61,
	0x31, 0x1a, 0x1e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62,
	0x75, 0x66, 0x2f, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x1a, 0x1c, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62,
	0x75, 0x66, 0x2f, 0x73, 0x74, 0x72, 0x75, 0x63, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a,
	0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66,
	0x2f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x22, 0x87, 0x01, 0x0a, 0x0a, 0x50, 0x61, 0x72, 0x73, 0x65, 0x64, 0x45, 0x78, 0x70, 0x72, 0x12,
	0x32, 0x0a, 0x04, 0x65, 0x78, 0x70, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x65, 0x78, 0x70, 0x72, 0x2e,
	0x76, 0x31, 0x61, 0x6c, 0x70, 0x68, 0x61, 0x31, 0x2e, 0x45, 0x78, 0x70, 0x72, 0x52, 0x04, 0x65,
	0x78, 0x70, 0x72, 0x12, 0x45, 0x0a, 0x0b, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x69, 0x6e,
	0x66, 0x6f, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x24, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x65, 0x78, 0x70, 0x72, 0x2e, 0x76, 0x31, 0x61, 0x6c, 0x70,
	0x68, 0x61, 0x31, 0x2e, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x0a,
	0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x22, 0xcb, 0x0d, 0x0a, 0x04, 0x45,
	0x78, 0x70, 0x72, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x02, 0x69, 0x64, 0x12, 0x43, 0x0a, 0x0a, 0x63, 0x6f, 0x6e, 0x73, 0x74, 0x5f, 0x65, 0x78, 0x70,
	0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x65, 0x78, 0x70, 0x72, 0x2e, 0x76, 0x31, 0x61, 0x6c, 0x70, 0x68,
	0x61, 0x31, 0x2e, 0x43, 0x6f, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x74, 0x48, 0x00, 0x52, 0x09, 0x63,
	0x6f, 0x6e, 0x73, 0x74, 0x45, 0x78, 0x70, 0x72, 0x12, 0x45, 0x0a, 0x0a, 0x69, 0x64, 0x65, 0x6e,
	0x74, 0x5f, 0x65, 0x78, 0x70, 0x72, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x24, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x65, 0x78, 0x70, 0x72, 0x2e, 0x76,
	0x31, 0x61, 0x6c, 0x70, 0x68, 0x61, 0x31, 0x2e, 0x45, 0x78, 0x70, 0x72, 0x2e, 0x49, 0x64, 0x65,
	0x6e, 0x74, 0x48, 0x00, 0x52, 0x09, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x45, 0x78, 0x70, 0x72, 0x12,
	0x48, 0x0a, 0x0b, 0x73, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x5f, 0x65, 0x78, 0x70, 0x72, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x65, 0x78, 0x70, 0x72, 0x2e, 0x76, 0x31, 0x61, 0x6c, 0x70, 0x68, 0x61, 0x31, 0x2e,
	0x45, 0x78, 0x70, 0x72, 0x2e, 0x53, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x48, 0x00, 0x52, 0x0a, 0x73,
	0x65, 0x6c, 0x65, 0x63, 0x74, 0x45, 0x78, 0x70, 0x72, 0x12, 0x42, 0x0a, 0x09, 0x63, 0x61, 0x6c,
	0x6c, 0x5f, 0x65, 0x78, 0x70, 0x72, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x65, 0x78, 0x70, 0x72, 0x2e, 0x76,
	0x31, 0x61, 0x6c, 0x70, 0x68, 0x61, 0x31, 0x2e, 0x45, 0x78, 0x70, 0x72, 0x2e, 0x43, 0x61, 0x6c,
	0x6c, 0x48, 0x00, 0x52, 0x08, 0x63, 0x61, 0x6c, 0x6c, 0x45, 0x78, 0x70, 0x72, 0x12, 0x48, 0x0a,
	0x09, 0x6c, 0x69, 0x73, 0x74, 0x5f, 0x65, 0x78, 0x70, 0x72, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x29, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x65, 0x78,
	0x70, 0x72, 0x2e, 0x76, 0x31, 0x61, 0x6c, 0x70, 0x68, 0x61, 0x31, 0x2e, 0x45, 0x78, 0x70, 0x72,
	0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x48, 0x00, 0x52, 0x08, 0x6c,
	0x69, 0x73, 0x74, 0x45, 0x78, 0x70, 0x72, 0x12, 0x4e, 0x0a, 0x0b, 0x73, 0x74, 0x72, 0x75, 0x63,
	0x74, 0x5f, 0x65, 0x78, 0x70, 0x72, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2b, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x65, 0x78, 0x70, 0x72, 0x2e, 0x76,
	0x31, 0x61, 0x6c, 0x70, 0x68, 0x61, 0x31, 0x2e, 0x45, 0x78, 0x70, 0x72, 0x2e, 0x43, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x53, 0x74, 0x72, 0x75, 0x63, 0x74, 0x48, 0x00, 0x52, 0x0a, 0x73, 0x74, 0x72,
	0x75, 0x63, 0x74, 0x45, 0x78, 0x70, 0x72, 0x12, 0x5d, 0x0a, 0x12, 0x63, 0x6f, 0x6d, 0x70, 0x72,
	0x65, 0x68, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x65, 0x78, 0x70, 0x72, 0x18, 0x09, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x2c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x65, 0x78, 0x70, 0x72, 0x2e, 0x76, 0x31, 0x61, 0x6c, 0x70, 0x68, 0x61, 0x31, 0x2e, 0x45,
	0x78, 0x70, 0x72, 0x2e, 0x43, 0x6f, 0x6d, 0x70, 0x72, 0x65, 0x68, 0x65, 0x6e, 0x73, 0x69, 0x6f,
	0x6e, 0x48, 0x00, 0x52, 0x11, 0x63, 0x6f, 0x6d, 0x70, 0x72, 0x65, 0x68, 0x65, 0x6e, 0x73, 0x69,
	0x6f, 0x6e, 0x45, 0x78, 0x70, 0x72, 0x1a, 0x1b, 0x0a, 0x05, 0x49, 0x64, 0x65, 0x6e, 0x74, 0x12,
	0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e,
	0x61, 0x6d, 0x65, 0x1a, 0x75, 0x0a, 0x06, 0x53, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x12, 0x38, 0x0a,
	0x07, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x6e, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x65, 0x78, 0x70, 0x72,
	0x2e, 0x76, 0x31, 0x61, 0x6c, 0x70, 0x68, 0x61, 0x31, 0x2e, 0x45, 0x78, 0x70, 0x72, 0x52, 0x07,
	0x6f, 0x70, 0x65, 0x72, 0x61, 0x6e, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x66, 0x69, 0x65, 0x6c, 0x64,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x12, 0x1b, 0x0a,
	0x09, 0x74, 0x65, 0x73, 0x74, 0x5f, 0x6f, 0x6e, 0x6c, 0x79, 0x18, 0x03, 0x20, 0x01, 0x28, 0x08,
	0x52, 0x08, 0x74, 0x65, 0x73, 0x74, 0x4f, 0x6e, 0x6c, 0x79, 0x1a, 0x8e, 0x01, 0x0a, 0x04, 0x43,
	0x61, 0x6c, 0x6c, 0x12, 0x36, 0x0a, 0x06, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x65, 0x78, 0x70, 0x72, 0x2e, 0x76, 0x31, 0x61, 0x6c, 0x70, 0x68, 0x61, 0x31, 0x2e, 0x45,
	0x78, 0x70, 0x72, 0x52, 0x06, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x12, 0x1a, 0x0a, 0x08, 0x66,
	0x75, 0x6e, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x66,
	0x75, 0x6e, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x32, 0x0a, 0x04, 0x61, 0x72, 0x67, 0x73, 0x18,
	0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x65, 0x78, 0x70, 0x72, 0x2e, 0x76, 0x31, 0x61, 0x6c, 0x70, 0x68, 0x61, 0x31,
	0x2e, 0x45, 0x78, 0x70, 0x72, 0x52, 0x04, 0x61, 0x72, 0x67, 0x73, 0x1a, 0x73, 0x0a, 0x0a, 0x43,
	0x72, 0x65, 0x61, 0x74, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x3a, 0x0a, 0x08, 0x65, 0x6c, 0x65,
	0x6d, 0x65, 0x6e, 0x74, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x65, 0x78, 0x70, 0x72, 0x2e, 0x76, 0x31,
	0x61, 0x6c, 0x70, 0x68, 0x61, 0x31, 0x2e, 0x45, 0x78, 0x70, 0x72, 0x52, 0x08, 0x65, 0x6c, 0x65,
	0x6d, 0x65, 0x6e, 0x74, 0x73, 0x12, 0x29, 0x0a, 0x10, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61,
	0x6c, 0x5f, 0x69, 0x6e, 0x64, 0x69, 0x63, 0x65, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x05, 0x52,
	0x0f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x49, 0x6e, 0x64, 0x69, 0x63, 0x65, 0x73,
	0x1a, 0xdb, 0x02, 0x0a, 0x0c, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x53, 0x74, 0x72, 0x75, 0x63,
	0x74, 0x12, 0x21, 0x0a, 0x0c, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x5f, 0x6e, 0x61, 0x6d,
	0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65,
	0x4e, 0x61, 0x6d, 0x65, 0x12, 0x4b, 0x0a, 0x07, 0x65, 0x6e, 0x74, 0x72, 0x69, 0x65, 0x73, 0x18,
	0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x31, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x65, 0x78, 0x70, 0x72, 0x2e, 0x76, 0x31, 0x61, 0x6c, 0x70, 0x68, 0x61, 0x31,
	0x2e, 0x45, 0x78, 0x70, 0x72, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x53, 0x74, 0x72, 0x75,
	0x63, 0x74, 0x2e, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x07, 0x65, 0x6e, 0x74, 0x72, 0x69, 0x65,
	0x73, 0x1a, 0xda, 0x01, 0x0a, 0x05, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x0e, 0x0a, 0x02, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x1d, 0x0a, 0x09, 0x66,
	0x69, 0x65, 0x6c, 0x64, 0x5f, 0x6b, 0x65, 0x79, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00,
	0x52, 0x08, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x4b, 0x65, 0x79, 0x12, 0x39, 0x0a, 0x07, 0x6d, 0x61,
	0x70, 0x5f, 0x6b, 0x65, 0x79, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x65, 0x78, 0x70, 0x72, 0x2e, 0x76, 0x31,
	0x61, 0x6c, 0x70, 0x68, 0x61, 0x31, 0x2e, 0x45, 0x78, 0x70, 0x72, 0x48, 0x00, 0x52, 0x06, 0x6d,
	0x61, 0x70, 0x4b, 0x65, 0x79, 0x12, 0x34, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x65, 0x78, 0x70, 0x72, 0x2e, 0x76, 0x31, 0x61, 0x6c, 0x70, 0x68, 0x61, 0x31, 0x2e,
	0x45, 0x78, 0x70, 0x72, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x12, 0x25, 0x0a, 0x0e, 0x6f,
	0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x5f, 0x65, 0x6e, 0x74, 0x72, 0x79, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x08, 0x52, 0x0d, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x45, 0x6e, 0x74,
	0x72, 0x79, 0x42, 0x0a, 0x0a, 0x08, 0x6b, 0x65, 0x79, 0x5f, 0x6b, 0x69, 0x6e, 0x64, 0x1a, 0x9a,
	0x03, 0x0a, 0x0d, 0x43, 0x6f, 0x6d, 0x70, 0x72, 0x65, 0x68, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e,
	0x12, 0x19, 0x0a, 0x08, 0x69, 0x74, 0x65, 0x72, 0x5f, 0x76, 0x61, 0x72, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x07, 0x69, 0x74, 0x65, 0x72, 0x56, 0x61, 0x72, 0x12, 0x1b, 0x0a, 0x09, 0x69,
	0x74, 0x65, 0x72, 0x5f, 0x76, 0x61, 0x72, 0x32, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08,
	0x69, 0x74, 0x65, 0x72, 0x56, 0x61, 0x72, 0x32, 0x12, 0x3d, 0x0a, 0x0a, 0x69, 0x74, 0x65, 0x72,
	0x5f, 0x72, 0x61, 0x6e, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x65, 0x78, 0x70, 0x72, 0x2e, 0x76,
	0x31, 0x61, 0x6c, 0x70, 0x68, 0x61, 0x31, 0x2e, 0x45, 0x78, 0x70, 0x72, 0x52, 0x09, 0x69, 0x74,
	0x65, 0x72, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x61, 0x63, 0x63, 0x75, 0x5f,
	0x76, 0x61, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x63, 0x63, 0x75, 0x56,
	0x61, 0x72, 0x12, 0x3b, 0x0a, 0x09, 0x61, 0x63, 0x63, 0x75, 0x5f, 0x69, 0x6e, 0x69, 0x74, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x65, 0x78, 0x70, 0x72, 0x2e, 0x76, 0x31, 0x61, 0x6c, 0x70, 0x68, 0x61, 0x31,
	0x2e, 0x45, 0x78, 0x70, 0x72, 0x52, 0x08, 0x61, 0x63, 0x63, 0x75, 0x49, 0x6e, 0x69, 0x74, 0x12,
	0x45, 0x0a, 0x0e, 0x6c, 0x6f, 0x6f, 0x70, 0x5f, 0x63, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f,
	0x6e, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x65, 0x78, 0x70, 0x72, 0x2e, 0x76, 0x31, 0x61, 0x6c, 0x70, 0x68,
	0x61, 0x31, 0x2e, 0x45, 0x78, 0x70, 0x72, 0x52, 0x0d, 0x6c, 0x6f, 0x6f, 0x70, 0x43, 0x6f, 0x6e,
	0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x3b, 0x0a, 0x09, 0x6c, 0x6f, 0x6f, 0x70, 0x5f, 0x73,
	0x74, 0x65, 0x70, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x65, 0x78, 0x70, 0x72, 0x2e, 0x76, 0x31, 0x61, 0x6c,
	0x70, 0x68, 0x61, 0x31, 0x2e, 0x45, 0x78, 0x70, 0x72, 0x52, 0x08, 0x6c, 0x6f, 0x6f, 0x70, 0x53,
	0x74, 0x65, 0x70, 0x12, 0x36, 0x0a, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x18, 0x07, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x65, 0x78, 0x70, 0x72, 0x2e, 0x76, 0x31, 0x61, 0x6c, 0x70, 0x68, 0x61, 0x31, 0x2e, 0x45,
	0x78, 0x70, 0x72, 0x52, 0x06, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x42, 0x0b, 0x0a, 0x09, 0x65,
	0x78, 0x70, 0x72, 0x5f, 0x6b, 0x69, 0x6e, 0x64, 0x22, 0xc1, 0x03, 0x0a, 0x08, 0x43, 0x6f, 0x6e,
	0x73, 0x74, 0x61, 0x6e, 0x74, 0x12, 0x3b, 0x0a, 0x0a, 0x6e, 0x75, 0x6c, 0x6c, 0x5f, 0x76, 0x61,
	0x6c, 0x75, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x4e, 0x75, 0x6c, 0x6c,
	0x56, 0x61, 0x6c, 0x75, 0x65, 0x48, 0x00, 0x52, 0x09, 0x6e, 0x75, 0x6c, 0x6c, 0x56, 0x61, 0x6c,
	0x75, 0x65, 0x12, 0x1f, 0x0a, 0x0a, 0x62, 0x6f, 0x6f, 0x6c, 0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x48, 0x00, 0x52, 0x09, 0x62, 0x6f, 0x6f, 0x6c, 0x56, 0x61,
	0x6c, 0x75, 0x65, 0x12, 0x21, 0x0a, 0x0b, 0x69, 0x6e, 0x74, 0x36, 0x34, 0x5f, 0x76, 0x61, 0x6c,
	0x75, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x48, 0x00, 0x52, 0x0a, 0x69, 0x6e, 0x74, 0x36,
	0x34, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x12, 0x23, 0x0a, 0x0c, 0x75, 0x69, 0x6e, 0x74, 0x36, 0x34,
	0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x04, 0x48, 0x00, 0x52, 0x0b,
	0x75, 0x69, 0x6e, 0x74, 0x36, 0x34, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x12, 0x23, 0x0a, 0x0c, 0x64,
	0x6f, 0x75, 0x62, 0x6c, 0x65, 0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x01, 0x48, 0x00, 0x52, 0x0b, 0x64, 0x6f, 0x75, 0x62, 0x6c, 0x65, 0x56, 0x61, 0x6c, 0x75, 0x65,
	0x12, 0x23, 0x0a, 0x0c, 0x73, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65,
	0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x0b, 0x73, 0x74, 0x72, 0x69, 0x6e, 0x67,
	0x56, 0x61, 0x6c, 0x75, 0x65, 0x12, 0x21, 0x0a, 0x0b, 0x62, 0x79, 0x74, 0x65, 0x73, 0x5f, 0x76,
	0x61, 0x6c, 0x75, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0c, 0x48, 0x00, 0x52, 0x0a, 0x62, 0x79,
	0x74, 0x65, 0x73, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x12, 0x46, 0x0a, 0x0e, 0x64, 0x75, 0x72, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x19, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62,
	0x75, 0x66, 0x2e, 0x44, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x42, 0x02, 0x18, 0x01, 0x48,
	0x00, 0x52, 0x0d, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x56, 0x61, 0x6c, 0x75, 0x65,
	0x12, 0x49, 0x0a, 0x0f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x5f, 0x76, 0x61,
	0x6c, 0x75, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65,
	0x73, 0x74, 0x61, 0x6d, 0x70, 0x42, 0x02, 0x18, 0x01, 0x48, 0x00, 0x52, 0x0e, 0x74, 0x69, 0x6d,
	0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x42, 0x0f, 0x0a, 0x0d, 0x63,
	0x6f, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x74, 0x5f, 0x6b, 0x69, 0x6e, 0x64, 0x22, 0x8c, 0x07, 0x0a,
	0x0a, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x25, 0x0a, 0x0e, 0x73,
	0x79, 0x6e, 0x74, 0x61, 0x78, 0x5f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0d, 0x73, 0x79, 0x6e, 0x74, 0x61, 0x78, 0x56, 0x65, 0x72, 0x73, 0x69,
	0x6f, 0x6e, 0x12, 0x1a, 0x0a, 0x08, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x21,
	0x0a, 0x0c, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x6f, 0x66, 0x66, 0x73, 0x65, 0x74, 0x73, 0x18, 0x03,
	0x20, 0x03, 0x28, 0x05, 0x52, 0x0b, 0x6c, 0x69, 0x6e, 0x65, 0x4f, 0x66, 0x66, 0x73, 0x65, 0x74,
	0x73, 0x12, 0x51, 0x0a, 0x09, 0x70, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x04,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x33, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x65, 0x78, 0x70, 0x72, 0x2e, 0x76, 0x31, 0x61, 0x6c, 0x70, 0x68, 0x61, 0x31, 0x2e,
	0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x2e, 0x50, 0x6f, 0x73, 0x69, 0x74,
	0x69, 0x6f, 0x6e, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x09, 0x70, 0x6f, 0x73, 0x69, 0x74,
	0x69, 0x6f, 0x6e, 0x73, 0x12, 0x55, 0x0a, 0x0b, 0x6d, 0x61, 0x63, 0x72, 0x6f, 0x5f, 0x63, 0x61,
	0x6c, 0x6c, 0x73, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x34, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x65, 0x78, 0x70, 0x72, 0x2e, 0x76, 0x31, 0x61, 0x6c,
	0x70, 0x68, 0x61, 0x31, 0x2e, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x2e,
	0x4d, 0x61, 0x63, 0x72, 0x6f, 0x43, 0x61, 0x6c, 0x6c, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52,
	0x0a, 0x6d, 0x61, 0x63, 0x72, 0x6f, 0x43, 0x61, 0x6c, 0x6c, 0x73, 0x12, 0x4e, 0x0a, 0x0a, 0x65,
	0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x06, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x2e, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x65, 0x78, 0x70,
	0x72, 0x2e, 0x76, 0x31, 0x61, 0x6c, 0x70, 0x68, 0x61, 0x31, 0x2e, 0x53, 0x6f, 0x75, 0x72, 0x63,
	0x65, 0x49, 0x6e, 0x66, 0x6f, 0x2e, 0x45, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x52,
	0x0a, 0x65, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x1a, 0x80, 0x03, 0x0a, 0x09,
	0x45, 0x78, 0x74, 0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x69, 0x0a, 0x13, 0x61, 0x66, 0x66,
	0x65, 0x63, 0x74, 0x65, 0x64, 0x5f, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x73,
	0x18, 0x02, 0x20, 0x03, 0x28, 0x0e, 0x32, 0x38, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x65, 0x78, 0x70, 0x72, 0x2e, 0x76, 0x31, 0x61, 0x6c, 0x70, 0x68, 0x61,
	0x31, 0x2e, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x2e, 0x45, 0x78, 0x74,
	0x65, 0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x2e, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74,
	0x52, 0x12, 0x61, 0x66, 0x66, 0x65, 0x63, 0x74, 0x65, 0x64, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e,
	0x65, 0x6e, 0x74, 0x73, 0x12, 0x50, 0x0a, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x36, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x65, 0x78, 0x70, 0x72, 0x2e, 0x76, 0x31, 0x61, 0x6c, 0x70, 0x68, 0x61, 0x31,
	0x2e, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x2e, 0x45, 0x78, 0x74, 0x65,
	0x6e, 0x73, 0x69, 0x6f, 0x6e, 0x2e, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x07, 0x76,
	0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x1a, 0x35, 0x0a, 0x07, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f,
	0x6e, 0x12, 0x14, 0x0a, 0x05, 0x6d, 0x61, 0x6a, 0x6f, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x05, 0x6d, 0x61, 0x6a, 0x6f, 0x72, 0x12, 0x14, 0x0a, 0x05, 0x6d, 0x69, 0x6e, 0x6f, 0x72,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x6d, 0x69, 0x6e, 0x6f, 0x72, 0x22, 0x6f, 0x0a,
	0x09, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x12, 0x19, 0x0a, 0x15, 0x43, 0x4f,
	0x4d, 0x50, 0x4f, 0x4e, 0x45, 0x4e, 0x54, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46,
	0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x14, 0x0a, 0x10, 0x43, 0x4f, 0x4d, 0x50, 0x4f, 0x4e, 0x45,
	0x4e, 0x54, 0x5f, 0x50, 0x41, 0x52, 0x53, 0x45, 0x52, 0x10, 0x01, 0x12, 0x1a, 0x0a, 0x16, 0x43,
	0x4f, 0x4d, 0x50, 0x4f, 0x4e, 0x45, 0x4e, 0x54, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x43, 0x48,
	0x45, 0x43, 0x4b, 0x45, 0x52, 0x10, 0x02, 0x12, 0x15, 0x0a, 0x11, 0x43, 0x4f, 0x4d, 0x50, 0x4f,
	0x4e, 0x45, 0x4e, 0x54, 0x5f, 0x52, 0x55, 0x4e, 0x54, 0x49, 0x4d, 0x45, 0x10, 0x03, 0x1a, 0x3c,
	0x0a, 0x0e, 0x50, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79,
	0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x03, 0x6b,
	0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x1a, 0x5d, 0x0a, 0x0f,
	0x4d, 0x61, 0x63, 0x72, 0x6f, 0x43, 0x61, 0x6c, 0x6c, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12,
	0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x03, 0x6b, 0x65,
	0x79, 0x12, 0x34, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1e, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x65, 0x78,
	0x70, 0x72, 0x2e, 0x76, 0x31, 0x61, 0x6c, 0x70, 0x68, 0x61, 0x31, 0x2e, 0x45, 0x78, 0x70, 0x72,
	0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0x70, 0x0a, 0x0e, 0x53,
	0x6f, 0x75, 0x72, 0x63, 0x65, 0x50, 0x6f, 0x73, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x1a, 0x0a,
	0x08, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x08, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x16, 0x0a, 0x06, 0x6f, 0x66, 0x66,
	0x73, 0x65, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x6f, 0x66, 0x66, 0x73, 0x65,
	0x74, 0x12, 0x12, 0x0a, 0x04, 0x6c, 0x69, 0x6e, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x04, 0x6c, 0x69, 0x6e, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x63, 0x6f, 0x6c, 0x75, 0x6d, 0x6e, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x63, 0x6f, 0x6c, 0x75, 0x6d, 0x6e, 0x42, 0x6e, 0x0a,
	0x1c, 0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x65, 0x78, 0x70, 0x72, 0x2e, 0x76, 0x31, 0x61, 0x6c, 0x70, 0x68, 0x61, 0x31, 0x42, 0x0b, 0x53,
	0x79, 0x6e, 0x74, 0x61, 0x78, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x50, 0x01, 0x5a, 0x3c, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x67, 0x6f, 0x6c, 0x61, 0x6e, 0x67, 0x2e, 0x6f, 0x72, 0x67, 0x2f,
	0x67, 0x65, 0x6e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61,
	0x70, 0x69, 0x73, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x65, 0x78, 0x70, 0x72, 0x2f, 0x76, 0x31, 0x61,
	0x6c, 0x70, 0x68, 0x61, 0x31, 0x3b, 0x65, 0x78, 0x70, 0x72, 0xf8, 0x01, 0x01, 0x62, 0x06, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_google_api_expr_v1alpha1_syntax_proto_rawDescOnce sync.Once
	file_google_api_expr_v1alpha1_syntax_proto_rawDescData = file_google_api_expr_v1alpha1_syntax_proto_rawDesc
)

func file_google_api_expr_v1alpha1_syntax_proto_rawDescGZIP() []byte {
	file_google_api_expr_v1alpha1_syntax_proto_rawDescOnce.Do(func() {
		file_google_api_expr_v1alpha1_syntax_proto_rawDescData = protoimpl.X.CompressGZIP(file_google_api_expr_v1alpha1_syntax_proto_rawDescData)
	})
	return file_google_api_expr_v1alpha1_syntax_proto_rawDescData
}

var file_google_api_expr_v1alpha1_syntax_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_google_api_expr_v1alpha1_syntax_proto_msgTypes = make([]protoimpl.MessageInfo, 16)
var file_google_api_expr_v1alpha1_syntax_proto_goTypes = []interface{}{
	(SourceInfo_Extension_Component)(0),  // 0: google.api.expr.v1alpha1.SourceInfo.Extension.Component
	(*ParsedExpr)(nil),                   // 1: google.api.expr.v1alpha1.ParsedExpr
	(*Expr)(nil),                         // 2: google.api.expr.v1alpha1.Expr
	(*Constant)(nil),                     // 3: google.api.expr.v1alpha1.Constant
	(*SourceInfo)(nil),                   // 4: google.api.expr.v1alpha1.SourceInfo
	(*SourcePosition)(nil),               // 5: google.api.expr.v1alpha1.SourcePosition
	(*Expr_Ident)(nil),                   // 6: google.api.expr.v1alpha1.Expr.Ident
	(*Expr_Select)(nil),                  // 7: google.api.expr.v1alpha1.Expr.Select
	(*Expr_Call)(nil),                    // 8: google.api.expr.v1alpha1.Expr.Call
	(*Expr_CreateList)(nil),              // 9: google.api.expr.v1alpha1.Expr.CreateList
	(*Expr_CreateStruct)(nil),            // 10: google.api.expr.v1alpha1.Expr.CreateStruct
	(*Expr_Comprehension)(nil),           // 11: google.api.expr.v1alpha1.Expr.Comprehension
	(*Expr_CreateStruct_Entry)(nil),      // 12: google.api.expr.v1alpha1.Expr.CreateStruct.Entry
	(*SourceInfo_Extension)(nil),         // 13: google.api.expr.v1alpha1.SourceInfo.Extension
	nil,                                  // 14: google.api.expr.v1alpha1.SourceInfo.PositionsEntry
	nil,                                  // 15: google.api.expr.v1alpha1.SourceInfo.MacroCallsEntry
	(*SourceInfo_Extension_Version)(nil), // 16: google.api.expr.v1alpha1.SourceInfo.Extension.Version
	(structpb.NullValue)(0),              // 17: google.protobuf.NullValue
	(*durationpb.Duration)(nil),          // 18: google.protobuf.Duration
	(*timestamppb.Timestamp)(nil),        // 19: google.protobuf.Timestamp
}
var file_google_api_expr_v1alpha1_syntax_proto_depIdxs = []int32{
	2,  // 0: google.api.expr.v1alpha1.ParsedExpr.expr:type_name -> google.api.expr.v1alpha1.Expr
	4,  // 1: google.api.expr.v1alpha1.ParsedExpr.source_info:type_name -> google.api.expr.v1alpha1.SourceInfo
	3,  // 2: google.api.expr.v1alpha1.Expr.const_expr:type_name -> google.api.expr.v1alpha1.Constant
	6,  // 3: google.api.expr.v1alpha1.Expr.ident_expr:type_name -> google.api.expr.v1alpha1.Expr.Ident
	7,  // 4: google.api.expr.v1alpha1.Expr.select_expr:type_name -> google.api.expr.v1alpha1.Expr.Select
	8,  // 5: google.api.expr.v1alpha1.Expr.call_expr:type_name -> google.api.expr.v1alpha1.Expr.Call
	9,  // 6: google.api.expr.v1alpha1.Expr.list_expr:type_name -> google.api.expr.v1alpha1.Expr.CreateList
	10, // 7: google.api.expr.v1alpha1.Expr.struct_expr:type_name -> google.api.expr.v1alpha1.Expr.CreateStruct
	11, // 8: google.api.expr.v1alpha1.Expr.comprehension_expr:type_name -> google.api.expr.v1alpha1.Expr.Comprehension
	17, // 9: google.api.expr.v1alpha1.Constant.null_value:type_name -> google.protobuf.NullValue
	18, // 10: google.api.expr.v1alpha1.Constant.duration_value:type_name -> google.protobuf.Duration
	19, // 11: google.api.expr.v1alpha1.Constant.timestamp_value:type_name -> google.protobuf.Timestamp
	14, // 12: google.api.expr.v1alpha1.SourceInfo.positions:type_name -> google.api.expr.v1alpha1.SourceInfo.PositionsEntry
	15, // 13: google.api.expr.v1alpha1.SourceInfo.macro_calls:type_name -> google.api.expr.v1alpha1.SourceInfo.MacroCallsEntry
	13, // 14: google.api.expr.v1alpha1.SourceInfo.extensions:type_name -> google.api.expr.v1alpha1.SourceInfo.Extension
	2,  // 15: google.api.expr.v1alpha1.Expr.Select.operand:type_name -> google.api.expr.v1alpha1.Expr
	2,  // 16: google.api.expr.v1alpha1.Expr.Call.target:type_name -> google.api.expr.v1alpha1.Expr
	2,  // 17: google.api.expr.v1alpha1.Expr.Call.args:type_name -> google.api.expr.v1alpha1.Expr
	2,  // 18: google.api.expr.v1alpha1.Expr.CreateList.elements:type_name -> google.api.expr.v1alpha1.Expr
	12, // 19: google.api.expr.v1alpha1.Expr.CreateStruct.entries:type_name -> google.api.expr.v1alpha1.Expr.CreateStruct.Entry
	2,  // 20: google.api.expr.v1alpha1.Expr.Comprehension.iter_range:type_name -> google.api.expr.v1alpha1.Expr
	2,  // 21: google.api.expr.v1alpha1.Expr.Comprehension.accu_init:type_name -> google.api.expr.v1alpha1.Expr
	2,  // 22: google.api.expr.v1alpha1.Expr.Comprehension.loop_condition:type_name -> google.api.expr.v1alpha1.Expr
	2,  // 23: google.api.expr.v1alpha1.Expr.Comprehension.loop_step:type_name -> google.api.expr.v1alpha1.Expr
	2,  // 24: google.api.expr.v1alpha1.Expr.Comprehension.result:type_name -> google.api.expr.v1alpha1.Expr
	2,  // 25: google.api.expr.v1alpha1.Expr.CreateStruct.Entry.map_key:type_name -> google.api.expr.v1alpha1.Expr
	2,  // 26: google.api.expr.v1alpha1.Expr.CreateStruct.Entry.value:type_name -> google.api.expr.v1alpha1.Expr
	0,  // 27: google.api.expr.v1alpha1.SourceInfo.Extension.affected_components:type_name -> google.api.expr.v1alpha1.SourceInfo.Extension.Component
	16, // 28: google.api.expr.v1alpha1.SourceInfo.Extension.version:type_name -> google.api.expr.v1alpha1.SourceInfo.Extension.Version
	2,  // 29: google.api.expr.v1alpha1.SourceInfo.MacroCallsEntry.value:type_name -> google.api.expr.v1alpha1.Expr
	30, // [30:30] is the sub-list for method output_type
	30, // [30:30] is the sub-list for method input_type
	30, // [30:30] is the sub-list for extension type_name
	30, // [30:30] is the sub-list for extension extendee
	0,  // [0:30] is the sub-list for field type_name
}

func init() { file_google_api_expr_v1alpha1_syntax_proto_init() }
func file_google_api_expr_v1alpha1_syntax_proto_init() {
	if File_google_api_expr_v1alpha1_syntax_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_google_api_expr_v1alpha1_syntax_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ParsedExpr); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_api_expr_v1alpha1_syntax_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Expr); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_api_expr_v1alpha1_syntax_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Constant); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_api_expr_v1alpha1_syntax_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SourceInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_api_expr_v1alpha1_syntax_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SourcePosition); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_api_expr_v1alpha1_syntax_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Expr_Ident); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_api_expr_v1alpha1_syntax_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Expr_Select); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_api_expr_v1alpha1_syntax_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Expr_Call); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_api_expr_v1alpha1_syntax_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Expr_CreateList); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_api_expr_v1alpha1_syntax_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Expr_CreateStruct); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_api_expr_v1alpha1_syntax_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Expr_Comprehension); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_api_expr_v1alpha1_syntax_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Expr_CreateStruct_Entry); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_api_expr_v1alpha1_syntax_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SourceInfo_Extension); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_api_expr_v1alpha1_syntax_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SourceInfo_Extension_Version); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_google_api_expr_v1alpha1_syntax_proto_msgTypes[1].OneofWrappers = []interface{}{
		(*Expr_ConstExpr)(nil),
		(*Expr_IdentExpr)(nil),
		(*Expr_SelectExpr)(nil),
		(*Expr_CallExpr)(nil),
		(*Expr_ListExpr)(nil),
		(*Expr_StructExpr)(nil),
		(*Expr_ComprehensionExpr)(nil),
	}
	file_google_api_expr_v1alpha1_syntax_proto_msgTypes[2].OneofWrappers = []interface{}{
		(*Constant_NullValue)(nil),
		(*Constant_BoolValue)(nil),
		(*Constant_Int64Value)(nil),
		(*Constant_Uint64Value)(nil),
		(*Constant_DoubleValue)(nil),
		(*Constant_StringValue)(nil),
		(*Constant_BytesValue)(nil),
		(*Constant_DurationValue)(nil),
		(*Constant_TimestampValue)(nil),
	}
	file_google_api_expr_v1alpha1_syntax_proto_msgTypes[11].OneofWrappers = []interface{}{
		(*Expr_CreateStruct_Entry_FieldKey)(nil),
		(*Expr_CreateStruct_Entry_MapKey)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_google_api_expr_v1alpha1_syntax_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   16,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_google_api_expr_v1alpha1_syntax_proto_goTypes,
		DependencyIndexes: file_google_api_expr_v1alpha1_syntax_proto_depIdxs,
		EnumInfos:         file_google_api_expr_v1alpha1_syntax_proto_enumTypes,
		MessageInfos:      file_google_api_expr_v1alpha1_syntax_proto_msgTypes,
	}.Build()
	File_google_api_expr_v1alpha1_syntax_proto = out.File
	file_google_api_expr_v1alpha1_syntax_proto_rawDesc = nil
	file_google_api_expr_v1alpha1_syntax_proto_goTypes = nil
	file_google_api_expr_v1alpha1_syntax_proto_depIdxs = nil
}
