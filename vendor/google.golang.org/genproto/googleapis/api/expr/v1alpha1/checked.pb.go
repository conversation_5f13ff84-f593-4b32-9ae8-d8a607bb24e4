// Copyright 2025 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.26.0
// 	protoc        v4.24.4
// source: google/api/expr/v1alpha1/checked.proto

package expr

import (
	reflect "reflect"
	sync "sync"

	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
	structpb "google.golang.org/protobuf/types/known/structpb"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// CEL primitive types.
type Type_PrimitiveType int32

const (
	// Unspecified type.
	Type_PRIMITIVE_TYPE_UNSPECIFIED Type_PrimitiveType = 0
	// Boolean type.
	Type_BOOL Type_PrimitiveType = 1
	// Int64 type.
	//
	// Proto-based integer values are widened to int64.
	Type_INT64 Type_PrimitiveType = 2
	// Uint64 type.
	//
	// Proto-based unsigned integer values are widened to uint64.
	Type_UINT64 Type_PrimitiveType = 3
	// Double type.
	//
	// Proto-based float values are widened to double values.
	Type_DOUBLE Type_PrimitiveType = 4
	// String type.
	Type_STRING Type_PrimitiveType = 5
	// Bytes type.
	Type_BYTES Type_PrimitiveType = 6
)

// Enum value maps for Type_PrimitiveType.
var (
	Type_PrimitiveType_name = map[int32]string{
		0: "PRIMITIVE_TYPE_UNSPECIFIED",
		1: "BOOL",
		2: "INT64",
		3: "UINT64",
		4: "DOUBLE",
		5: "STRING",
		6: "BYTES",
	}
	Type_PrimitiveType_value = map[string]int32{
		"PRIMITIVE_TYPE_UNSPECIFIED": 0,
		"BOOL":                       1,
		"INT64":                      2,
		"UINT64":                     3,
		"DOUBLE":                     4,
		"STRING":                     5,
		"BYTES":                      6,
	}
)

func (x Type_PrimitiveType) Enum() *Type_PrimitiveType {
	p := new(Type_PrimitiveType)
	*p = x
	return p
}

func (x Type_PrimitiveType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Type_PrimitiveType) Descriptor() protoreflect.EnumDescriptor {
	return file_google_api_expr_v1alpha1_checked_proto_enumTypes[0].Descriptor()
}

func (Type_PrimitiveType) Type() protoreflect.EnumType {
	return &file_google_api_expr_v1alpha1_checked_proto_enumTypes[0]
}

func (x Type_PrimitiveType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use Type_PrimitiveType.Descriptor instead.
func (Type_PrimitiveType) EnumDescriptor() ([]byte, []int) {
	return file_google_api_expr_v1alpha1_checked_proto_rawDescGZIP(), []int{1, 0}
}

// Well-known protobuf types treated with first-class support in CEL.
type Type_WellKnownType int32

const (
	// Unspecified type.
	Type_WELL_KNOWN_TYPE_UNSPECIFIED Type_WellKnownType = 0
	// Well-known protobuf.Any type.
	//
	// Any types are a polymorphic message type. During type-checking they are
	// treated like `DYN` types, but at runtime they are resolved to a specific
	// message type specified at evaluation time.
	Type_ANY Type_WellKnownType = 1
	// Well-known protobuf.Timestamp type, internally referenced as `timestamp`.
	Type_TIMESTAMP Type_WellKnownType = 2
	// Well-known protobuf.Duration type, internally referenced as `duration`.
	Type_DURATION Type_WellKnownType = 3
)

// Enum value maps for Type_WellKnownType.
var (
	Type_WellKnownType_name = map[int32]string{
		0: "WELL_KNOWN_TYPE_UNSPECIFIED",
		1: "ANY",
		2: "TIMESTAMP",
		3: "DURATION",
	}
	Type_WellKnownType_value = map[string]int32{
		"WELL_KNOWN_TYPE_UNSPECIFIED": 0,
		"ANY":                         1,
		"TIMESTAMP":                   2,
		"DURATION":                    3,
	}
)

func (x Type_WellKnownType) Enum() *Type_WellKnownType {
	p := new(Type_WellKnownType)
	*p = x
	return p
}

func (x Type_WellKnownType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Type_WellKnownType) Descriptor() protoreflect.EnumDescriptor {
	return file_google_api_expr_v1alpha1_checked_proto_enumTypes[1].Descriptor()
}

func (Type_WellKnownType) Type() protoreflect.EnumType {
	return &file_google_api_expr_v1alpha1_checked_proto_enumTypes[1]
}

func (x Type_WellKnownType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use Type_WellKnownType.Descriptor instead.
func (Type_WellKnownType) EnumDescriptor() ([]byte, []int) {
	return file_google_api_expr_v1alpha1_checked_proto_rawDescGZIP(), []int{1, 1}
}

// A CEL expression which has been successfully type checked.
type CheckedExpr struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// A map from expression ids to resolved references.
	//
	// The following entries are in this table:
	//
	//   - An Ident or Select expression is represented here if it resolves to a
	//     declaration. For instance, if `a.b.c` is represented by
	//     `select(select(id(a), b), c)`, and `a.b` resolves to a declaration,
	//     while `c` is a field selection, then the reference is attached to the
	//     nested select expression (but not to the id or or the outer select).
	//     In turn, if `a` resolves to a declaration and `b.c` are field selections,
	//     the reference is attached to the ident expression.
	//   - Every Call expression has an entry here, identifying the function being
	//     called.
	//   - Every CreateStruct expression for a message has an entry, identifying
	//     the message.
	ReferenceMap map[int64]*Reference `protobuf:"bytes,2,rep,name=reference_map,json=referenceMap,proto3" json:"reference_map,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	// A map from expression ids to types.
	//
	// Every expression node which has a type different than DYN has a mapping
	// here. If an expression has type DYN, it is omitted from this map to save
	// space.
	TypeMap map[int64]*Type `protobuf:"bytes,3,rep,name=type_map,json=typeMap,proto3" json:"type_map,omitempty" protobuf_key:"varint,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	// The source info derived from input that generated the parsed `expr` and
	// any optimizations made during the type-checking pass.
	SourceInfo *SourceInfo `protobuf:"bytes,5,opt,name=source_info,json=sourceInfo,proto3" json:"source_info,omitempty"`
	// The expr version indicates the major / minor version number of the `expr`
	// representation.
	//
	// The most common reason for a version change will be to indicate to the CEL
	// runtimes that transformations have been performed on the expr during static
	// analysis. In some cases, this will save the runtime the work of applying
	// the same or similar transformations prior to evaluation.
	ExprVersion string `protobuf:"bytes,6,opt,name=expr_version,json=exprVersion,proto3" json:"expr_version,omitempty"`
	// The checked expression. Semantically equivalent to the parsed `expr`, but
	// may have structural differences.
	Expr *Expr `protobuf:"bytes,4,opt,name=expr,proto3" json:"expr,omitempty"`
}

func (x *CheckedExpr) Reset() {
	*x = CheckedExpr{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_api_expr_v1alpha1_checked_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CheckedExpr) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckedExpr) ProtoMessage() {}

func (x *CheckedExpr) ProtoReflect() protoreflect.Message {
	mi := &file_google_api_expr_v1alpha1_checked_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckedExpr.ProtoReflect.Descriptor instead.
func (*CheckedExpr) Descriptor() ([]byte, []int) {
	return file_google_api_expr_v1alpha1_checked_proto_rawDescGZIP(), []int{0}
}

func (x *CheckedExpr) GetReferenceMap() map[int64]*Reference {
	if x != nil {
		return x.ReferenceMap
	}
	return nil
}

func (x *CheckedExpr) GetTypeMap() map[int64]*Type {
	if x != nil {
		return x.TypeMap
	}
	return nil
}

func (x *CheckedExpr) GetSourceInfo() *SourceInfo {
	if x != nil {
		return x.SourceInfo
	}
	return nil
}

func (x *CheckedExpr) GetExprVersion() string {
	if x != nil {
		return x.ExprVersion
	}
	return ""
}

func (x *CheckedExpr) GetExpr() *Expr {
	if x != nil {
		return x.Expr
	}
	return nil
}

// Represents a CEL type.
type Type struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The kind of type.
	//
	// Types that are assignable to TypeKind:
	//
	//	*Type_Dyn
	//	*Type_Null
	//	*Type_Primitive
	//	*Type_Wrapper
	//	*Type_WellKnown
	//	*Type_ListType_
	//	*Type_MapType_
	//	*Type_Function
	//	*Type_MessageType
	//	*Type_TypeParam
	//	*Type_Type
	//	*Type_Error
	//	*Type_AbstractType_
	TypeKind isType_TypeKind `protobuf_oneof:"type_kind"`
}

func (x *Type) Reset() {
	*x = Type{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_api_expr_v1alpha1_checked_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Type) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Type) ProtoMessage() {}

func (x *Type) ProtoReflect() protoreflect.Message {
	mi := &file_google_api_expr_v1alpha1_checked_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Type.ProtoReflect.Descriptor instead.
func (*Type) Descriptor() ([]byte, []int) {
	return file_google_api_expr_v1alpha1_checked_proto_rawDescGZIP(), []int{1}
}

func (m *Type) GetTypeKind() isType_TypeKind {
	if m != nil {
		return m.TypeKind
	}
	return nil
}

func (x *Type) GetDyn() *emptypb.Empty {
	if x, ok := x.GetTypeKind().(*Type_Dyn); ok {
		return x.Dyn
	}
	return nil
}

func (x *Type) GetNull() structpb.NullValue {
	if x, ok := x.GetTypeKind().(*Type_Null); ok {
		return x.Null
	}
	return structpb.NullValue_NULL_VALUE
}

func (x *Type) GetPrimitive() Type_PrimitiveType {
	if x, ok := x.GetTypeKind().(*Type_Primitive); ok {
		return x.Primitive
	}
	return Type_PRIMITIVE_TYPE_UNSPECIFIED
}

func (x *Type) GetWrapper() Type_PrimitiveType {
	if x, ok := x.GetTypeKind().(*Type_Wrapper); ok {
		return x.Wrapper
	}
	return Type_PRIMITIVE_TYPE_UNSPECIFIED
}

func (x *Type) GetWellKnown() Type_WellKnownType {
	if x, ok := x.GetTypeKind().(*Type_WellKnown); ok {
		return x.WellKnown
	}
	return Type_WELL_KNOWN_TYPE_UNSPECIFIED
}

func (x *Type) GetListType() *Type_ListType {
	if x, ok := x.GetTypeKind().(*Type_ListType_); ok {
		return x.ListType
	}
	return nil
}

func (x *Type) GetMapType() *Type_MapType {
	if x, ok := x.GetTypeKind().(*Type_MapType_); ok {
		return x.MapType
	}
	return nil
}

func (x *Type) GetFunction() *Type_FunctionType {
	if x, ok := x.GetTypeKind().(*Type_Function); ok {
		return x.Function
	}
	return nil
}

func (x *Type) GetMessageType() string {
	if x, ok := x.GetTypeKind().(*Type_MessageType); ok {
		return x.MessageType
	}
	return ""
}

func (x *Type) GetTypeParam() string {
	if x, ok := x.GetTypeKind().(*Type_TypeParam); ok {
		return x.TypeParam
	}
	return ""
}

func (x *Type) GetType() *Type {
	if x, ok := x.GetTypeKind().(*Type_Type); ok {
		return x.Type
	}
	return nil
}

func (x *Type) GetError() *emptypb.Empty {
	if x, ok := x.GetTypeKind().(*Type_Error); ok {
		return x.Error
	}
	return nil
}

func (x *Type) GetAbstractType() *Type_AbstractType {
	if x, ok := x.GetTypeKind().(*Type_AbstractType_); ok {
		return x.AbstractType
	}
	return nil
}

type isType_TypeKind interface {
	isType_TypeKind()
}

type Type_Dyn struct {
	// Dynamic type.
	Dyn *emptypb.Empty `protobuf:"bytes,1,opt,name=dyn,proto3,oneof"`
}

type Type_Null struct {
	// Null value.
	Null structpb.NullValue `protobuf:"varint,2,opt,name=null,proto3,enum=google.protobuf.NullValue,oneof"`
}

type Type_Primitive struct {
	// Primitive types: `true`, `1u`, `-2.0`, `'string'`, `b'bytes'`.
	Primitive Type_PrimitiveType `protobuf:"varint,3,opt,name=primitive,proto3,enum=google.api.expr.v1alpha1.Type_PrimitiveType,oneof"`
}

type Type_Wrapper struct {
	// Wrapper of a primitive type, e.g. `google.protobuf.Int64Value`.
	Wrapper Type_PrimitiveType `protobuf:"varint,4,opt,name=wrapper,proto3,enum=google.api.expr.v1alpha1.Type_PrimitiveType,oneof"`
}

type Type_WellKnown struct {
	// Well-known protobuf type such as `google.protobuf.Timestamp`.
	WellKnown Type_WellKnownType `protobuf:"varint,5,opt,name=well_known,json=wellKnown,proto3,enum=google.api.expr.v1alpha1.Type_WellKnownType,oneof"`
}

type Type_ListType_ struct {
	// Parameterized list with elements of `list_type`, e.g. `list<timestamp>`.
	ListType *Type_ListType `protobuf:"bytes,6,opt,name=list_type,json=listType,proto3,oneof"`
}

type Type_MapType_ struct {
	// Parameterized map with typed keys and values.
	MapType *Type_MapType `protobuf:"bytes,7,opt,name=map_type,json=mapType,proto3,oneof"`
}

type Type_Function struct {
	// Function type.
	Function *Type_FunctionType `protobuf:"bytes,8,opt,name=function,proto3,oneof"`
}

type Type_MessageType struct {
	// Protocol buffer message type.
	//
	// The `message_type` string specifies the qualified message type name. For
	// example, `google.plus.Profile`.
	MessageType string `protobuf:"bytes,9,opt,name=message_type,json=messageType,proto3,oneof"`
}

type Type_TypeParam struct {
	// Type param type.
	//
	// The `type_param` string specifies the type parameter name, e.g. `list<E>`
	// would be a `list_type` whose element type was a `type_param` type
	// named `E`.
	TypeParam string `protobuf:"bytes,10,opt,name=type_param,json=typeParam,proto3,oneof"`
}

type Type_Type struct {
	// Type type.
	//
	// The `type` value specifies the target type. e.g. int is type with a
	// target type of `Primitive.INT`.
	Type *Type `protobuf:"bytes,11,opt,name=type,proto3,oneof"`
}

type Type_Error struct {
	// Error type.
	//
	// During type-checking if an expression is an error, its type is propagated
	// as the `ERROR` type. This permits the type-checker to discover other
	// errors present in the expression.
	Error *emptypb.Empty `protobuf:"bytes,12,opt,name=error,proto3,oneof"`
}

type Type_AbstractType_ struct {
	// Abstract, application defined type.
	AbstractType *Type_AbstractType `protobuf:"bytes,14,opt,name=abstract_type,json=abstractType,proto3,oneof"`
}

func (*Type_Dyn) isType_TypeKind() {}

func (*Type_Null) isType_TypeKind() {}

func (*Type_Primitive) isType_TypeKind() {}

func (*Type_Wrapper) isType_TypeKind() {}

func (*Type_WellKnown) isType_TypeKind() {}

func (*Type_ListType_) isType_TypeKind() {}

func (*Type_MapType_) isType_TypeKind() {}

func (*Type_Function) isType_TypeKind() {}

func (*Type_MessageType) isType_TypeKind() {}

func (*Type_TypeParam) isType_TypeKind() {}

func (*Type_Type) isType_TypeKind() {}

func (*Type_Error) isType_TypeKind() {}

func (*Type_AbstractType_) isType_TypeKind() {}

// Represents a declaration of a named value or function.
//
// A declaration is part of the contract between the expression, the agent
// evaluating that expression, and the caller requesting evaluation.
type Decl struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The fully qualified name of the declaration.
	//
	// Declarations are organized in containers and this represents the full path
	// to the declaration in its container, as in `google.api.expr.Decl`.
	//
	// Declarations used as
	// [FunctionDecl.Overload][google.api.expr.v1alpha1.Decl.FunctionDecl.Overload]
	// parameters may or may not have a name depending on whether the overload is
	// function declaration or a function definition containing a result
	// [Expr][google.api.expr.v1alpha1.Expr].
	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	// Required. The declaration kind.
	//
	// Types that are assignable to DeclKind:
	//
	//	*Decl_Ident
	//	*Decl_Function
	DeclKind isDecl_DeclKind `protobuf_oneof:"decl_kind"`
}

func (x *Decl) Reset() {
	*x = Decl{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_api_expr_v1alpha1_checked_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Decl) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Decl) ProtoMessage() {}

func (x *Decl) ProtoReflect() protoreflect.Message {
	mi := &file_google_api_expr_v1alpha1_checked_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Decl.ProtoReflect.Descriptor instead.
func (*Decl) Descriptor() ([]byte, []int) {
	return file_google_api_expr_v1alpha1_checked_proto_rawDescGZIP(), []int{2}
}

func (x *Decl) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (m *Decl) GetDeclKind() isDecl_DeclKind {
	if m != nil {
		return m.DeclKind
	}
	return nil
}

func (x *Decl) GetIdent() *Decl_IdentDecl {
	if x, ok := x.GetDeclKind().(*Decl_Ident); ok {
		return x.Ident
	}
	return nil
}

func (x *Decl) GetFunction() *Decl_FunctionDecl {
	if x, ok := x.GetDeclKind().(*Decl_Function); ok {
		return x.Function
	}
	return nil
}

type isDecl_DeclKind interface {
	isDecl_DeclKind()
}

type Decl_Ident struct {
	// Identifier declaration.
	Ident *Decl_IdentDecl `protobuf:"bytes,2,opt,name=ident,proto3,oneof"`
}

type Decl_Function struct {
	// Function declaration.
	Function *Decl_FunctionDecl `protobuf:"bytes,3,opt,name=function,proto3,oneof"`
}

func (*Decl_Ident) isDecl_DeclKind() {}

func (*Decl_Function) isDecl_DeclKind() {}

// Describes a resolved reference to a declaration.
type Reference struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The fully qualified name of the declaration.
	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	// For references to functions, this is a list of `Overload.overload_id`
	// values which match according to typing rules.
	//
	// If the list has more than one element, overload resolution among the
	// presented candidates must happen at runtime because of dynamic types. The
	// type checker attempts to narrow down this list as much as possible.
	//
	// Empty if this is not a reference to a
	// [Decl.FunctionDecl][google.api.expr.v1alpha1.Decl.FunctionDecl].
	OverloadId []string `protobuf:"bytes,3,rep,name=overload_id,json=overloadId,proto3" json:"overload_id,omitempty"`
	// For references to constants, this may contain the value of the
	// constant if known at compile time.
	Value *Constant `protobuf:"bytes,4,opt,name=value,proto3" json:"value,omitempty"`
}

func (x *Reference) Reset() {
	*x = Reference{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_api_expr_v1alpha1_checked_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Reference) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Reference) ProtoMessage() {}

func (x *Reference) ProtoReflect() protoreflect.Message {
	mi := &file_google_api_expr_v1alpha1_checked_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Reference.ProtoReflect.Descriptor instead.
func (*Reference) Descriptor() ([]byte, []int) {
	return file_google_api_expr_v1alpha1_checked_proto_rawDescGZIP(), []int{3}
}

func (x *Reference) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Reference) GetOverloadId() []string {
	if x != nil {
		return x.OverloadId
	}
	return nil
}

func (x *Reference) GetValue() *Constant {
	if x != nil {
		return x.Value
	}
	return nil
}

// List type with typed elements, e.g. `list<example.proto.MyMessage>`.
type Type_ListType struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The element type.
	ElemType *Type `protobuf:"bytes,1,opt,name=elem_type,json=elemType,proto3" json:"elem_type,omitempty"`
}

func (x *Type_ListType) Reset() {
	*x = Type_ListType{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_api_expr_v1alpha1_checked_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Type_ListType) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Type_ListType) ProtoMessage() {}

func (x *Type_ListType) ProtoReflect() protoreflect.Message {
	mi := &file_google_api_expr_v1alpha1_checked_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Type_ListType.ProtoReflect.Descriptor instead.
func (*Type_ListType) Descriptor() ([]byte, []int) {
	return file_google_api_expr_v1alpha1_checked_proto_rawDescGZIP(), []int{1, 0}
}

func (x *Type_ListType) GetElemType() *Type {
	if x != nil {
		return x.ElemType
	}
	return nil
}

// Map type with parameterized key and value types, e.g. `map<string, int>`.
type Type_MapType struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The type of the key.
	KeyType *Type `protobuf:"bytes,1,opt,name=key_type,json=keyType,proto3" json:"key_type,omitempty"`
	// The type of the value.
	ValueType *Type `protobuf:"bytes,2,opt,name=value_type,json=valueType,proto3" json:"value_type,omitempty"`
}

func (x *Type_MapType) Reset() {
	*x = Type_MapType{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_api_expr_v1alpha1_checked_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Type_MapType) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Type_MapType) ProtoMessage() {}

func (x *Type_MapType) ProtoReflect() protoreflect.Message {
	mi := &file_google_api_expr_v1alpha1_checked_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Type_MapType.ProtoReflect.Descriptor instead.
func (*Type_MapType) Descriptor() ([]byte, []int) {
	return file_google_api_expr_v1alpha1_checked_proto_rawDescGZIP(), []int{1, 1}
}

func (x *Type_MapType) GetKeyType() *Type {
	if x != nil {
		return x.KeyType
	}
	return nil
}

func (x *Type_MapType) GetValueType() *Type {
	if x != nil {
		return x.ValueType
	}
	return nil
}

// Function type with result and arg types.
type Type_FunctionType struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Result type of the function.
	ResultType *Type `protobuf:"bytes,1,opt,name=result_type,json=resultType,proto3" json:"result_type,omitempty"`
	// Argument types of the function.
	ArgTypes []*Type `protobuf:"bytes,2,rep,name=arg_types,json=argTypes,proto3" json:"arg_types,omitempty"`
}

func (x *Type_FunctionType) Reset() {
	*x = Type_FunctionType{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_api_expr_v1alpha1_checked_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Type_FunctionType) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Type_FunctionType) ProtoMessage() {}

func (x *Type_FunctionType) ProtoReflect() protoreflect.Message {
	mi := &file_google_api_expr_v1alpha1_checked_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Type_FunctionType.ProtoReflect.Descriptor instead.
func (*Type_FunctionType) Descriptor() ([]byte, []int) {
	return file_google_api_expr_v1alpha1_checked_proto_rawDescGZIP(), []int{1, 2}
}

func (x *Type_FunctionType) GetResultType() *Type {
	if x != nil {
		return x.ResultType
	}
	return nil
}

func (x *Type_FunctionType) GetArgTypes() []*Type {
	if x != nil {
		return x.ArgTypes
	}
	return nil
}

// Application defined abstract type.
type Type_AbstractType struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The fully qualified name of this abstract type.
	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	// Parameter types for this abstract type.
	ParameterTypes []*Type `protobuf:"bytes,2,rep,name=parameter_types,json=parameterTypes,proto3" json:"parameter_types,omitempty"`
}

func (x *Type_AbstractType) Reset() {
	*x = Type_AbstractType{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_api_expr_v1alpha1_checked_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Type_AbstractType) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Type_AbstractType) ProtoMessage() {}

func (x *Type_AbstractType) ProtoReflect() protoreflect.Message {
	mi := &file_google_api_expr_v1alpha1_checked_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Type_AbstractType.ProtoReflect.Descriptor instead.
func (*Type_AbstractType) Descriptor() ([]byte, []int) {
	return file_google_api_expr_v1alpha1_checked_proto_rawDescGZIP(), []int{1, 3}
}

func (x *Type_AbstractType) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Type_AbstractType) GetParameterTypes() []*Type {
	if x != nil {
		return x.ParameterTypes
	}
	return nil
}

// Identifier declaration which specifies its type and optional `Expr` value.
//
// An identifier without a value is a declaration that must be provided at
// evaluation time. An identifier with a value should resolve to a constant,
// but may be used in conjunction with other identifiers bound at evaluation
// time.
type Decl_IdentDecl struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Required. The type of the identifier.
	Type *Type `protobuf:"bytes,1,opt,name=type,proto3" json:"type,omitempty"`
	// The constant value of the identifier. If not specified, the identifier
	// must be supplied at evaluation time.
	Value *Constant `protobuf:"bytes,2,opt,name=value,proto3" json:"value,omitempty"`
	// Documentation string for the identifier.
	Doc string `protobuf:"bytes,3,opt,name=doc,proto3" json:"doc,omitempty"`
}

func (x *Decl_IdentDecl) Reset() {
	*x = Decl_IdentDecl{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_api_expr_v1alpha1_checked_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Decl_IdentDecl) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Decl_IdentDecl) ProtoMessage() {}

func (x *Decl_IdentDecl) ProtoReflect() protoreflect.Message {
	mi := &file_google_api_expr_v1alpha1_checked_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Decl_IdentDecl.ProtoReflect.Descriptor instead.
func (*Decl_IdentDecl) Descriptor() ([]byte, []int) {
	return file_google_api_expr_v1alpha1_checked_proto_rawDescGZIP(), []int{2, 0}
}

func (x *Decl_IdentDecl) GetType() *Type {
	if x != nil {
		return x.Type
	}
	return nil
}

func (x *Decl_IdentDecl) GetValue() *Constant {
	if x != nil {
		return x.Value
	}
	return nil
}

func (x *Decl_IdentDecl) GetDoc() string {
	if x != nil {
		return x.Doc
	}
	return ""
}

// Function declaration specifies one or more overloads which indicate the
// function's parameter types and return type.
//
// Functions have no observable side-effects (there may be side-effects like
// logging which are not observable from CEL).
type Decl_FunctionDecl struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Required. List of function overloads, must contain at least one overload.
	Overloads []*Decl_FunctionDecl_Overload `protobuf:"bytes,1,rep,name=overloads,proto3" json:"overloads,omitempty"`
}

func (x *Decl_FunctionDecl) Reset() {
	*x = Decl_FunctionDecl{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_api_expr_v1alpha1_checked_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Decl_FunctionDecl) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Decl_FunctionDecl) ProtoMessage() {}

func (x *Decl_FunctionDecl) ProtoReflect() protoreflect.Message {
	mi := &file_google_api_expr_v1alpha1_checked_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Decl_FunctionDecl.ProtoReflect.Descriptor instead.
func (*Decl_FunctionDecl) Descriptor() ([]byte, []int) {
	return file_google_api_expr_v1alpha1_checked_proto_rawDescGZIP(), []int{2, 1}
}

func (x *Decl_FunctionDecl) GetOverloads() []*Decl_FunctionDecl_Overload {
	if x != nil {
		return x.Overloads
	}
	return nil
}

// An overload indicates a function's parameter types and return type, and
// may optionally include a function body described in terms of
// [Expr][google.api.expr.v1alpha1.Expr] values.
//
// Functions overloads are declared in either a function or method
// call-style. For methods, the `params[0]` is the expected type of the
// target receiver.
//
// Overloads must have non-overlapping argument types after erasure of all
// parameterized type variables (similar as type erasure in Java).
type Decl_FunctionDecl_Overload struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Required. Globally unique overload name of the function which reflects
	// the function name and argument types.
	//
	// This will be used by a [Reference][google.api.expr.v1alpha1.Reference]
	// to indicate the `overload_id` that was resolved for the function
	// `name`.
	OverloadId string `protobuf:"bytes,1,opt,name=overload_id,json=overloadId,proto3" json:"overload_id,omitempty"`
	// List of function parameter [Type][google.api.expr.v1alpha1.Type]
	// values.
	//
	// Param types are disjoint after generic type parameters have been
	// replaced with the type `DYN`. Since the `DYN` type is compatible with
	// any other type, this means that if `A` is a type parameter, the
	// function types `int<A>` and `int<int>` are not disjoint. Likewise,
	// `map<string, string>` is not disjoint from `map<K, V>`.
	//
	// When the `result_type` of a function is a generic type param, the
	// type param name also appears as the `type` of on at least one params.
	Params []*Type `protobuf:"bytes,2,rep,name=params,proto3" json:"params,omitempty"`
	// The type param names associated with the function declaration.
	//
	// For example, `function ex<K,V>(K key, map<K, V> map) : V` would yield
	// the type params of `K, V`.
	TypeParams []string `protobuf:"bytes,3,rep,name=type_params,json=typeParams,proto3" json:"type_params,omitempty"`
	// Required. The result type of the function. For example, the operator
	// `string.isEmpty()` would have `result_type` of `kind: BOOL`.
	ResultType *Type `protobuf:"bytes,4,opt,name=result_type,json=resultType,proto3" json:"result_type,omitempty"`
	// Whether the function is to be used in a method call-style `x.f(...)`
	// or a function call-style `f(x, ...)`.
	//
	// For methods, the first parameter declaration, `params[0]` is the
	// expected type of the target receiver.
	IsInstanceFunction bool `protobuf:"varint,5,opt,name=is_instance_function,json=isInstanceFunction,proto3" json:"is_instance_function,omitempty"`
	// Documentation string for the overload.
	Doc string `protobuf:"bytes,6,opt,name=doc,proto3" json:"doc,omitempty"`
}

func (x *Decl_FunctionDecl_Overload) Reset() {
	*x = Decl_FunctionDecl_Overload{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_api_expr_v1alpha1_checked_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Decl_FunctionDecl_Overload) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Decl_FunctionDecl_Overload) ProtoMessage() {}

func (x *Decl_FunctionDecl_Overload) ProtoReflect() protoreflect.Message {
	mi := &file_google_api_expr_v1alpha1_checked_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Decl_FunctionDecl_Overload.ProtoReflect.Descriptor instead.
func (*Decl_FunctionDecl_Overload) Descriptor() ([]byte, []int) {
	return file_google_api_expr_v1alpha1_checked_proto_rawDescGZIP(), []int{2, 1, 0}
}

func (x *Decl_FunctionDecl_Overload) GetOverloadId() string {
	if x != nil {
		return x.OverloadId
	}
	return ""
}

func (x *Decl_FunctionDecl_Overload) GetParams() []*Type {
	if x != nil {
		return x.Params
	}
	return nil
}

func (x *Decl_FunctionDecl_Overload) GetTypeParams() []string {
	if x != nil {
		return x.TypeParams
	}
	return nil
}

func (x *Decl_FunctionDecl_Overload) GetResultType() *Type {
	if x != nil {
		return x.ResultType
	}
	return nil
}

func (x *Decl_FunctionDecl_Overload) GetIsInstanceFunction() bool {
	if x != nil {
		return x.IsInstanceFunction
	}
	return false
}

func (x *Decl_FunctionDecl_Overload) GetDoc() string {
	if x != nil {
		return x.Doc
	}
	return ""
}

var File_google_api_expr_v1alpha1_checked_proto protoreflect.FileDescriptor

var file_google_api_expr_v1alpha1_checked_proto_rawDesc = []byte{
	0x0a, 0x26, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x65, 0x78, 0x70,
	0x72, 0x2f, 0x76, 0x31, 0x61, 0x6c, 0x70, 0x68, 0x61, 0x31, 0x2f, 0x63, 0x68, 0x65, 0x63, 0x6b,
	0x65, 0x64, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x18, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x65, 0x78, 0x70, 0x72, 0x2e, 0x76, 0x31, 0x61, 0x6c, 0x70, 0x68,
	0x61, 0x31, 0x1a, 0x25, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x65,
	0x78, 0x70, 0x72, 0x2f, 0x76, 0x31, 0x61, 0x6c, 0x70, 0x68, 0x61, 0x31, 0x2f, 0x73, 0x79, 0x6e,
	0x74, 0x61, 0x78, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1b, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x65, 0x6d, 0x70, 0x74, 0x79,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1c, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x73, 0x74, 0x72, 0x75, 0x63, 0x74, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x22, 0x9a, 0x04, 0x0a, 0x0b, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x65, 0x64,
	0x45, 0x78, 0x70, 0x72, 0x12, 0x5c, 0x0a, 0x0d, 0x72, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63,
	0x65, 0x5f, 0x6d, 0x61, 0x70, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x37, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x65, 0x78, 0x70, 0x72, 0x2e, 0x76, 0x31,
	0x61, 0x6c, 0x70, 0x68, 0x61, 0x31, 0x2e, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x65, 0x64, 0x45, 0x78,
	0x70, 0x72, 0x2e, 0x52, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x4d, 0x61, 0x70, 0x45,
	0x6e, 0x74, 0x72, 0x79, 0x52, 0x0c, 0x72, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x4d,
	0x61, 0x70, 0x12, 0x4d, 0x0a, 0x08, 0x74, 0x79, 0x70, 0x65, 0x5f, 0x6d, 0x61, 0x70, 0x18, 0x03,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x32, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x65, 0x78, 0x70, 0x72, 0x2e, 0x76, 0x31, 0x61, 0x6c, 0x70, 0x68, 0x61, 0x31, 0x2e,
	0x43, 0x68, 0x65, 0x63, 0x6b, 0x65, 0x64, 0x45, 0x78, 0x70, 0x72, 0x2e, 0x54, 0x79, 0x70, 0x65,
	0x4d, 0x61, 0x70, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x07, 0x74, 0x79, 0x70, 0x65, 0x4d, 0x61,
	0x70, 0x12, 0x45, 0x0a, 0x0b, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x69, 0x6e, 0x66, 0x6f,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x24, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x65, 0x78, 0x70, 0x72, 0x2e, 0x76, 0x31, 0x61, 0x6c, 0x70, 0x68, 0x61,
	0x31, 0x2e, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x0a, 0x73, 0x6f,
	0x75, 0x72, 0x63, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x21, 0x0a, 0x0c, 0x65, 0x78, 0x70, 0x72,
	0x5f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b,
	0x65, 0x78, 0x70, 0x72, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x32, 0x0a, 0x04, 0x65,
	0x78, 0x70, 0x72, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x65, 0x78, 0x70, 0x72, 0x2e, 0x76, 0x31, 0x61, 0x6c,
	0x70, 0x68, 0x61, 0x31, 0x2e, 0x45, 0x78, 0x70, 0x72, 0x52, 0x04, 0x65, 0x78, 0x70, 0x72, 0x1a,
	0x64, 0x0a, 0x11, 0x52, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x4d, 0x61, 0x70, 0x45,
	0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x39, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x65, 0x78, 0x70, 0x72, 0x2e, 0x76, 0x31, 0x61, 0x6c, 0x70, 0x68, 0x61, 0x31,
	0x2e, 0x52, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75,
	0x65, 0x3a, 0x02, 0x38, 0x01, 0x1a, 0x5a, 0x0a, 0x0c, 0x54, 0x79, 0x70, 0x65, 0x4d, 0x61, 0x70,
	0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x34, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x65, 0x78, 0x70, 0x72, 0x2e, 0x76, 0x31, 0x61, 0x6c, 0x70, 0x68, 0x61,
	0x31, 0x2e, 0x54, 0x79, 0x70, 0x65, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38,
	0x01, 0x22, 0xc8, 0x0b, 0x0a, 0x04, 0x54, 0x79, 0x70, 0x65, 0x12, 0x2a, 0x0a, 0x03, 0x64, 0x79,
	0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x48,
	0x00, 0x52, 0x03, 0x64, 0x79, 0x6e, 0x12, 0x30, 0x0a, 0x04, 0x6e, 0x75, 0x6c, 0x6c, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x4e, 0x75, 0x6c, 0x6c, 0x56, 0x61, 0x6c, 0x75, 0x65,
	0x48, 0x00, 0x52, 0x04, 0x6e, 0x75, 0x6c, 0x6c, 0x12, 0x4c, 0x0a, 0x09, 0x70, 0x72, 0x69, 0x6d,
	0x69, 0x74, 0x69, 0x76, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2c, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x65, 0x78, 0x70, 0x72, 0x2e, 0x76, 0x31,
	0x61, 0x6c, 0x70, 0x68, 0x61, 0x31, 0x2e, 0x54, 0x79, 0x70, 0x65, 0x2e, 0x50, 0x72, 0x69, 0x6d,
	0x69, 0x74, 0x69, 0x76, 0x65, 0x54, 0x79, 0x70, 0x65, 0x48, 0x00, 0x52, 0x09, 0x70, 0x72, 0x69,
	0x6d, 0x69, 0x74, 0x69, 0x76, 0x65, 0x12, 0x48, 0x0a, 0x07, 0x77, 0x72, 0x61, 0x70, 0x70, 0x65,
	0x72, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x65, 0x78, 0x70, 0x72, 0x2e, 0x76, 0x31, 0x61, 0x6c, 0x70, 0x68,
	0x61, 0x31, 0x2e, 0x54, 0x79, 0x70, 0x65, 0x2e, 0x50, 0x72, 0x69, 0x6d, 0x69, 0x74, 0x69, 0x76,
	0x65, 0x54, 0x79, 0x70, 0x65, 0x48, 0x00, 0x52, 0x07, 0x77, 0x72, 0x61, 0x70, 0x70, 0x65, 0x72,
	0x12, 0x4d, 0x0a, 0x0a, 0x77, 0x65, 0x6c, 0x6c, 0x5f, 0x6b, 0x6e, 0x6f, 0x77, 0x6e, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x2c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x65, 0x78, 0x70, 0x72, 0x2e, 0x76, 0x31, 0x61, 0x6c, 0x70, 0x68, 0x61, 0x31, 0x2e,
	0x54, 0x79, 0x70, 0x65, 0x2e, 0x57, 0x65, 0x6c, 0x6c, 0x4b, 0x6e, 0x6f, 0x77, 0x6e, 0x54, 0x79,
	0x70, 0x65, 0x48, 0x00, 0x52, 0x09, 0x77, 0x65, 0x6c, 0x6c, 0x4b, 0x6e, 0x6f, 0x77, 0x6e, 0x12,
	0x46, 0x0a, 0x09, 0x6c, 0x69, 0x73, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x06, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x27, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x65, 0x78, 0x70, 0x72, 0x2e, 0x76, 0x31, 0x61, 0x6c, 0x70, 0x68, 0x61, 0x31, 0x2e, 0x54, 0x79,
	0x70, 0x65, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x54, 0x79, 0x70, 0x65, 0x48, 0x00, 0x52, 0x08, 0x6c,
	0x69, 0x73, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x43, 0x0a, 0x08, 0x6d, 0x61, 0x70, 0x5f, 0x74,
	0x79, 0x70, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x26, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x65, 0x78, 0x70, 0x72, 0x2e, 0x76, 0x31, 0x61, 0x6c,
	0x70, 0x68, 0x61, 0x31, 0x2e, 0x54, 0x79, 0x70, 0x65, 0x2e, 0x4d, 0x61, 0x70, 0x54, 0x79, 0x70,
	0x65, 0x48, 0x00, 0x52, 0x07, 0x6d, 0x61, 0x70, 0x54, 0x79, 0x70, 0x65, 0x12, 0x49, 0x0a, 0x08,
	0x66, 0x75, 0x6e, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2b,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x65, 0x78, 0x70, 0x72,
	0x2e, 0x76, 0x31, 0x61, 0x6c, 0x70, 0x68, 0x61, 0x31, 0x2e, 0x54, 0x79, 0x70, 0x65, 0x2e, 0x46,
	0x75, 0x6e, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x48, 0x00, 0x52, 0x08, 0x66,
	0x75, 0x6e, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x23, 0x0a, 0x0c, 0x6d, 0x65, 0x73, 0x73, 0x61,
	0x67, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00, 0x52,
	0x0b, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1f, 0x0a, 0x0a,
	0x74, 0x79, 0x70, 0x65, 0x5f, 0x70, 0x61, 0x72, 0x61, 0x6d, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09,
	0x48, 0x00, 0x52, 0x09, 0x74, 0x79, 0x70, 0x65, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x12, 0x34, 0x0a,
	0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x65, 0x78, 0x70, 0x72, 0x2e, 0x76, 0x31,
	0x61, 0x6c, 0x70, 0x68, 0x61, 0x31, 0x2e, 0x54, 0x79, 0x70, 0x65, 0x48, 0x00, 0x52, 0x04, 0x74,
	0x79, 0x70, 0x65, 0x12, 0x2e, 0x0a, 0x05, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x18, 0x0c, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x48, 0x00, 0x52, 0x05, 0x65, 0x72,
	0x72, 0x6f, 0x72, 0x12, 0x52, 0x0a, 0x0d, 0x61, 0x62, 0x73, 0x74, 0x72, 0x61, 0x63, 0x74, 0x5f,
	0x74, 0x79, 0x70, 0x65, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2b, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x65, 0x78, 0x70, 0x72, 0x2e, 0x76, 0x31, 0x61,
	0x6c, 0x70, 0x68, 0x61, 0x31, 0x2e, 0x54, 0x79, 0x70, 0x65, 0x2e, 0x41, 0x62, 0x73, 0x74, 0x72,
	0x61, 0x63, 0x74, 0x54, 0x79, 0x70, 0x65, 0x48, 0x00, 0x52, 0x0c, 0x61, 0x62, 0x73, 0x74, 0x72,
	0x61, 0x63, 0x74, 0x54, 0x79, 0x70, 0x65, 0x1a, 0x47, 0x0a, 0x08, 0x4c, 0x69, 0x73, 0x74, 0x54,
	0x79, 0x70, 0x65, 0x12, 0x3b, 0x0a, 0x09, 0x65, 0x6c, 0x65, 0x6d, 0x5f, 0x74, 0x79, 0x70, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x65, 0x78, 0x70, 0x72, 0x2e, 0x76, 0x31, 0x61, 0x6c, 0x70, 0x68, 0x61,
	0x31, 0x2e, 0x54, 0x79, 0x70, 0x65, 0x52, 0x08, 0x65, 0x6c, 0x65, 0x6d, 0x54, 0x79, 0x70, 0x65,
	0x1a, 0x83, 0x01, 0x0a, 0x07, 0x4d, 0x61, 0x70, 0x54, 0x79, 0x70, 0x65, 0x12, 0x39, 0x0a, 0x08,
	0x6b, 0x65, 0x79, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x65, 0x78, 0x70, 0x72,
	0x2e, 0x76, 0x31, 0x61, 0x6c, 0x70, 0x68, 0x61, 0x31, 0x2e, 0x54, 0x79, 0x70, 0x65, 0x52, 0x07,
	0x6b, 0x65, 0x79, 0x54, 0x79, 0x70, 0x65, 0x12, 0x3d, 0x0a, 0x0a, 0x76, 0x61, 0x6c, 0x75, 0x65,
	0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x65, 0x78, 0x70, 0x72, 0x2e, 0x76, 0x31,
	0x61, 0x6c, 0x70, 0x68, 0x61, 0x31, 0x2e, 0x54, 0x79, 0x70, 0x65, 0x52, 0x09, 0x76, 0x61, 0x6c,
	0x75, 0x65, 0x54, 0x79, 0x70, 0x65, 0x1a, 0x8c, 0x01, 0x0a, 0x0c, 0x46, 0x75, 0x6e, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x12, 0x3f, 0x0a, 0x0b, 0x72, 0x65, 0x73, 0x75, 0x6c,
	0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x65, 0x78, 0x70, 0x72, 0x2e, 0x76,
	0x31, 0x61, 0x6c, 0x70, 0x68, 0x61, 0x31, 0x2e, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0a, 0x72, 0x65,
	0x73, 0x75, 0x6c, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x3b, 0x0a, 0x09, 0x61, 0x72, 0x67, 0x5f,
	0x74, 0x79, 0x70, 0x65, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x65, 0x78, 0x70, 0x72, 0x2e, 0x76, 0x31,
	0x61, 0x6c, 0x70, 0x68, 0x61, 0x31, 0x2e, 0x54, 0x79, 0x70, 0x65, 0x52, 0x08, 0x61, 0x72, 0x67,
	0x54, 0x79, 0x70, 0x65, 0x73, 0x1a, 0x6b, 0x0a, 0x0c, 0x41, 0x62, 0x73, 0x74, 0x72, 0x61, 0x63,
	0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x47, 0x0a, 0x0f, 0x70, 0x61, 0x72,
	0x61, 0x6d, 0x65, 0x74, 0x65, 0x72, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x18, 0x02, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x65, 0x78, 0x70, 0x72, 0x2e, 0x76, 0x31, 0x61, 0x6c, 0x70, 0x68, 0x61, 0x31, 0x2e, 0x54, 0x79,
	0x70, 0x65, 0x52, 0x0e, 0x70, 0x61, 0x72, 0x61, 0x6d, 0x65, 0x74, 0x65, 0x72, 0x54, 0x79, 0x70,
	0x65, 0x73, 0x22, 0x73, 0x0a, 0x0d, 0x50, 0x72, 0x69, 0x6d, 0x69, 0x74, 0x69, 0x76, 0x65, 0x54,
	0x79, 0x70, 0x65, 0x12, 0x1e, 0x0a, 0x1a, 0x50, 0x52, 0x49, 0x4d, 0x49, 0x54, 0x49, 0x56, 0x45,
	0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45,
	0x44, 0x10, 0x00, 0x12, 0x08, 0x0a, 0x04, 0x42, 0x4f, 0x4f, 0x4c, 0x10, 0x01, 0x12, 0x09, 0x0a,
	0x05, 0x49, 0x4e, 0x54, 0x36, 0x34, 0x10, 0x02, 0x12, 0x0a, 0x0a, 0x06, 0x55, 0x49, 0x4e, 0x54,
	0x36, 0x34, 0x10, 0x03, 0x12, 0x0a, 0x0a, 0x06, 0x44, 0x4f, 0x55, 0x42, 0x4c, 0x45, 0x10, 0x04,
	0x12, 0x0a, 0x0a, 0x06, 0x53, 0x54, 0x52, 0x49, 0x4e, 0x47, 0x10, 0x05, 0x12, 0x09, 0x0a, 0x05,
	0x42, 0x59, 0x54, 0x45, 0x53, 0x10, 0x06, 0x22, 0x56, 0x0a, 0x0d, 0x57, 0x65, 0x6c, 0x6c, 0x4b,
	0x6e, 0x6f, 0x77, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1f, 0x0a, 0x1b, 0x57, 0x45, 0x4c, 0x4c,
	0x5f, 0x4b, 0x4e, 0x4f, 0x57, 0x4e, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50,
	0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x07, 0x0a, 0x03, 0x41, 0x4e, 0x59,
	0x10, 0x01, 0x12, 0x0d, 0x0a, 0x09, 0x54, 0x49, 0x4d, 0x45, 0x53, 0x54, 0x41, 0x4d, 0x50, 0x10,
	0x02, 0x12, 0x0c, 0x0a, 0x08, 0x44, 0x55, 0x52, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0x03, 0x42,
	0x0b, 0x0a, 0x09, 0x74, 0x79, 0x70, 0x65, 0x5f, 0x6b, 0x69, 0x6e, 0x64, 0x22, 0xb3, 0x05, 0x0a,
	0x04, 0x44, 0x65, 0x63, 0x6c, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x40, 0x0a, 0x05, 0x69, 0x64, 0x65,
	0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x28, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x65, 0x78, 0x70, 0x72, 0x2e, 0x76, 0x31, 0x61, 0x6c, 0x70,
	0x68, 0x61, 0x31, 0x2e, 0x44, 0x65, 0x63, 0x6c, 0x2e, 0x49, 0x64, 0x65, 0x6e, 0x74, 0x44, 0x65,
	0x63, 0x6c, 0x48, 0x00, 0x52, 0x05, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x12, 0x49, 0x0a, 0x08, 0x66,
	0x75, 0x6e, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2b, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x65, 0x78, 0x70, 0x72, 0x2e,
	0x76, 0x31, 0x61, 0x6c, 0x70, 0x68, 0x61, 0x31, 0x2e, 0x44, 0x65, 0x63, 0x6c, 0x2e, 0x46, 0x75,
	0x6e, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x65, 0x63, 0x6c, 0x48, 0x00, 0x52, 0x08, 0x66, 0x75,
	0x6e, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x1a, 0x8b, 0x01, 0x0a, 0x09, 0x49, 0x64, 0x65, 0x6e, 0x74,
	0x44, 0x65, 0x63, 0x6c, 0x12, 0x32, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x65, 0x78, 0x70, 0x72, 0x2e, 0x76, 0x31, 0x61, 0x6c, 0x70, 0x68, 0x61, 0x31, 0x2e, 0x54, 0x79,
	0x70, 0x65, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x38, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x65, 0x78, 0x70, 0x72, 0x2e, 0x76, 0x31, 0x61, 0x6c, 0x70, 0x68,
	0x61, 0x31, 0x2e, 0x43, 0x6f, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x74, 0x52, 0x05, 0x76, 0x61, 0x6c,
	0x75, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x64, 0x6f, 0x63, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x03, 0x64, 0x6f, 0x63, 0x1a, 0xee, 0x02, 0x0a, 0x0c, 0x46, 0x75, 0x6e, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x44, 0x65, 0x63, 0x6c, 0x12, 0x52, 0x0a, 0x09, 0x6f, 0x76, 0x65, 0x72, 0x6c, 0x6f, 0x61,
	0x64, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x34, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x65, 0x78, 0x70, 0x72, 0x2e, 0x76, 0x31, 0x61, 0x6c, 0x70,
	0x68, 0x61, 0x31, 0x2e, 0x44, 0x65, 0x63, 0x6c, 0x2e, 0x46, 0x75, 0x6e, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x44, 0x65, 0x63, 0x6c, 0x2e, 0x4f, 0x76, 0x65, 0x72, 0x6c, 0x6f, 0x61, 0x64, 0x52, 0x09,
	0x6f, 0x76, 0x65, 0x72, 0x6c, 0x6f, 0x61, 0x64, 0x73, 0x1a, 0x89, 0x02, 0x0a, 0x08, 0x4f, 0x76,
	0x65, 0x72, 0x6c, 0x6f, 0x61, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x6f, 0x76, 0x65, 0x72, 0x6c, 0x6f,
	0x61, 0x64, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x6f, 0x76, 0x65,
	0x72, 0x6c, 0x6f, 0x61, 0x64, 0x49, 0x64, 0x12, 0x36, 0x0a, 0x06, 0x70, 0x61, 0x72, 0x61, 0x6d,
	0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x65, 0x78, 0x70, 0x72, 0x2e, 0x76, 0x31, 0x61, 0x6c, 0x70, 0x68,
	0x61, 0x31, 0x2e, 0x54, 0x79, 0x70, 0x65, 0x52, 0x06, 0x70, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12,
	0x1f, 0x0a, 0x0b, 0x74, 0x79, 0x70, 0x65, 0x5f, 0x70, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x18, 0x03,
	0x20, 0x03, 0x28, 0x09, 0x52, 0x0a, 0x74, 0x79, 0x70, 0x65, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73,
	0x12, 0x3f, 0x0a, 0x0b, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x65, 0x78, 0x70, 0x72, 0x2e, 0x76, 0x31, 0x61, 0x6c, 0x70, 0x68, 0x61, 0x31,
	0x2e, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0a, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x54, 0x79, 0x70,
	0x65, 0x12, 0x30, 0x0a, 0x14, 0x69, 0x73, 0x5f, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65,
	0x5f, 0x66, 0x75, 0x6e, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x05, 0x20, 0x01, 0x28, 0x08, 0x52,
	0x12, 0x69, 0x73, 0x49, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x46, 0x75, 0x6e, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x12, 0x10, 0x0a, 0x03, 0x64, 0x6f, 0x63, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x03, 0x64, 0x6f, 0x63, 0x42, 0x0b, 0x0a, 0x09, 0x64, 0x65, 0x63, 0x6c, 0x5f, 0x6b, 0x69,
	0x6e, 0x64, 0x22, 0x7a, 0x0a, 0x09, 0x52, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x12,
	0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e,
	0x61, 0x6d, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x6f, 0x76, 0x65, 0x72, 0x6c, 0x6f, 0x61, 0x64, 0x5f,
	0x69, 0x64, 0x18, 0x03, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0a, 0x6f, 0x76, 0x65, 0x72, 0x6c, 0x6f,
	0x61, 0x64, 0x49, 0x64, 0x12, 0x38, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x65, 0x78, 0x70, 0x72, 0x2e, 0x76, 0x31, 0x61, 0x6c, 0x70, 0x68, 0x61, 0x31, 0x2e, 0x43,
	0x6f, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x74, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x42, 0x6c,
	0x0a, 0x1c, 0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x65, 0x78, 0x70, 0x72, 0x2e, 0x76, 0x31, 0x61, 0x6c, 0x70, 0x68, 0x61, 0x31, 0x42, 0x09,
	0x44, 0x65, 0x63, 0x6c, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x50, 0x01, 0x5a, 0x3c, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x67, 0x6f, 0x6c, 0x61, 0x6e, 0x67, 0x2e, 0x6f, 0x72, 0x67, 0x2f, 0x67,
	0x65, 0x6e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61, 0x70,
	0x69, 0x73, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x65, 0x78, 0x70, 0x72, 0x2f, 0x76, 0x31, 0x61, 0x6c,
	0x70, 0x68, 0x61, 0x31, 0x3b, 0x65, 0x78, 0x70, 0x72, 0xf8, 0x01, 0x01, 0x62, 0x06, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_google_api_expr_v1alpha1_checked_proto_rawDescOnce sync.Once
	file_google_api_expr_v1alpha1_checked_proto_rawDescData = file_google_api_expr_v1alpha1_checked_proto_rawDesc
)

func file_google_api_expr_v1alpha1_checked_proto_rawDescGZIP() []byte {
	file_google_api_expr_v1alpha1_checked_proto_rawDescOnce.Do(func() {
		file_google_api_expr_v1alpha1_checked_proto_rawDescData = protoimpl.X.CompressGZIP(file_google_api_expr_v1alpha1_checked_proto_rawDescData)
	})
	return file_google_api_expr_v1alpha1_checked_proto_rawDescData
}

var file_google_api_expr_v1alpha1_checked_proto_enumTypes = make([]protoimpl.EnumInfo, 2)
var file_google_api_expr_v1alpha1_checked_proto_msgTypes = make([]protoimpl.MessageInfo, 13)
var file_google_api_expr_v1alpha1_checked_proto_goTypes = []interface{}{
	(Type_PrimitiveType)(0),            // 0: google.api.expr.v1alpha1.Type.PrimitiveType
	(Type_WellKnownType)(0),            // 1: google.api.expr.v1alpha1.Type.WellKnownType
	(*CheckedExpr)(nil),                // 2: google.api.expr.v1alpha1.CheckedExpr
	(*Type)(nil),                       // 3: google.api.expr.v1alpha1.Type
	(*Decl)(nil),                       // 4: google.api.expr.v1alpha1.Decl
	(*Reference)(nil),                  // 5: google.api.expr.v1alpha1.Reference
	nil,                                // 6: google.api.expr.v1alpha1.CheckedExpr.ReferenceMapEntry
	nil,                                // 7: google.api.expr.v1alpha1.CheckedExpr.TypeMapEntry
	(*Type_ListType)(nil),              // 8: google.api.expr.v1alpha1.Type.ListType
	(*Type_MapType)(nil),               // 9: google.api.expr.v1alpha1.Type.MapType
	(*Type_FunctionType)(nil),          // 10: google.api.expr.v1alpha1.Type.FunctionType
	(*Type_AbstractType)(nil),          // 11: google.api.expr.v1alpha1.Type.AbstractType
	(*Decl_IdentDecl)(nil),             // 12: google.api.expr.v1alpha1.Decl.IdentDecl
	(*Decl_FunctionDecl)(nil),          // 13: google.api.expr.v1alpha1.Decl.FunctionDecl
	(*Decl_FunctionDecl_Overload)(nil), // 14: google.api.expr.v1alpha1.Decl.FunctionDecl.Overload
	(*SourceInfo)(nil),                 // 15: google.api.expr.v1alpha1.SourceInfo
	(*Expr)(nil),                       // 16: google.api.expr.v1alpha1.Expr
	(*emptypb.Empty)(nil),              // 17: google.protobuf.Empty
	(structpb.NullValue)(0),            // 18: google.protobuf.NullValue
	(*Constant)(nil),                   // 19: google.api.expr.v1alpha1.Constant
}
var file_google_api_expr_v1alpha1_checked_proto_depIdxs = []int32{
	6,  // 0: google.api.expr.v1alpha1.CheckedExpr.reference_map:type_name -> google.api.expr.v1alpha1.CheckedExpr.ReferenceMapEntry
	7,  // 1: google.api.expr.v1alpha1.CheckedExpr.type_map:type_name -> google.api.expr.v1alpha1.CheckedExpr.TypeMapEntry
	15, // 2: google.api.expr.v1alpha1.CheckedExpr.source_info:type_name -> google.api.expr.v1alpha1.SourceInfo
	16, // 3: google.api.expr.v1alpha1.CheckedExpr.expr:type_name -> google.api.expr.v1alpha1.Expr
	17, // 4: google.api.expr.v1alpha1.Type.dyn:type_name -> google.protobuf.Empty
	18, // 5: google.api.expr.v1alpha1.Type.null:type_name -> google.protobuf.NullValue
	0,  // 6: google.api.expr.v1alpha1.Type.primitive:type_name -> google.api.expr.v1alpha1.Type.PrimitiveType
	0,  // 7: google.api.expr.v1alpha1.Type.wrapper:type_name -> google.api.expr.v1alpha1.Type.PrimitiveType
	1,  // 8: google.api.expr.v1alpha1.Type.well_known:type_name -> google.api.expr.v1alpha1.Type.WellKnownType
	8,  // 9: google.api.expr.v1alpha1.Type.list_type:type_name -> google.api.expr.v1alpha1.Type.ListType
	9,  // 10: google.api.expr.v1alpha1.Type.map_type:type_name -> google.api.expr.v1alpha1.Type.MapType
	10, // 11: google.api.expr.v1alpha1.Type.function:type_name -> google.api.expr.v1alpha1.Type.FunctionType
	3,  // 12: google.api.expr.v1alpha1.Type.type:type_name -> google.api.expr.v1alpha1.Type
	17, // 13: google.api.expr.v1alpha1.Type.error:type_name -> google.protobuf.Empty
	11, // 14: google.api.expr.v1alpha1.Type.abstract_type:type_name -> google.api.expr.v1alpha1.Type.AbstractType
	12, // 15: google.api.expr.v1alpha1.Decl.ident:type_name -> google.api.expr.v1alpha1.Decl.IdentDecl
	13, // 16: google.api.expr.v1alpha1.Decl.function:type_name -> google.api.expr.v1alpha1.Decl.FunctionDecl
	19, // 17: google.api.expr.v1alpha1.Reference.value:type_name -> google.api.expr.v1alpha1.Constant
	5,  // 18: google.api.expr.v1alpha1.CheckedExpr.ReferenceMapEntry.value:type_name -> google.api.expr.v1alpha1.Reference
	3,  // 19: google.api.expr.v1alpha1.CheckedExpr.TypeMapEntry.value:type_name -> google.api.expr.v1alpha1.Type
	3,  // 20: google.api.expr.v1alpha1.Type.ListType.elem_type:type_name -> google.api.expr.v1alpha1.Type
	3,  // 21: google.api.expr.v1alpha1.Type.MapType.key_type:type_name -> google.api.expr.v1alpha1.Type
	3,  // 22: google.api.expr.v1alpha1.Type.MapType.value_type:type_name -> google.api.expr.v1alpha1.Type
	3,  // 23: google.api.expr.v1alpha1.Type.FunctionType.result_type:type_name -> google.api.expr.v1alpha1.Type
	3,  // 24: google.api.expr.v1alpha1.Type.FunctionType.arg_types:type_name -> google.api.expr.v1alpha1.Type
	3,  // 25: google.api.expr.v1alpha1.Type.AbstractType.parameter_types:type_name -> google.api.expr.v1alpha1.Type
	3,  // 26: google.api.expr.v1alpha1.Decl.IdentDecl.type:type_name -> google.api.expr.v1alpha1.Type
	19, // 27: google.api.expr.v1alpha1.Decl.IdentDecl.value:type_name -> google.api.expr.v1alpha1.Constant
	14, // 28: google.api.expr.v1alpha1.Decl.FunctionDecl.overloads:type_name -> google.api.expr.v1alpha1.Decl.FunctionDecl.Overload
	3,  // 29: google.api.expr.v1alpha1.Decl.FunctionDecl.Overload.params:type_name -> google.api.expr.v1alpha1.Type
	3,  // 30: google.api.expr.v1alpha1.Decl.FunctionDecl.Overload.result_type:type_name -> google.api.expr.v1alpha1.Type
	31, // [31:31] is the sub-list for method output_type
	31, // [31:31] is the sub-list for method input_type
	31, // [31:31] is the sub-list for extension type_name
	31, // [31:31] is the sub-list for extension extendee
	0,  // [0:31] is the sub-list for field type_name
}

func init() { file_google_api_expr_v1alpha1_checked_proto_init() }
func file_google_api_expr_v1alpha1_checked_proto_init() {
	if File_google_api_expr_v1alpha1_checked_proto != nil {
		return
	}
	file_google_api_expr_v1alpha1_syntax_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_google_api_expr_v1alpha1_checked_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CheckedExpr); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_api_expr_v1alpha1_checked_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Type); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_api_expr_v1alpha1_checked_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Decl); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_api_expr_v1alpha1_checked_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Reference); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_api_expr_v1alpha1_checked_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Type_ListType); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_api_expr_v1alpha1_checked_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Type_MapType); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_api_expr_v1alpha1_checked_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Type_FunctionType); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_api_expr_v1alpha1_checked_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Type_AbstractType); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_api_expr_v1alpha1_checked_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Decl_IdentDecl); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_api_expr_v1alpha1_checked_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Decl_FunctionDecl); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_api_expr_v1alpha1_checked_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Decl_FunctionDecl_Overload); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_google_api_expr_v1alpha1_checked_proto_msgTypes[1].OneofWrappers = []interface{}{
		(*Type_Dyn)(nil),
		(*Type_Null)(nil),
		(*Type_Primitive)(nil),
		(*Type_Wrapper)(nil),
		(*Type_WellKnown)(nil),
		(*Type_ListType_)(nil),
		(*Type_MapType_)(nil),
		(*Type_Function)(nil),
		(*Type_MessageType)(nil),
		(*Type_TypeParam)(nil),
		(*Type_Type)(nil),
		(*Type_Error)(nil),
		(*Type_AbstractType_)(nil),
	}
	file_google_api_expr_v1alpha1_checked_proto_msgTypes[2].OneofWrappers = []interface{}{
		(*Decl_Ident)(nil),
		(*Decl_Function)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_google_api_expr_v1alpha1_checked_proto_rawDesc,
			NumEnums:      2,
			NumMessages:   13,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_google_api_expr_v1alpha1_checked_proto_goTypes,
		DependencyIndexes: file_google_api_expr_v1alpha1_checked_proto_depIdxs,
		EnumInfos:         file_google_api_expr_v1alpha1_checked_proto_enumTypes,
		MessageInfos:      file_google_api_expr_v1alpha1_checked_proto_msgTypes,
	}.Build()
	File_google_api_expr_v1alpha1_checked_proto = out.File
	file_google_api_expr_v1alpha1_checked_proto_rawDesc = nil
	file_google_api_expr_v1alpha1_checked_proto_goTypes = nil
	file_google_api_expr_v1alpha1_checked_proto_depIdxs = nil
}
