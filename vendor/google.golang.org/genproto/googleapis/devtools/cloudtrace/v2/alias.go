// Copyright 2022 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

// Code generated by aliasgen. DO NOT EDIT.

// Package trace aliases all exported identifiers in package
// "cloud.google.com/go/trace/apiv2/tracepb".
//
// Deprecated: Please use types in: cloud.google.com/go/trace/apiv2/tracepb.
// Please read https://github.com/googleapis/google-cloud-go/blob/main/migration.md
// for more details.
package trace

import (
	src "cloud.google.com/go/trace/apiv2/tracepb"
	grpc "google.golang.org/grpc"
)

// Deprecated: Please use consts in: cloud.google.com/go/trace/apiv2/tracepb
const (
	Span_CLIENT                                  = src.Span_CLIENT
	Span_CONSUMER                                = src.Span_CONSUMER
	Span_INTERNAL                                = src.Span_INTERNAL
	Span_Link_CHILD_LINKED_SPAN                  = src.Span_Link_CHILD_LINKED_SPAN
	Span_Link_PARENT_LINKED_SPAN                 = src.Span_Link_PARENT_LINKED_SPAN
	Span_Link_TYPE_UNSPECIFIED                   = src.Span_Link_TYPE_UNSPECIFIED
	Span_PRODUCER                                = src.Span_PRODUCER
	Span_SERVER                                  = src.Span_SERVER
	Span_SPAN_KIND_UNSPECIFIED                   = src.Span_SPAN_KIND_UNSPECIFIED
	Span_TimeEvent_MessageEvent_RECEIVED         = src.Span_TimeEvent_MessageEvent_RECEIVED
	Span_TimeEvent_MessageEvent_SENT             = src.Span_TimeEvent_MessageEvent_SENT
	Span_TimeEvent_MessageEvent_TYPE_UNSPECIFIED = src.Span_TimeEvent_MessageEvent_TYPE_UNSPECIFIED
)

// Deprecated: Please use vars in: cloud.google.com/go/trace/apiv2/tracepb
var (
	File_google_devtools_cloudtrace_v2_trace_proto   = src.File_google_devtools_cloudtrace_v2_trace_proto
	File_google_devtools_cloudtrace_v2_tracing_proto = src.File_google_devtools_cloudtrace_v2_tracing_proto
	Span_Link_Type_name                              = src.Span_Link_Type_name
	Span_Link_Type_value                             = src.Span_Link_Type_value
	Span_SpanKind_name                               = src.Span_SpanKind_name
	Span_SpanKind_value                              = src.Span_SpanKind_value
	Span_TimeEvent_MessageEvent_Type_name            = src.Span_TimeEvent_MessageEvent_Type_name
	Span_TimeEvent_MessageEvent_Type_value           = src.Span_TimeEvent_MessageEvent_Type_value
)

// The allowed types for [VALUE] in a `[KEY]:[VALUE]` attribute.
//
// Deprecated: Please use types in: cloud.google.com/go/trace/apiv2/tracepb
type AttributeValue = src.AttributeValue
type AttributeValue_BoolValue = src.AttributeValue_BoolValue
type AttributeValue_IntValue = src.AttributeValue_IntValue
type AttributeValue_StringValue = src.AttributeValue_StringValue

// The request message for the `BatchWriteSpans` method.
//
// Deprecated: Please use types in: cloud.google.com/go/trace/apiv2/tracepb
type BatchWriteSpansRequest = src.BatchWriteSpansRequest

// Binary module.
//
// Deprecated: Please use types in: cloud.google.com/go/trace/apiv2/tracepb
type Module = src.Module

// A span represents a single operation within a trace. Spans can be nested to
// form a trace tree. Often, a trace contains a root span that describes the
// end-to-end latency, and one or more subspans for its sub-operations. A trace
// can also contain multiple root spans, or none at all. Spans do not need to
// be contiguous&mdash;there may be gaps or overlaps between spans in a trace.
//
// Deprecated: Please use types in: cloud.google.com/go/trace/apiv2/tracepb
type Span = src.Span

// A set of attributes, each in the format `[KEY]:[VALUE]`.
//
// Deprecated: Please use types in: cloud.google.com/go/trace/apiv2/tracepb
type Span_Attributes = src.Span_Attributes

// A pointer from the current span to another span in the same trace or in a
// different trace. For example, this can be used in batching operations, where
// a single batch handler processes multiple requests from different traces or
// when the handler receives a request from a different project.
//
// Deprecated: Please use types in: cloud.google.com/go/trace/apiv2/tracepb
type Span_Link = src.Span_Link

// The relationship of the current span relative to the linked span: child,
// parent, or unspecified.
//
// Deprecated: Please use types in: cloud.google.com/go/trace/apiv2/tracepb
type Span_Link_Type = src.Span_Link_Type

// A collection of links, which are references from this span to a span in the
// same or different trace.
//
// Deprecated: Please use types in: cloud.google.com/go/trace/apiv2/tracepb
type Span_Links = src.Span_Links

// Type of span. Can be used to specify additional relationships between spans
// in addition to a parent/child relationship.
//
// Deprecated: Please use types in: cloud.google.com/go/trace/apiv2/tracepb
type Span_SpanKind = src.Span_SpanKind

// A time-stamped annotation or message event in the Span.
//
// Deprecated: Please use types in: cloud.google.com/go/trace/apiv2/tracepb
type Span_TimeEvent = src.Span_TimeEvent

// Text annotation with a set of attributes.
//
// Deprecated: Please use types in: cloud.google.com/go/trace/apiv2/tracepb
type Span_TimeEvent_Annotation = src.Span_TimeEvent_Annotation
type Span_TimeEvent_Annotation_ = src.Span_TimeEvent_Annotation_

// An event describing a message sent/received between Spans.
//
// Deprecated: Please use types in: cloud.google.com/go/trace/apiv2/tracepb
type Span_TimeEvent_MessageEvent = src.Span_TimeEvent_MessageEvent
type Span_TimeEvent_MessageEvent_ = src.Span_TimeEvent_MessageEvent_

// Indicates whether the message was sent or received.
//
// Deprecated: Please use types in: cloud.google.com/go/trace/apiv2/tracepb
type Span_TimeEvent_MessageEvent_Type = src.Span_TimeEvent_MessageEvent_Type

// A collection of `TimeEvent`s. A `TimeEvent` is a time-stamped annotation on
// the span, consisting of either user-supplied key:value pairs, or details of
// a message sent/received between Spans.
//
// Deprecated: Please use types in: cloud.google.com/go/trace/apiv2/tracepb
type Span_TimeEvents = src.Span_TimeEvents

// A call stack appearing in a trace.
//
// Deprecated: Please use types in: cloud.google.com/go/trace/apiv2/tracepb
type StackTrace = src.StackTrace

// Represents a single stack frame in a stack trace.
//
// Deprecated: Please use types in: cloud.google.com/go/trace/apiv2/tracepb
type StackTrace_StackFrame = src.StackTrace_StackFrame

// A collection of stack frames, which can be truncated.
//
// Deprecated: Please use types in: cloud.google.com/go/trace/apiv2/tracepb
type StackTrace_StackFrames = src.StackTrace_StackFrames

// TraceServiceClient is the client API for TraceService service. For
// semantics around ctx use and closing/ending streaming RPCs, please refer to
// https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
//
// Deprecated: Please use types in: cloud.google.com/go/trace/apiv2/tracepb
type TraceServiceClient = src.TraceServiceClient

// TraceServiceServer is the server API for TraceService service.
//
// Deprecated: Please use types in: cloud.google.com/go/trace/apiv2/tracepb
type TraceServiceServer = src.TraceServiceServer

// Represents a string that might be shortened to a specified length.
//
// Deprecated: Please use types in: cloud.google.com/go/trace/apiv2/tracepb
type TruncatableString = src.TruncatableString

// UnimplementedTraceServiceServer can be embedded to have forward compatible
// implementations.
//
// Deprecated: Please use types in: cloud.google.com/go/trace/apiv2/tracepb
type UnimplementedTraceServiceServer = src.UnimplementedTraceServiceServer

// Deprecated: Please use funcs in: cloud.google.com/go/trace/apiv2/tracepb
func NewTraceServiceClient(cc grpc.ClientConnInterface) TraceServiceClient {
	return src.NewTraceServiceClient(cc)
}

// Deprecated: Please use funcs in: cloud.google.com/go/trace/apiv2/tracepb
func RegisterTraceServiceServer(s *grpc.Server, srv TraceServiceServer) {
	src.RegisterTraceServiceServer(s, srv)
}
