// Copyright 2018 The gRPC Authors
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

// The canonical version of this proto can be found at
// https://github.com/grpc/grpc-proto/blob/master/grpc/gcp/transport_security_common.proto

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v5.27.1
// source: grpc/gcp/transport_security_common.proto

package grpc_gcp

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// The security level of the created channel. The list is sorted in increasing
// level of security. This order must always be maintained.
type SecurityLevel int32

const (
	SecurityLevel_SECURITY_NONE         SecurityLevel = 0
	SecurityLevel_INTEGRITY_ONLY        SecurityLevel = 1
	SecurityLevel_INTEGRITY_AND_PRIVACY SecurityLevel = 2
)

// Enum value maps for SecurityLevel.
var (
	SecurityLevel_name = map[int32]string{
		0: "SECURITY_NONE",
		1: "INTEGRITY_ONLY",
		2: "INTEGRITY_AND_PRIVACY",
	}
	SecurityLevel_value = map[string]int32{
		"SECURITY_NONE":         0,
		"INTEGRITY_ONLY":        1,
		"INTEGRITY_AND_PRIVACY": 2,
	}
)

func (x SecurityLevel) Enum() *SecurityLevel {
	p := new(SecurityLevel)
	*p = x
	return p
}

func (x SecurityLevel) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (SecurityLevel) Descriptor() protoreflect.EnumDescriptor {
	return file_grpc_gcp_transport_security_common_proto_enumTypes[0].Descriptor()
}

func (SecurityLevel) Type() protoreflect.EnumType {
	return &file_grpc_gcp_transport_security_common_proto_enumTypes[0]
}

func (x SecurityLevel) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use SecurityLevel.Descriptor instead.
func (SecurityLevel) EnumDescriptor() ([]byte, []int) {
	return file_grpc_gcp_transport_security_common_proto_rawDescGZIP(), []int{0}
}

// Max and min supported RPC protocol versions.
type RpcProtocolVersions struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Maximum supported RPC version.
	MaxRpcVersion *RpcProtocolVersions_Version `protobuf:"bytes,1,opt,name=max_rpc_version,json=maxRpcVersion,proto3" json:"max_rpc_version,omitempty"`
	// Minimum supported RPC version.
	MinRpcVersion *RpcProtocolVersions_Version `protobuf:"bytes,2,opt,name=min_rpc_version,json=minRpcVersion,proto3" json:"min_rpc_version,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RpcProtocolVersions) Reset() {
	*x = RpcProtocolVersions{}
	mi := &file_grpc_gcp_transport_security_common_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RpcProtocolVersions) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RpcProtocolVersions) ProtoMessage() {}

func (x *RpcProtocolVersions) ProtoReflect() protoreflect.Message {
	mi := &file_grpc_gcp_transport_security_common_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RpcProtocolVersions.ProtoReflect.Descriptor instead.
func (*RpcProtocolVersions) Descriptor() ([]byte, []int) {
	return file_grpc_gcp_transport_security_common_proto_rawDescGZIP(), []int{0}
}

func (x *RpcProtocolVersions) GetMaxRpcVersion() *RpcProtocolVersions_Version {
	if x != nil {
		return x.MaxRpcVersion
	}
	return nil
}

func (x *RpcProtocolVersions) GetMinRpcVersion() *RpcProtocolVersions_Version {
	if x != nil {
		return x.MinRpcVersion
	}
	return nil
}

// The ordered list of protocols that the client wishes to use, or the set
// that the server supports.
type TransportProtocolPreferences struct {
	state             protoimpl.MessageState `protogen:"open.v1"`
	TransportProtocol []string               `protobuf:"bytes,1,rep,name=transport_protocol,json=transportProtocol,proto3" json:"transport_protocol,omitempty"`
	unknownFields     protoimpl.UnknownFields
	sizeCache         protoimpl.SizeCache
}

func (x *TransportProtocolPreferences) Reset() {
	*x = TransportProtocolPreferences{}
	mi := &file_grpc_gcp_transport_security_common_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TransportProtocolPreferences) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TransportProtocolPreferences) ProtoMessage() {}

func (x *TransportProtocolPreferences) ProtoReflect() protoreflect.Message {
	mi := &file_grpc_gcp_transport_security_common_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TransportProtocolPreferences.ProtoReflect.Descriptor instead.
func (*TransportProtocolPreferences) Descriptor() ([]byte, []int) {
	return file_grpc_gcp_transport_security_common_proto_rawDescGZIP(), []int{1}
}

func (x *TransportProtocolPreferences) GetTransportProtocol() []string {
	if x != nil {
		return x.TransportProtocol
	}
	return nil
}

// The negotiated transport protocol.
type NegotiatedTransportProtocol struct {
	state             protoimpl.MessageState `protogen:"open.v1"`
	TransportProtocol string                 `protobuf:"bytes,1,opt,name=transport_protocol,json=transportProtocol,proto3" json:"transport_protocol,omitempty"`
	unknownFields     protoimpl.UnknownFields
	sizeCache         protoimpl.SizeCache
}

func (x *NegotiatedTransportProtocol) Reset() {
	*x = NegotiatedTransportProtocol{}
	mi := &file_grpc_gcp_transport_security_common_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *NegotiatedTransportProtocol) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NegotiatedTransportProtocol) ProtoMessage() {}

func (x *NegotiatedTransportProtocol) ProtoReflect() protoreflect.Message {
	mi := &file_grpc_gcp_transport_security_common_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NegotiatedTransportProtocol.ProtoReflect.Descriptor instead.
func (*NegotiatedTransportProtocol) Descriptor() ([]byte, []int) {
	return file_grpc_gcp_transport_security_common_proto_rawDescGZIP(), []int{2}
}

func (x *NegotiatedTransportProtocol) GetTransportProtocol() string {
	if x != nil {
		return x.TransportProtocol
	}
	return ""
}

// RPC version contains a major version and a minor version.
type RpcProtocolVersions_Version struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Major         uint32                 `protobuf:"varint,1,opt,name=major,proto3" json:"major,omitempty"`
	Minor         uint32                 `protobuf:"varint,2,opt,name=minor,proto3" json:"minor,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RpcProtocolVersions_Version) Reset() {
	*x = RpcProtocolVersions_Version{}
	mi := &file_grpc_gcp_transport_security_common_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RpcProtocolVersions_Version) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RpcProtocolVersions_Version) ProtoMessage() {}

func (x *RpcProtocolVersions_Version) ProtoReflect() protoreflect.Message {
	mi := &file_grpc_gcp_transport_security_common_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RpcProtocolVersions_Version.ProtoReflect.Descriptor instead.
func (*RpcProtocolVersions_Version) Descriptor() ([]byte, []int) {
	return file_grpc_gcp_transport_security_common_proto_rawDescGZIP(), []int{0, 0}
}

func (x *RpcProtocolVersions_Version) GetMajor() uint32 {
	if x != nil {
		return x.Major
	}
	return 0
}

func (x *RpcProtocolVersions_Version) GetMinor() uint32 {
	if x != nil {
		return x.Minor
	}
	return 0
}

var File_grpc_gcp_transport_security_common_proto protoreflect.FileDescriptor

const file_grpc_gcp_transport_security_common_proto_rawDesc = "" +
	"\n" +
	"(grpc/gcp/transport_security_common.proto\x12\bgrpc.gcp\"\xea\x01\n" +
	"\x13RpcProtocolVersions\x12M\n" +
	"\x0fmax_rpc_version\x18\x01 \x01(\v2%.grpc.gcp.RpcProtocolVersions.VersionR\rmaxRpcVersion\x12M\n" +
	"\x0fmin_rpc_version\x18\x02 \x01(\v2%.grpc.gcp.RpcProtocolVersions.VersionR\rminRpcVersion\x1a5\n" +
	"\aVersion\x12\x14\n" +
	"\x05major\x18\x01 \x01(\rR\x05major\x12\x14\n" +
	"\x05minor\x18\x02 \x01(\rR\x05minor\"M\n" +
	"\x1cTransportProtocolPreferences\x12-\n" +
	"\x12transport_protocol\x18\x01 \x03(\tR\x11transportProtocol\"L\n" +
	"\x1bNegotiatedTransportProtocol\x12-\n" +
	"\x12transport_protocol\x18\x01 \x01(\tR\x11transportProtocol*Q\n" +
	"\rSecurityLevel\x12\x11\n" +
	"\rSECURITY_NONE\x10\x00\x12\x12\n" +
	"\x0eINTEGRITY_ONLY\x10\x01\x12\x19\n" +
	"\x15INTEGRITY_AND_PRIVACY\x10\x02Bx\n" +
	"\x15io.grpc.alts.internalB\x1cTransportSecurityCommonProtoP\x01Z?google.golang.org/grpc/credentials/alts/internal/proto/grpc_gcpb\x06proto3"

var (
	file_grpc_gcp_transport_security_common_proto_rawDescOnce sync.Once
	file_grpc_gcp_transport_security_common_proto_rawDescData []byte
)

func file_grpc_gcp_transport_security_common_proto_rawDescGZIP() []byte {
	file_grpc_gcp_transport_security_common_proto_rawDescOnce.Do(func() {
		file_grpc_gcp_transport_security_common_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_grpc_gcp_transport_security_common_proto_rawDesc), len(file_grpc_gcp_transport_security_common_proto_rawDesc)))
	})
	return file_grpc_gcp_transport_security_common_proto_rawDescData
}

var file_grpc_gcp_transport_security_common_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_grpc_gcp_transport_security_common_proto_msgTypes = make([]protoimpl.MessageInfo, 4)
var file_grpc_gcp_transport_security_common_proto_goTypes = []any{
	(SecurityLevel)(0),                   // 0: grpc.gcp.SecurityLevel
	(*RpcProtocolVersions)(nil),          // 1: grpc.gcp.RpcProtocolVersions
	(*TransportProtocolPreferences)(nil), // 2: grpc.gcp.TransportProtocolPreferences
	(*NegotiatedTransportProtocol)(nil),  // 3: grpc.gcp.NegotiatedTransportProtocol
	(*RpcProtocolVersions_Version)(nil),  // 4: grpc.gcp.RpcProtocolVersions.Version
}
var file_grpc_gcp_transport_security_common_proto_depIdxs = []int32{
	4, // 0: grpc.gcp.RpcProtocolVersions.max_rpc_version:type_name -> grpc.gcp.RpcProtocolVersions.Version
	4, // 1: grpc.gcp.RpcProtocolVersions.min_rpc_version:type_name -> grpc.gcp.RpcProtocolVersions.Version
	2, // [2:2] is the sub-list for method output_type
	2, // [2:2] is the sub-list for method input_type
	2, // [2:2] is the sub-list for extension type_name
	2, // [2:2] is the sub-list for extension extendee
	0, // [0:2] is the sub-list for field type_name
}

func init() { file_grpc_gcp_transport_security_common_proto_init() }
func file_grpc_gcp_transport_security_common_proto_init() {
	if File_grpc_gcp_transport_security_common_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_grpc_gcp_transport_security_common_proto_rawDesc), len(file_grpc_gcp_transport_security_common_proto_rawDesc)),
			NumEnums:      1,
			NumMessages:   4,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_grpc_gcp_transport_security_common_proto_goTypes,
		DependencyIndexes: file_grpc_gcp_transport_security_common_proto_depIdxs,
		EnumInfos:         file_grpc_gcp_transport_security_common_proto_enumTypes,
		MessageInfos:      file_grpc_gcp_transport_security_common_proto_msgTypes,
	}.Build()
	File_grpc_gcp_transport_security_common_proto = out.File
	file_grpc_gcp_transport_security_common_proto_goTypes = nil
	file_grpc_gcp_transport_security_common_proto_depIdxs = nil
}
