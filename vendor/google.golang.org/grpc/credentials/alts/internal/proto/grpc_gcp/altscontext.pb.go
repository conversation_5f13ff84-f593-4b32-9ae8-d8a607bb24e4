// Copyright 2018 The gRPC Authors
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

// The canonical version of this proto can be found at
// https://github.com/grpc/grpc-proto/blob/master/grpc/gcp/altscontext.proto

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v5.27.1
// source: grpc/gcp/altscontext.proto

package grpc_gcp

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type AltsContext struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// The application protocol negotiated for this connection.
	ApplicationProtocol string `protobuf:"bytes,1,opt,name=application_protocol,json=applicationProtocol,proto3" json:"application_protocol,omitempty"`
	// The record protocol negotiated for this connection.
	RecordProtocol string `protobuf:"bytes,2,opt,name=record_protocol,json=recordProtocol,proto3" json:"record_protocol,omitempty"`
	// The security level of the created secure channel.
	SecurityLevel SecurityLevel `protobuf:"varint,3,opt,name=security_level,json=securityLevel,proto3,enum=grpc.gcp.SecurityLevel" json:"security_level,omitempty"`
	// The peer service account.
	PeerServiceAccount string `protobuf:"bytes,4,opt,name=peer_service_account,json=peerServiceAccount,proto3" json:"peer_service_account,omitempty"`
	// The local service account.
	LocalServiceAccount string `protobuf:"bytes,5,opt,name=local_service_account,json=localServiceAccount,proto3" json:"local_service_account,omitempty"`
	// The RPC protocol versions supported by the peer.
	PeerRpcVersions *RpcProtocolVersions `protobuf:"bytes,6,opt,name=peer_rpc_versions,json=peerRpcVersions,proto3" json:"peer_rpc_versions,omitempty"`
	// Additional attributes of the peer.
	PeerAttributes map[string]string `protobuf:"bytes,7,rep,name=peer_attributes,json=peerAttributes,proto3" json:"peer_attributes,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *AltsContext) Reset() {
	*x = AltsContext{}
	mi := &file_grpc_gcp_altscontext_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AltsContext) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AltsContext) ProtoMessage() {}

func (x *AltsContext) ProtoReflect() protoreflect.Message {
	mi := &file_grpc_gcp_altscontext_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AltsContext.ProtoReflect.Descriptor instead.
func (*AltsContext) Descriptor() ([]byte, []int) {
	return file_grpc_gcp_altscontext_proto_rawDescGZIP(), []int{0}
}

func (x *AltsContext) GetApplicationProtocol() string {
	if x != nil {
		return x.ApplicationProtocol
	}
	return ""
}

func (x *AltsContext) GetRecordProtocol() string {
	if x != nil {
		return x.RecordProtocol
	}
	return ""
}

func (x *AltsContext) GetSecurityLevel() SecurityLevel {
	if x != nil {
		return x.SecurityLevel
	}
	return SecurityLevel_SECURITY_NONE
}

func (x *AltsContext) GetPeerServiceAccount() string {
	if x != nil {
		return x.PeerServiceAccount
	}
	return ""
}

func (x *AltsContext) GetLocalServiceAccount() string {
	if x != nil {
		return x.LocalServiceAccount
	}
	return ""
}

func (x *AltsContext) GetPeerRpcVersions() *RpcProtocolVersions {
	if x != nil {
		return x.PeerRpcVersions
	}
	return nil
}

func (x *AltsContext) GetPeerAttributes() map[string]string {
	if x != nil {
		return x.PeerAttributes
	}
	return nil
}

var File_grpc_gcp_altscontext_proto protoreflect.FileDescriptor

const file_grpc_gcp_altscontext_proto_rawDesc = "" +
	"\n" +
	"\x1agrpc/gcp/altscontext.proto\x12\bgrpc.gcp\x1a(grpc/gcp/transport_security_common.proto\"\xf1\x03\n" +
	"\vAltsContext\x121\n" +
	"\x14application_protocol\x18\x01 \x01(\tR\x13applicationProtocol\x12'\n" +
	"\x0frecord_protocol\x18\x02 \x01(\tR\x0erecordProtocol\x12>\n" +
	"\x0esecurity_level\x18\x03 \x01(\x0e2\x17.grpc.gcp.SecurityLevelR\rsecurityLevel\x120\n" +
	"\x14peer_service_account\x18\x04 \x01(\tR\x12peerServiceAccount\x122\n" +
	"\x15local_service_account\x18\x05 \x01(\tR\x13localServiceAccount\x12I\n" +
	"\x11peer_rpc_versions\x18\x06 \x01(\v2\x1d.grpc.gcp.RpcProtocolVersionsR\x0fpeerRpcVersions\x12R\n" +
	"\x0fpeer_attributes\x18\a \x03(\v2).grpc.gcp.AltsContext.PeerAttributesEntryR\x0epeerAttributes\x1aA\n" +
	"\x13PeerAttributesEntry\x12\x10\n" +
	"\x03key\x18\x01 \x01(\tR\x03key\x12\x14\n" +
	"\x05value\x18\x02 \x01(\tR\x05value:\x028\x01Bl\n" +
	"\x15io.grpc.alts.internalB\x10AltsContextProtoP\x01Z?google.golang.org/grpc/credentials/alts/internal/proto/grpc_gcpb\x06proto3"

var (
	file_grpc_gcp_altscontext_proto_rawDescOnce sync.Once
	file_grpc_gcp_altscontext_proto_rawDescData []byte
)

func file_grpc_gcp_altscontext_proto_rawDescGZIP() []byte {
	file_grpc_gcp_altscontext_proto_rawDescOnce.Do(func() {
		file_grpc_gcp_altscontext_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_grpc_gcp_altscontext_proto_rawDesc), len(file_grpc_gcp_altscontext_proto_rawDesc)))
	})
	return file_grpc_gcp_altscontext_proto_rawDescData
}

var file_grpc_gcp_altscontext_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_grpc_gcp_altscontext_proto_goTypes = []any{
	(*AltsContext)(nil),         // 0: grpc.gcp.AltsContext
	nil,                         // 1: grpc.gcp.AltsContext.PeerAttributesEntry
	(SecurityLevel)(0),          // 2: grpc.gcp.SecurityLevel
	(*RpcProtocolVersions)(nil), // 3: grpc.gcp.RpcProtocolVersions
}
var file_grpc_gcp_altscontext_proto_depIdxs = []int32{
	2, // 0: grpc.gcp.AltsContext.security_level:type_name -> grpc.gcp.SecurityLevel
	3, // 1: grpc.gcp.AltsContext.peer_rpc_versions:type_name -> grpc.gcp.RpcProtocolVersions
	1, // 2: grpc.gcp.AltsContext.peer_attributes:type_name -> grpc.gcp.AltsContext.PeerAttributesEntry
	3, // [3:3] is the sub-list for method output_type
	3, // [3:3] is the sub-list for method input_type
	3, // [3:3] is the sub-list for extension type_name
	3, // [3:3] is the sub-list for extension extendee
	0, // [0:3] is the sub-list for field type_name
}

func init() { file_grpc_gcp_altscontext_proto_init() }
func file_grpc_gcp_altscontext_proto_init() {
	if File_grpc_gcp_altscontext_proto != nil {
		return
	}
	file_grpc_gcp_transport_security_common_proto_init()
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_grpc_gcp_altscontext_proto_rawDesc), len(file_grpc_gcp_altscontext_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_grpc_gcp_altscontext_proto_goTypes,
		DependencyIndexes: file_grpc_gcp_altscontext_proto_depIdxs,
		MessageInfos:      file_grpc_gcp_altscontext_proto_msgTypes,
	}.Build()
	File_grpc_gcp_altscontext_proto = out.File
	file_grpc_gcp_altscontext_proto_goTypes = nil
	file_grpc_gcp_altscontext_proto_depIdxs = nil
}
