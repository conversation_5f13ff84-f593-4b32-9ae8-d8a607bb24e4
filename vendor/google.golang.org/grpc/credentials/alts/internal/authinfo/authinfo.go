/*
 *
 * Copyright 2018 gRPC authors.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 */

// Package authinfo provide authentication information returned by handshakers.
package authinfo

import (
	"google.golang.org/grpc/credentials"
	altspb "google.golang.org/grpc/credentials/alts/internal/proto/grpc_gcp"
)

var _ credentials.AuthInfo = (*altsAuthInfo)(nil)

// altsAuthInfo exposes security information from the ALTS handshake to the
// application. altsAuthInfo is immutable and implements credentials.AuthInfo.
type altsAuthInfo struct {
	p *altspb.AltsContext
	credentials.CommonAuthInfo
}

// New returns a new altsAuthInfo object given handshaker results.
func New(result *altspb.HandshakerResult) credentials.AuthInfo {
	return newAuthInfo(result)
}

func newAuthInfo(result *altspb.HandshakerResult) *altsAuthInfo {
	return &altsAuthInfo{
		p: &altspb.AltsContext{
			ApplicationProtocol: result.GetApplicationProtocol(),
			RecordProtocol:      result.GetRecordProtocol(),
			// TODO: assign security level from result.
			SecurityLevel:       altspb.SecurityLevel_INTEGRITY_AND_PRIVACY,
			PeerServiceAccount:  result.GetPeerIdentity().GetServiceAccount(),
			LocalServiceAccount: result.GetLocalIdentity().GetServiceAccount(),
			PeerRpcVersions:     result.GetPeerRpcVersions(),
			PeerAttributes:      result.GetPeerIdentity().GetAttributes(),
		},
		CommonAuthInfo: credentials.CommonAuthInfo{SecurityLevel: credentials.PrivacyAndIntegrity},
	}
}

// AuthType identifies the context as providing ALTS authentication information.
func (s *altsAuthInfo) AuthType() string {
	return "alts"
}

// ApplicationProtocol returns the context's application protocol.
func (s *altsAuthInfo) ApplicationProtocol() string {
	return s.p.GetApplicationProtocol()
}

// RecordProtocol returns the context's record protocol.
func (s *altsAuthInfo) RecordProtocol() string {
	return s.p.GetRecordProtocol()
}

// SecurityLevel returns the context's security level.
func (s *altsAuthInfo) SecurityLevel() altspb.SecurityLevel {
	return s.p.GetSecurityLevel()
}

// PeerServiceAccount returns the context's peer service account.
func (s *altsAuthInfo) PeerServiceAccount() string {
	return s.p.GetPeerServiceAccount()
}

// LocalServiceAccount returns the context's local service account.
func (s *altsAuthInfo) LocalServiceAccount() string {
	return s.p.GetLocalServiceAccount()
}

// PeerRPCVersions returns the context's peer RPC versions.
func (s *altsAuthInfo) PeerRPCVersions() *altspb.RpcProtocolVersions {
	return s.p.GetPeerRpcVersions()
}

// PeerAttributes returns the context's peer attributes.
func (s *altsAuthInfo) PeerAttributes() map[string]string {
	return s.p.GetPeerAttributes()
}
