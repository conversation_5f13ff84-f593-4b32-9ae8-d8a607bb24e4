// Copyright 2015 The gRPC Authors
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

// This file defines the GRPCLB LoadBalancing protocol.
//
// The canonical version of this proto can be found at
// https://github.com/grpc/grpc-proto/blob/master/grpc/lb/v1/load_balancer.proto

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v5.27.1
// source: grpc/lb/v1/load_balancer.proto

package grpc_lb_v1

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	durationpb "google.golang.org/protobuf/types/known/durationpb"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type LoadBalanceRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Types that are valid to be assigned to LoadBalanceRequestType:
	//
	//	*LoadBalanceRequest_InitialRequest
	//	*LoadBalanceRequest_ClientStats
	LoadBalanceRequestType isLoadBalanceRequest_LoadBalanceRequestType `protobuf_oneof:"load_balance_request_type"`
	unknownFields          protoimpl.UnknownFields
	sizeCache              protoimpl.SizeCache
}

func (x *LoadBalanceRequest) Reset() {
	*x = LoadBalanceRequest{}
	mi := &file_grpc_lb_v1_load_balancer_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *LoadBalanceRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LoadBalanceRequest) ProtoMessage() {}

func (x *LoadBalanceRequest) ProtoReflect() protoreflect.Message {
	mi := &file_grpc_lb_v1_load_balancer_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LoadBalanceRequest.ProtoReflect.Descriptor instead.
func (*LoadBalanceRequest) Descriptor() ([]byte, []int) {
	return file_grpc_lb_v1_load_balancer_proto_rawDescGZIP(), []int{0}
}

func (x *LoadBalanceRequest) GetLoadBalanceRequestType() isLoadBalanceRequest_LoadBalanceRequestType {
	if x != nil {
		return x.LoadBalanceRequestType
	}
	return nil
}

func (x *LoadBalanceRequest) GetInitialRequest() *InitialLoadBalanceRequest {
	if x != nil {
		if x, ok := x.LoadBalanceRequestType.(*LoadBalanceRequest_InitialRequest); ok {
			return x.InitialRequest
		}
	}
	return nil
}

func (x *LoadBalanceRequest) GetClientStats() *ClientStats {
	if x != nil {
		if x, ok := x.LoadBalanceRequestType.(*LoadBalanceRequest_ClientStats); ok {
			return x.ClientStats
		}
	}
	return nil
}

type isLoadBalanceRequest_LoadBalanceRequestType interface {
	isLoadBalanceRequest_LoadBalanceRequestType()
}

type LoadBalanceRequest_InitialRequest struct {
	// This message should be sent on the first request to the load balancer.
	InitialRequest *InitialLoadBalanceRequest `protobuf:"bytes,1,opt,name=initial_request,json=initialRequest,proto3,oneof"`
}

type LoadBalanceRequest_ClientStats struct {
	// The client stats should be periodically reported to the load balancer
	// based on the duration defined in the InitialLoadBalanceResponse.
	ClientStats *ClientStats `protobuf:"bytes,2,opt,name=client_stats,json=clientStats,proto3,oneof"`
}

func (*LoadBalanceRequest_InitialRequest) isLoadBalanceRequest_LoadBalanceRequestType() {}

func (*LoadBalanceRequest_ClientStats) isLoadBalanceRequest_LoadBalanceRequestType() {}

type InitialLoadBalanceRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// The name of the load balanced service (e.g., service.googleapis.com). Its
	// length should be less than 256 bytes.
	// The name might include a port number. How to handle the port number is up
	// to the balancer.
	Name          string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *InitialLoadBalanceRequest) Reset() {
	*x = InitialLoadBalanceRequest{}
	mi := &file_grpc_lb_v1_load_balancer_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *InitialLoadBalanceRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InitialLoadBalanceRequest) ProtoMessage() {}

func (x *InitialLoadBalanceRequest) ProtoReflect() protoreflect.Message {
	mi := &file_grpc_lb_v1_load_balancer_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InitialLoadBalanceRequest.ProtoReflect.Descriptor instead.
func (*InitialLoadBalanceRequest) Descriptor() ([]byte, []int) {
	return file_grpc_lb_v1_load_balancer_proto_rawDescGZIP(), []int{1}
}

func (x *InitialLoadBalanceRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

// Contains the number of calls finished for a particular load balance token.
type ClientStatsPerToken struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// See Server.load_balance_token.
	LoadBalanceToken string `protobuf:"bytes,1,opt,name=load_balance_token,json=loadBalanceToken,proto3" json:"load_balance_token,omitempty"`
	// The total number of RPCs that finished associated with the token.
	NumCalls      int64 `protobuf:"varint,2,opt,name=num_calls,json=numCalls,proto3" json:"num_calls,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ClientStatsPerToken) Reset() {
	*x = ClientStatsPerToken{}
	mi := &file_grpc_lb_v1_load_balancer_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ClientStatsPerToken) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ClientStatsPerToken) ProtoMessage() {}

func (x *ClientStatsPerToken) ProtoReflect() protoreflect.Message {
	mi := &file_grpc_lb_v1_load_balancer_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ClientStatsPerToken.ProtoReflect.Descriptor instead.
func (*ClientStatsPerToken) Descriptor() ([]byte, []int) {
	return file_grpc_lb_v1_load_balancer_proto_rawDescGZIP(), []int{2}
}

func (x *ClientStatsPerToken) GetLoadBalanceToken() string {
	if x != nil {
		return x.LoadBalanceToken
	}
	return ""
}

func (x *ClientStatsPerToken) GetNumCalls() int64 {
	if x != nil {
		return x.NumCalls
	}
	return 0
}

// Contains client level statistics that are useful to load balancing. Each
// count except the timestamp should be reset to zero after reporting the stats.
type ClientStats struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// The timestamp of generating the report.
	Timestamp *timestamppb.Timestamp `protobuf:"bytes,1,opt,name=timestamp,proto3" json:"timestamp,omitempty"`
	// The total number of RPCs that started.
	NumCallsStarted int64 `protobuf:"varint,2,opt,name=num_calls_started,json=numCallsStarted,proto3" json:"num_calls_started,omitempty"`
	// The total number of RPCs that finished.
	NumCallsFinished int64 `protobuf:"varint,3,opt,name=num_calls_finished,json=numCallsFinished,proto3" json:"num_calls_finished,omitempty"`
	// The total number of RPCs that failed to reach a server except dropped RPCs.
	NumCallsFinishedWithClientFailedToSend int64 `protobuf:"varint,6,opt,name=num_calls_finished_with_client_failed_to_send,json=numCallsFinishedWithClientFailedToSend,proto3" json:"num_calls_finished_with_client_failed_to_send,omitempty"`
	// The total number of RPCs that finished and are known to have been received
	// by a server.
	NumCallsFinishedKnownReceived int64 `protobuf:"varint,7,opt,name=num_calls_finished_known_received,json=numCallsFinishedKnownReceived,proto3" json:"num_calls_finished_known_received,omitempty"`
	// The list of dropped calls.
	CallsFinishedWithDrop []*ClientStatsPerToken `protobuf:"bytes,8,rep,name=calls_finished_with_drop,json=callsFinishedWithDrop,proto3" json:"calls_finished_with_drop,omitempty"`
	unknownFields         protoimpl.UnknownFields
	sizeCache             protoimpl.SizeCache
}

func (x *ClientStats) Reset() {
	*x = ClientStats{}
	mi := &file_grpc_lb_v1_load_balancer_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ClientStats) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ClientStats) ProtoMessage() {}

func (x *ClientStats) ProtoReflect() protoreflect.Message {
	mi := &file_grpc_lb_v1_load_balancer_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ClientStats.ProtoReflect.Descriptor instead.
func (*ClientStats) Descriptor() ([]byte, []int) {
	return file_grpc_lb_v1_load_balancer_proto_rawDescGZIP(), []int{3}
}

func (x *ClientStats) GetTimestamp() *timestamppb.Timestamp {
	if x != nil {
		return x.Timestamp
	}
	return nil
}

func (x *ClientStats) GetNumCallsStarted() int64 {
	if x != nil {
		return x.NumCallsStarted
	}
	return 0
}

func (x *ClientStats) GetNumCallsFinished() int64 {
	if x != nil {
		return x.NumCallsFinished
	}
	return 0
}

func (x *ClientStats) GetNumCallsFinishedWithClientFailedToSend() int64 {
	if x != nil {
		return x.NumCallsFinishedWithClientFailedToSend
	}
	return 0
}

func (x *ClientStats) GetNumCallsFinishedKnownReceived() int64 {
	if x != nil {
		return x.NumCallsFinishedKnownReceived
	}
	return 0
}

func (x *ClientStats) GetCallsFinishedWithDrop() []*ClientStatsPerToken {
	if x != nil {
		return x.CallsFinishedWithDrop
	}
	return nil
}

type LoadBalanceResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Types that are valid to be assigned to LoadBalanceResponseType:
	//
	//	*LoadBalanceResponse_InitialResponse
	//	*LoadBalanceResponse_ServerList
	//	*LoadBalanceResponse_FallbackResponse
	LoadBalanceResponseType isLoadBalanceResponse_LoadBalanceResponseType `protobuf_oneof:"load_balance_response_type"`
	unknownFields           protoimpl.UnknownFields
	sizeCache               protoimpl.SizeCache
}

func (x *LoadBalanceResponse) Reset() {
	*x = LoadBalanceResponse{}
	mi := &file_grpc_lb_v1_load_balancer_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *LoadBalanceResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LoadBalanceResponse) ProtoMessage() {}

func (x *LoadBalanceResponse) ProtoReflect() protoreflect.Message {
	mi := &file_grpc_lb_v1_load_balancer_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LoadBalanceResponse.ProtoReflect.Descriptor instead.
func (*LoadBalanceResponse) Descriptor() ([]byte, []int) {
	return file_grpc_lb_v1_load_balancer_proto_rawDescGZIP(), []int{4}
}

func (x *LoadBalanceResponse) GetLoadBalanceResponseType() isLoadBalanceResponse_LoadBalanceResponseType {
	if x != nil {
		return x.LoadBalanceResponseType
	}
	return nil
}

func (x *LoadBalanceResponse) GetInitialResponse() *InitialLoadBalanceResponse {
	if x != nil {
		if x, ok := x.LoadBalanceResponseType.(*LoadBalanceResponse_InitialResponse); ok {
			return x.InitialResponse
		}
	}
	return nil
}

func (x *LoadBalanceResponse) GetServerList() *ServerList {
	if x != nil {
		if x, ok := x.LoadBalanceResponseType.(*LoadBalanceResponse_ServerList); ok {
			return x.ServerList
		}
	}
	return nil
}

func (x *LoadBalanceResponse) GetFallbackResponse() *FallbackResponse {
	if x != nil {
		if x, ok := x.LoadBalanceResponseType.(*LoadBalanceResponse_FallbackResponse); ok {
			return x.FallbackResponse
		}
	}
	return nil
}

type isLoadBalanceResponse_LoadBalanceResponseType interface {
	isLoadBalanceResponse_LoadBalanceResponseType()
}

type LoadBalanceResponse_InitialResponse struct {
	// This message should be sent on the first response to the client.
	InitialResponse *InitialLoadBalanceResponse `protobuf:"bytes,1,opt,name=initial_response,json=initialResponse,proto3,oneof"`
}

type LoadBalanceResponse_ServerList struct {
	// Contains the list of servers selected by the load balancer. The client
	// should send requests to these servers in the specified order.
	ServerList *ServerList `protobuf:"bytes,2,opt,name=server_list,json=serverList,proto3,oneof"`
}

type LoadBalanceResponse_FallbackResponse struct {
	// If this field is set, then the client should eagerly enter fallback
	// mode (even if there are existing, healthy connections to backends).
	FallbackResponse *FallbackResponse `protobuf:"bytes,3,opt,name=fallback_response,json=fallbackResponse,proto3,oneof"`
}

func (*LoadBalanceResponse_InitialResponse) isLoadBalanceResponse_LoadBalanceResponseType() {}

func (*LoadBalanceResponse_ServerList) isLoadBalanceResponse_LoadBalanceResponseType() {}

func (*LoadBalanceResponse_FallbackResponse) isLoadBalanceResponse_LoadBalanceResponseType() {}

type FallbackResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *FallbackResponse) Reset() {
	*x = FallbackResponse{}
	mi := &file_grpc_lb_v1_load_balancer_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *FallbackResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FallbackResponse) ProtoMessage() {}

func (x *FallbackResponse) ProtoReflect() protoreflect.Message {
	mi := &file_grpc_lb_v1_load_balancer_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FallbackResponse.ProtoReflect.Descriptor instead.
func (*FallbackResponse) Descriptor() ([]byte, []int) {
	return file_grpc_lb_v1_load_balancer_proto_rawDescGZIP(), []int{5}
}

type InitialLoadBalanceResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// This interval defines how often the client should send the client stats
	// to the load balancer. Stats should only be reported when the duration is
	// positive.
	ClientStatsReportInterval *durationpb.Duration `protobuf:"bytes,2,opt,name=client_stats_report_interval,json=clientStatsReportInterval,proto3" json:"client_stats_report_interval,omitempty"`
	unknownFields             protoimpl.UnknownFields
	sizeCache                 protoimpl.SizeCache
}

func (x *InitialLoadBalanceResponse) Reset() {
	*x = InitialLoadBalanceResponse{}
	mi := &file_grpc_lb_v1_load_balancer_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *InitialLoadBalanceResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InitialLoadBalanceResponse) ProtoMessage() {}

func (x *InitialLoadBalanceResponse) ProtoReflect() protoreflect.Message {
	mi := &file_grpc_lb_v1_load_balancer_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InitialLoadBalanceResponse.ProtoReflect.Descriptor instead.
func (*InitialLoadBalanceResponse) Descriptor() ([]byte, []int) {
	return file_grpc_lb_v1_load_balancer_proto_rawDescGZIP(), []int{6}
}

func (x *InitialLoadBalanceResponse) GetClientStatsReportInterval() *durationpb.Duration {
	if x != nil {
		return x.ClientStatsReportInterval
	}
	return nil
}

type ServerList struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Contains a list of servers selected by the load balancer. The list will
	// be updated when server resolutions change or as needed to balance load
	// across more servers. The client should consume the server list in order
	// unless instructed otherwise via the client_config.
	Servers       []*Server `protobuf:"bytes,1,rep,name=servers,proto3" json:"servers,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ServerList) Reset() {
	*x = ServerList{}
	mi := &file_grpc_lb_v1_load_balancer_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ServerList) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ServerList) ProtoMessage() {}

func (x *ServerList) ProtoReflect() protoreflect.Message {
	mi := &file_grpc_lb_v1_load_balancer_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ServerList.ProtoReflect.Descriptor instead.
func (*ServerList) Descriptor() ([]byte, []int) {
	return file_grpc_lb_v1_load_balancer_proto_rawDescGZIP(), []int{7}
}

func (x *ServerList) GetServers() []*Server {
	if x != nil {
		return x.Servers
	}
	return nil
}

// Contains server information. When the drop field is not true, use the other
// fields.
type Server struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// A resolved address for the server, serialized in network-byte-order. It may
	// either be an IPv4 or IPv6 address.
	IpAddress []byte `protobuf:"bytes,1,opt,name=ip_address,json=ipAddress,proto3" json:"ip_address,omitempty"`
	// A resolved port number for the server.
	Port int32 `protobuf:"varint,2,opt,name=port,proto3" json:"port,omitempty"`
	// An opaque but printable token for load reporting. The client must include
	// the token of the picked server into the initial metadata when it starts a
	// call to that server. The token is used by the server to verify the request
	// and to allow the server to report load to the gRPC LB system. The token is
	// also used in client stats for reporting dropped calls.
	//
	// Its length can be variable but must be less than 50 bytes.
	LoadBalanceToken string `protobuf:"bytes,3,opt,name=load_balance_token,json=loadBalanceToken,proto3" json:"load_balance_token,omitempty"`
	// Indicates whether this particular request should be dropped by the client.
	// If the request is dropped, there will be a corresponding entry in
	// ClientStats.calls_finished_with_drop.
	Drop          bool `protobuf:"varint,4,opt,name=drop,proto3" json:"drop,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Server) Reset() {
	*x = Server{}
	mi := &file_grpc_lb_v1_load_balancer_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Server) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Server) ProtoMessage() {}

func (x *Server) ProtoReflect() protoreflect.Message {
	mi := &file_grpc_lb_v1_load_balancer_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Server.ProtoReflect.Descriptor instead.
func (*Server) Descriptor() ([]byte, []int) {
	return file_grpc_lb_v1_load_balancer_proto_rawDescGZIP(), []int{8}
}

func (x *Server) GetIpAddress() []byte {
	if x != nil {
		return x.IpAddress
	}
	return nil
}

func (x *Server) GetPort() int32 {
	if x != nil {
		return x.Port
	}
	return 0
}

func (x *Server) GetLoadBalanceToken() string {
	if x != nil {
		return x.LoadBalanceToken
	}
	return ""
}

func (x *Server) GetDrop() bool {
	if x != nil {
		return x.Drop
	}
	return false
}

var File_grpc_lb_v1_load_balancer_proto protoreflect.FileDescriptor

const file_grpc_lb_v1_load_balancer_proto_rawDesc = "" +
	"\n" +
	"\x1egrpc/lb/v1/load_balancer.proto\x12\n" +
	"grpc.lb.v1\x1a\x1egoogle/protobuf/duration.proto\x1a\x1fgoogle/protobuf/timestamp.proto\"\xc1\x01\n" +
	"\x12LoadBalanceRequest\x12P\n" +
	"\x0finitial_request\x18\x01 \x01(\v2%.grpc.lb.v1.InitialLoadBalanceRequestH\x00R\x0einitialRequest\x12<\n" +
	"\fclient_stats\x18\x02 \x01(\v2\x17.grpc.lb.v1.ClientStatsH\x00R\vclientStatsB\x1b\n" +
	"\x19load_balance_request_type\"/\n" +
	"\x19InitialLoadBalanceRequest\x12\x12\n" +
	"\x04name\x18\x01 \x01(\tR\x04name\"`\n" +
	"\x13ClientStatsPerToken\x12,\n" +
	"\x12load_balance_token\x18\x01 \x01(\tR\x10loadBalanceToken\x12\x1b\n" +
	"\tnum_calls\x18\x02 \x01(\x03R\bnumCalls\"\xb0\x03\n" +
	"\vClientStats\x128\n" +
	"\ttimestamp\x18\x01 \x01(\v2\x1a.google.protobuf.TimestampR\ttimestamp\x12*\n" +
	"\x11num_calls_started\x18\x02 \x01(\x03R\x0fnumCallsStarted\x12,\n" +
	"\x12num_calls_finished\x18\x03 \x01(\x03R\x10numCallsFinished\x12]\n" +
	"-num_calls_finished_with_client_failed_to_send\x18\x06 \x01(\x03R&numCallsFinishedWithClientFailedToSend\x12H\n" +
	"!num_calls_finished_known_received\x18\a \x01(\x03R\x1dnumCallsFinishedKnownReceived\x12X\n" +
	"\x18calls_finished_with_drop\x18\b \x03(\v2\x1f.grpc.lb.v1.ClientStatsPerTokenR\x15callsFinishedWithDropJ\x04\b\x04\x10\x05J\x04\b\x05\x10\x06\"\x90\x02\n" +
	"\x13LoadBalanceResponse\x12S\n" +
	"\x10initial_response\x18\x01 \x01(\v2&.grpc.lb.v1.InitialLoadBalanceResponseH\x00R\x0finitialResponse\x129\n" +
	"\vserver_list\x18\x02 \x01(\v2\x16.grpc.lb.v1.ServerListH\x00R\n" +
	"serverList\x12K\n" +
	"\x11fallback_response\x18\x03 \x01(\v2\x1c.grpc.lb.v1.FallbackResponseH\x00R\x10fallbackResponseB\x1c\n" +
	"\x1aload_balance_response_type\"\x12\n" +
	"\x10FallbackResponse\"~\n" +
	"\x1aInitialLoadBalanceResponse\x12Z\n" +
	"\x1cclient_stats_report_interval\x18\x02 \x01(\v2\x19.google.protobuf.DurationR\x19clientStatsReportIntervalJ\x04\b\x01\x10\x02\"@\n" +
	"\n" +
	"ServerList\x12,\n" +
	"\aservers\x18\x01 \x03(\v2\x12.grpc.lb.v1.ServerR\aserversJ\x04\b\x03\x10\x04\"\x83\x01\n" +
	"\x06Server\x12\x1d\n" +
	"\n" +
	"ip_address\x18\x01 \x01(\fR\tipAddress\x12\x12\n" +
	"\x04port\x18\x02 \x01(\x05R\x04port\x12,\n" +
	"\x12load_balance_token\x18\x03 \x01(\tR\x10loadBalanceToken\x12\x12\n" +
	"\x04drop\x18\x04 \x01(\bR\x04dropJ\x04\b\x05\x10\x062b\n" +
	"\fLoadBalancer\x12R\n" +
	"\vBalanceLoad\x12\x1e.grpc.lb.v1.LoadBalanceRequest\x1a\x1f.grpc.lb.v1.LoadBalanceResponse(\x010\x01BW\n" +
	"\rio.grpc.lb.v1B\x11LoadBalancerProtoP\x01Z1google.golang.org/grpc/balancer/grpclb/grpc_lb_v1b\x06proto3"

var (
	file_grpc_lb_v1_load_balancer_proto_rawDescOnce sync.Once
	file_grpc_lb_v1_load_balancer_proto_rawDescData []byte
)

func file_grpc_lb_v1_load_balancer_proto_rawDescGZIP() []byte {
	file_grpc_lb_v1_load_balancer_proto_rawDescOnce.Do(func() {
		file_grpc_lb_v1_load_balancer_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_grpc_lb_v1_load_balancer_proto_rawDesc), len(file_grpc_lb_v1_load_balancer_proto_rawDesc)))
	})
	return file_grpc_lb_v1_load_balancer_proto_rawDescData
}

var file_grpc_lb_v1_load_balancer_proto_msgTypes = make([]protoimpl.MessageInfo, 9)
var file_grpc_lb_v1_load_balancer_proto_goTypes = []any{
	(*LoadBalanceRequest)(nil),         // 0: grpc.lb.v1.LoadBalanceRequest
	(*InitialLoadBalanceRequest)(nil),  // 1: grpc.lb.v1.InitialLoadBalanceRequest
	(*ClientStatsPerToken)(nil),        // 2: grpc.lb.v1.ClientStatsPerToken
	(*ClientStats)(nil),                // 3: grpc.lb.v1.ClientStats
	(*LoadBalanceResponse)(nil),        // 4: grpc.lb.v1.LoadBalanceResponse
	(*FallbackResponse)(nil),           // 5: grpc.lb.v1.FallbackResponse
	(*InitialLoadBalanceResponse)(nil), // 6: grpc.lb.v1.InitialLoadBalanceResponse
	(*ServerList)(nil),                 // 7: grpc.lb.v1.ServerList
	(*Server)(nil),                     // 8: grpc.lb.v1.Server
	(*timestamppb.Timestamp)(nil),      // 9: google.protobuf.Timestamp
	(*durationpb.Duration)(nil),        // 10: google.protobuf.Duration
}
var file_grpc_lb_v1_load_balancer_proto_depIdxs = []int32{
	1,  // 0: grpc.lb.v1.LoadBalanceRequest.initial_request:type_name -> grpc.lb.v1.InitialLoadBalanceRequest
	3,  // 1: grpc.lb.v1.LoadBalanceRequest.client_stats:type_name -> grpc.lb.v1.ClientStats
	9,  // 2: grpc.lb.v1.ClientStats.timestamp:type_name -> google.protobuf.Timestamp
	2,  // 3: grpc.lb.v1.ClientStats.calls_finished_with_drop:type_name -> grpc.lb.v1.ClientStatsPerToken
	6,  // 4: grpc.lb.v1.LoadBalanceResponse.initial_response:type_name -> grpc.lb.v1.InitialLoadBalanceResponse
	7,  // 5: grpc.lb.v1.LoadBalanceResponse.server_list:type_name -> grpc.lb.v1.ServerList
	5,  // 6: grpc.lb.v1.LoadBalanceResponse.fallback_response:type_name -> grpc.lb.v1.FallbackResponse
	10, // 7: grpc.lb.v1.InitialLoadBalanceResponse.client_stats_report_interval:type_name -> google.protobuf.Duration
	8,  // 8: grpc.lb.v1.ServerList.servers:type_name -> grpc.lb.v1.Server
	0,  // 9: grpc.lb.v1.LoadBalancer.BalanceLoad:input_type -> grpc.lb.v1.LoadBalanceRequest
	4,  // 10: grpc.lb.v1.LoadBalancer.BalanceLoad:output_type -> grpc.lb.v1.LoadBalanceResponse
	10, // [10:11] is the sub-list for method output_type
	9,  // [9:10] is the sub-list for method input_type
	9,  // [9:9] is the sub-list for extension type_name
	9,  // [9:9] is the sub-list for extension extendee
	0,  // [0:9] is the sub-list for field type_name
}

func init() { file_grpc_lb_v1_load_balancer_proto_init() }
func file_grpc_lb_v1_load_balancer_proto_init() {
	if File_grpc_lb_v1_load_balancer_proto != nil {
		return
	}
	file_grpc_lb_v1_load_balancer_proto_msgTypes[0].OneofWrappers = []any{
		(*LoadBalanceRequest_InitialRequest)(nil),
		(*LoadBalanceRequest_ClientStats)(nil),
	}
	file_grpc_lb_v1_load_balancer_proto_msgTypes[4].OneofWrappers = []any{
		(*LoadBalanceResponse_InitialResponse)(nil),
		(*LoadBalanceResponse_ServerList)(nil),
		(*LoadBalanceResponse_FallbackResponse)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_grpc_lb_v1_load_balancer_proto_rawDesc), len(file_grpc_lb_v1_load_balancer_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   9,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_grpc_lb_v1_load_balancer_proto_goTypes,
		DependencyIndexes: file_grpc_lb_v1_load_balancer_proto_depIdxs,
		MessageInfos:      file_grpc_lb_v1_load_balancer_proto_msgTypes,
	}.Build()
	File_grpc_lb_v1_load_balancer_proto = out.File
	file_grpc_lb_v1_load_balancer_proto_goTypes = nil
	file_grpc_lb_v1_load_balancer_proto_depIdxs = nil
}
