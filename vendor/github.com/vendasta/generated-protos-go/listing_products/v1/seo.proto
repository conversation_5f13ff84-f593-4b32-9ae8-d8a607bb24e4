syntax = "proto3";

package listing_products.v1;

option go_package = "github.com/vendasta/generated-protos-go/listing_products/v1;listing_products_v1";
option java_outer_classname = "SeoProto";
option java_package = "com.vendasta.listingproducts.v1.generated";

import "google/protobuf/timestamp.proto";
import "listing_products/v1/listing_profile.proto";
import "vendasta_types/field_mask.proto";

enum Vicinity {
  VICINITY_UNDEFINED = 0;
  VICINITY_CITY = 1;

  // These are the 25 dots around the business location, forming a 5x5 grid like so, with C3 being the business's location:
  // A1 A2 A3 A4 A5
  // B1 B2 B3 B4 B5
  // C1 C2 C3 C4 C5
  // D1 D2 D3 D4 D5
  // E1 E2 E3 E4 E5

  VICINITY_A1 = 2;
  VICINITY_A2 = 3;
  VICINITY_A3 = 4;
  VICINITY_A4 = 5;
  VICINITY_A5 = 6;

  VICINITY_B1 = 7;
  VICINITY_B2 = 8;
  VICINITY_B3 = 9;
  VICINITY_B4 = 10;
  VICINITY_B5 = 11;

  VICINITY_C1 = 12;
  VICINITY_C2 = 13;
  VICINITY_C3 = 14;
  VICINITY_C4 = 15;
  VICINITY_C5 = 16;

  VICINITY_D1 = 17;
  VICINITY_D2 = 18;
  VICINITY_D3 = 19;
  VICINITY_D4 = 20;
  VICINITY_D5 = 21;

  VICINITY_E1 = 22;
  VICINITY_E2 = 23;
  VICINITY_E3 = 24;
  VICINITY_E4 = 25;
  VICINITY_E5 = 26;
}

enum GBPClaimStatus {
  GBP_CLAIM_STATUS_INVALID = 0;
  GBP_CLAIM_STATUS_UNKNOWN = 1;
  GBP_CLAIM_STATUS_CLAIMED = 2;
  GBP_CLAIM_STATUS_UNCLAIMED = 3;
}

message SEODataSummary {
  string keyword = 1;
  google.protobuf.Timestamp date = 2;
  double local_rank = 3;
  double organic_rank = 4;
  int64 difficulty = 5;
  int64 search_volume = 6;
  double search_radius = 7;
  string workflow_url = 8;
}

// Deprecated: use SEODataSummary & LocalSearchData
message SEOData {
  // Deprecated: use SEODataSummary
    string keyword = 1;
  // Deprecated: use SEODataSummary
    google.protobuf.Timestamp date = 2;
  // Deprecated: use SEODataSummary
    double local_rank = 3;
  // Deprecated: use SEODataSummary
    double organic_rank = 4;
  // Deprecated: use SEODataSummary
    int64 difficulty = 5;
  // Deprecated: use SEODataSummary
    int64 search_volume = 6;
  // Deprecated: use LocalSearchData
    repeated LocalSearchData local_searches = 7;
  // Deprecated: use SEODataSummary
    double search_radius = 8;
  // Deprecated: use SEODataSummary
    string workflow_url = 9;
}

message LocalSearchData {
    string keyword = 1;
    Vicinity vicinity = 2;
    Geo search_location = 3;
    repeated LocalSearchResult results = 4;
}

message LocalSearchResult {
    string business_name = 1;
    string address = 2;
    string url = 3;
    string rank = 4;
    bool is_main_business = 5;
    LocalSearchReviews reviews = 6;
    string phone_number = 7;
    GBPClaimStatus claim_status = 8;
}

message LocalSearchReviews {
    double rating = 1;
    string count = 2;
}

message GetSEODataRequest {
  string business_id = 1;
  repeated string keywords = 2;
  google.protobuf.Timestamp start_date = 3;
  google.protobuf.Timestamp end_date = 4;
}

message GetSEODataSummaryRequest {
  string business_id = 1;
  repeated string keywords = 2;
  google.protobuf.Timestamp start_date = 3;
  google.protobuf.Timestamp end_date = 4;
}

message GetAllAIOAuditRequest {
  string business_id = 1;
}
//AIO Audit APIs
message GetAllAIOAuditResponse {
  repeated AIOAuditResults audit = 1;
}

message AIOAuditResults {
  string business_id = 1;
  string brand_name = 2;
  string website_url = 3;
  string audit_date = 4;
  google.protobuf.Timestamp start_date = 5;
  int64 total_pages = 6;
  string audit_status = 7;
  string audit_summary = 8;
  repeated AuditPageData audit_pages = 9;
  repeated AuditScoreResults audit_score_results = 10;
  string keyword = 11;
  string audit_id = 12;
  string audit_url = 13;
}

message AuditPageData {
  string page_url = 1;
  string page_data = 2;
}

message AuditScores {
  string audit_score_scope_name = 1;
  int64 audit_score_scope_value = 2;
  repeated string audit_score_scope_summary = 3;
  repeated string audit_score_recommendations = 4;
}

message AuditScoreResults {
  string audit_page_url = 1;
  repeated AuditScores audit_scores = 2;
}

message GetAIOAuditRequest {
  string business_id = 1;
  string brand_name = 2;
  string website_url = 3;
}

message GetAIOAuditResponse {
  AIOAuditResults audit = 1;
}

message GetAllAIOAuditScoreResultsRequest {
  string business_id = 1;
  string brand_name = 2;
  string website_url = 3;
}

message GetAllAIOAuditScoreResultsResponse {
  repeated AuditScoreResults audit_score_results = 1;
}

message GetAIOAuditStatusRequest {
  string business_id = 1;
  string brand_name = 2;
  string website_url = 3;
}

message GetAIOAuditStatusResponse {
  string audit_status = 1;
}

message GetSEODataSummaryResponse {
  // Data is the most recent entry for the business for each keyword.
  repeated SEODataSummary data = 1;
  // Previous data is the oldest data entry for the business for each keyword within the date range.
  repeated SEODataSummary previous_data = 2;
}

message GetLocalSearchSEODataRequest {
  string business_id = 1;
  string keyword = 2;
  google.protobuf.Timestamp start_date = 3;
  google.protobuf.Timestamp end_date = 4;
}

message GetLocalSearchSEODataResponse {
  string keyword = 1;
  repeated LocalSearchData local_search_data = 2;
}

// AddonActivation is a representation of a addon activation in the Vendasta platform
message AddonActivation {
  enum AddonActivationStatus {
    ACTIVATION_STATUS_NOT_SPECIFIED = 0;
    ACTIVATION_STATUS_ACTIVATED     = 1;
    ACTIVATION_STATUS_PENDING       = 2;
    ACTIVATION_STATUS_CANCELED      = 3;
    ACTIVATION_STATUS_DEACTIVATED   = 4;
  }
  // A prerequisite ID representing the customer/business.
  string business_id = 1;
  // A prerequisite marketplace vendor's ID's of the app the addons belong to.
  string app_id = 2;
  // A prerequisite marketplace vendor's ID's of the addon activated.
  string addon_id = 3;
  // UTC time the addon was activated.
  google.protobuf.Timestamp activated = 5;
  // UTC time the addon was or will be deactivated, if ever.
  google.protobuf.Timestamp deactivated = 6;
  // The state of activation to determine if the addon is active or cancelled
  AddonActivationStatus status = 7;
  // A flag determining whether or not the activation is in trial
  bool is_trial = 8;
  // Determines the number of activations for a given add-on
  int32 count = 9;
}

//
message GetSEODataResponse {
  // Data is the most recent entry for the business for each keyword.
  repeated SEOData data = 1;
  // Previous data is the oldest data entry for the business for each keyword within the date range.
  repeated SEOData previous_data = 2;
}

message BusinessKeywords {
  string business_id = 1;
  // Optional. If not included, the business's stored keywords will be used.
  repeated string keywords = 3;
}

message StartLocalSEODataWorkflowRequest {
  repeated BusinessKeywords businesses = 1;
  // force_serp_workflow flag will skip date checks and run the SERP workflow
  bool force_serp_workflow = 2;
  // Optional: If included, the workflow will save results for the provided date instead of the current date
  google.protobuf.Timestamp date = 3;
  // force_keyword_info_workflow flag will skip date checks and run the Keyword Info workflow
  bool force_keyword_info_workflow = 4;
  // force_serp_workflow flag will skip date checks and run the Suggested Keywords workflow
  bool force_suggested_keywords_workflow = 5;
  // ignore_data_lake_results will re-fetch data from DataForSEO even if we have it stored in our data lake already
  bool ignore_data_lake_results = 6;
}

message GetSEOSettingsRequest {
  string business_id = 1;
}

message SaveSEOSettingsRequest {
  string business_id = 1;
  double local_search_radius = 2;
  repeated string favorite_keywords = 3;
  vendastatypes.FieldMask field_mask = 4;
  bool is_full_search_enabled =5;
}

message SEOSettingsResponse {
  string business_id = 1;
  double local_search_radius = 2;
  repeated string favorite_keywords = 3;
  bool is_full_search_enabled =4;
}

message GetActiveSEOAddonsRequest {
  // A prerequisite ID representing the customer/business.
  string business_id = 1;
}

message GetActiveSEOAddonsResponse {
  repeated AddonActivation active_addons = 1;
}

message StartSEOCategoryWorkflowRequest {
  repeated string business_ids = 1;
}

message GetDataForSEOCategoryRequest {
  // A prerequisite ID representing the customer/business
  string business_id = 1;
}

message GetDataForSEOCategoryResponse {
  string business_id = 1;
  string primary_category_id = 2;
  repeated string category_ids = 3;
  string task_id = 4;
  string raw_response = 5;
  google.protobuf.Timestamp created = 6;
  google.protobuf.Timestamp updated = 7;
  google.protobuf.Timestamp deleted = 8;
}
